import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'

// 测试工具类型
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[]
}

// 自定义渲染函数
const customRender = (
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { initialEntries = ['/'], ...renderOptions } = options

  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    return (
      <BrowserRouter>
        <ConfigProvider locale={zhCN}>
          {children}
        </ConfigProvider>
      </BrowserRouter>
    )
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock用户数据
export const mockUser = {
  _id: '507f1f77bcf86cd799439011',
  username: 'testuser',
  email: '<EMAIL>',
  avatar: 'https://example.com/avatar.jpg',
  isEmailVerified: true,
  subscription: {
    plan: 'pro',
    status: 'active',
    tokensUsed: 1000,
    tokensLimit: 10000,
  },
  createdAt: '2023-01-01T00:00:00.000Z',
  updatedAt: '2023-01-01T00:00:00.000Z',
}

// Mock数字人数据
export const mockCharacter = {
  _id: '507f1f77bcf86cd799439012',
  name: 'Test Character',
  description: 'A test character for testing',
  avatar: 'https://example.com/character-avatar.jpg',
  userId: mockUser._id,
  config: {
    personality: 'friendly',
    expertise: ['general', 'technology'],
    language: 'zh-CN',
    voice: {
      provider: 'azure',
      voiceId: 'zh-CN-XiaoxiaoNeural',
    },
    aiModel: {
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 2000,
      systemPrompt: 'You are a helpful assistant.',
    },
  },
  knowledgeBase: {
    enabled: false,
    vectorStoreId: '',
    documents: [],
  },
  isPublic: true,
  tags: ['AI', 'Assistant'],
  stats: {
    conversationCount: 10,
    messageCount: 100,
    lastUsedAt: '2023-01-01T00:00:00.000Z',
  },
  createdAt: '2023-01-01T00:00:00.000Z',
  updatedAt: '2023-01-01T00:00:00.000Z',
}

// Mock对话数据
export const mockConversation = {
  _id: '507f1f77bcf86cd799439013',
  userId: mockUser._id,
  characterId: mockCharacter._id,
  title: 'Test Conversation',
  messages: [
    {
      _id: '507f1f77bcf86cd799439014',
      role: 'user',
      content: 'Hello, how are you?',
      timestamp: '2023-01-01T00:00:00.000Z',
    },
    {
      _id: '507f1f77bcf86cd799439015',
      role: 'assistant',
      content: 'Hello! I am doing well, thank you for asking. How can I help you today?',
      timestamp: '2023-01-01T00:01:00.000Z',
      metadata: {
        tokens: 25,
        model: 'gpt-3.5-turbo',
        duration: 1200,
      },
    },
  ],
  status: 'active',
  createdAt: '2023-01-01T00:00:00.000Z',
  updatedAt: '2023-01-01T00:01:00.000Z',
}

// Mock知识库数据
export const mockKnowledge = {
  _id: '507f1f77bcf86cd799439016',
  name: 'Test Knowledge Base',
  description: 'A test knowledge base',
  userId: mockUser._id,
  documents: [
    {
      _id: '507f1f77bcf86cd799439017',
      name: 'test-document.pdf',
      originalName: 'test-document.pdf',
      type: 'pdf',
      size: 1024000,
      url: '/uploads/test-document.pdf',
      status: 'completed',
      chunks: 10,
      uploadedAt: '2023-01-01T00:00:00.000Z',
    },
  ],
  settings: {
    chunkSize: 1000,
    chunkOverlap: 200,
    embeddingModel: 'text-embedding-ada-002',
  },
  vectorStore: {
    provider: 'pinecone',
    indexId: 'test-index',
    dimensions: 1536,
    totalVectors: 10,
  },
  createdAt: '2023-01-01T00:00:00.000Z',
  updatedAt: '2023-01-01T00:00:00.000Z',
}

// Mock API响应
export const mockApiResponse = <T>(data: T, success = true) => ({
  success,
  data,
  message: success ? '操作成功' : '操作失败',
  timestamp: new Date().toISOString(),
})

// Mock分页响应
export const mockPaginatedResponse = <T>(items: T[], page = 1, limit = 20) => ({
  success: true,
  data: {
    items,
    total: items.length,
    page,
    limit,
    totalPages: Math.ceil(items.length / limit),
  },
  message: '获取数据成功',
  timestamp: new Date().toISOString(),
})

// 等待异步操作完成
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟用户输入
export const simulateUserInput = async (element: HTMLElement, value: string) => {
  const { fireEvent } = await import('@testing-library/react')
  
  fireEvent.change(element, { target: { value } })
  fireEvent.blur(element)
}

// 模拟文件上传
export const createMockFile = (name: string, size: number, type: string) => {
  const file = new File([''], name, { type })
  Object.defineProperty(file, 'size', { value: size })
  return file
}

// 重新导出所有测试工具
export * from '@testing-library/react'
export { customRender as render }
