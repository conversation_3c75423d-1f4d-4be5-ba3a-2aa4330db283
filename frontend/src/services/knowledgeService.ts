import { apiClient } from './api'
import type { 
  Knowledge, 
  Document, 
  ApiResponse, 
  PaginatedResponse,
  PaginationParams 
} from '@/types'

/**
 * 知识库服务类
 */
export class KnowledgeService {
  /**
   * 创建知识库
   */
  async createKnowledge(data: {
    name: string
    description?: string
    settings?: {
      chunkSize?: number
      chunkOverlap?: number
      embeddingModel?: string
    }
  }): Promise<ApiResponse<Knowledge>> {
    return await apiClient.post('/knowledge', data)
  }

  /**
   * 获取知识库列表
   */
  async getKnowledgeList(params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Knowledge>>> {
    const queryParams = new URLSearchParams()
    
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder)
    if (params?.search) queryParams.append('search', params.search)

    const url = `/knowledge${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return await apiClient.get(url)
  }

  /**
   * 获取知识库详情
   */
  async getKnowledge(knowledgeId: string): Promise<ApiResponse<Knowledge>> {
    return await apiClient.get(`/knowledge/${knowledgeId}`)
  }

  /**
   * 更新知识库
   */
  async updateKnowledge(
    knowledgeId: string,
    updates: {
      name?: string
      description?: string
      settings?: {
        chunkSize?: number
        chunkOverlap?: number
        embeddingModel?: string
      }
    }
  ): Promise<ApiResponse<Knowledge>> {
    return await apiClient.put(`/knowledge/${knowledgeId}`, updates)
  }

  /**
   * 删除知识库
   */
  async deleteKnowledge(knowledgeId: string): Promise<ApiResponse> {
    return await apiClient.delete(`/knowledge/${knowledgeId}`)
  }

  /**
   * 上传文档
   */
  async uploadDocument(
    knowledgeId: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<Document>> {
    return await apiClient.upload(
      `/knowledge/${knowledgeId}/documents`,
      file,
      onProgress
    )
  }

  /**
   * 删除文档
   */
  async deleteDocument(
    knowledgeId: string,
    documentId: string
  ): Promise<ApiResponse> {
    return await apiClient.delete(`/knowledge/${knowledgeId}/documents/${documentId}`)
  }

  /**
   * 批量删除文档
   */
  async batchDeleteDocuments(
    knowledgeId: string,
    documentIds: string[]
  ): Promise<ApiResponse<any>> {
    return await apiClient.delete(`/knowledge/${knowledgeId}/documents/batch`, {
      data: { documentIds }
    })
  }

  /**
   * 重新处理文档
   */
  async reprocessDocument(
    knowledgeId: string,
    documentId: string
  ): Promise<ApiResponse> {
    return await apiClient.post(`/knowledge/${knowledgeId}/documents/${documentId}/reprocess`)
  }

  /**
   * 搜索知识库
   */
  async searchKnowledge(
    knowledgeId: string,
    query: string,
    options?: {
      topK?: number
      scoreThreshold?: number
    }
  ): Promise<ApiResponse<{
    chunks: Array<{
      id: string
      content: string
      score: number
      metadata: any
    }>
    context: string
    sources: Array<{
      documentId: string
      documentName: string
      relevanceScore: number
    }>
    totalResults: number
  }>> {
    return await apiClient.post(`/knowledge/${knowledgeId}/search`, {
      query,
      ...options,
    })
  }

  /**
   * 获取知识库统计
   */
  async getKnowledgeStats(knowledgeId: string): Promise<ApiResponse<{
    totalDocuments: number
    completedDocuments: number
    processingDocuments: number
    failedDocuments: number
    totalSize: number
    totalChunks: number
    vectorStats?: any
    isReady: boolean
  }>> {
    return await apiClient.get(`/knowledge/${knowledgeId}/stats`)
  }

  /**
   * 获取文档处理进度
   */
  async getDocumentProgress(knowledgeId: string): Promise<ApiResponse<{
    total: number
    completed: number
    processing: number
    failed: number
    percentage: number
  }>> {
    return await apiClient.get(`/knowledge/${knowledgeId}/progress`)
  }

  /**
   * 获取支持的文档类型
   */
  async getSupportedDocumentTypes(): Promise<ApiResponse<Array<{
    type: string
    name: string
    maxSize: string
  }>>> {
    return await apiClient.get('/knowledge/document-types')
  }

  /**
   * 下载文档
   */
  async downloadDocument(
    knowledgeId: string,
    documentId: string,
    filename?: string
  ): Promise<void> {
    return await apiClient.download(
      `/knowledge/${knowledgeId}/documents/${documentId}/download`,
      filename
    )
  }

  /**
   * 获取文档预览
   */
  async getDocumentPreview(
    knowledgeId: string,
    documentId: string
  ): Promise<ApiResponse<{
    content: string
    chunks: Array<{
      index: number
      content: string
      tokens: number
    }>
  }>> {
    return await apiClient.get(`/knowledge/${knowledgeId}/documents/${documentId}/preview`)
  }

  /**
   * 搜索文档
   */
  async searchDocuments(
    knowledgeId: string,
    query: string,
    options?: {
      type?: string
      status?: string
      limit?: number
    }
  ): Promise<ApiResponse<Document[]>> {
    const queryParams = new URLSearchParams()
    queryParams.append('q', query)
    
    if (options?.type) queryParams.append('type', options.type)
    if (options?.status) queryParams.append('status', options.status)
    if (options?.limit) queryParams.append('limit', options.limit.toString())

    return await apiClient.get(`/knowledge/${knowledgeId}/documents/search?${queryParams.toString()}`)
  }

  /**
   * 获取知识库分析
   */
  async getKnowledgeAnalysis(knowledgeId: string): Promise<ApiResponse<{
    documentTypes: Array<{ type: string; count: number; percentage: number }>
    processingStatus: Array<{ status: string; count: number; percentage: number }>
    sizeDistribution: Array<{ range: string; count: number; percentage: number }>
    uploadTrend: Array<{ date: string; count: number }>
    topKeywords: Array<{ keyword: string; frequency: number }>
  }>> {
    return await apiClient.get(`/knowledge/${knowledgeId}/analysis`)
  }

  /**
   * 导出知识库
   */
  async exportKnowledge(
    knowledgeId: string,
    format: 'json' | 'csv' = 'json'
  ): Promise<void> {
    return await apiClient.download(
      `/knowledge/${knowledgeId}/export?format=${format}`,
      `knowledge-${knowledgeId}.${format}`
    )
  }

  /**
   * 导入知识库
   */
  async importKnowledge(file: File): Promise<ApiResponse<Knowledge>> {
    return await apiClient.upload('/knowledge/import', file)
  }

  /**
   * 克隆知识库
   */
  async cloneKnowledge(
    knowledgeId: string,
    newName: string
  ): Promise<ApiResponse<Knowledge>> {
    return await apiClient.post(`/knowledge/${knowledgeId}/clone`, { name: newName })
  }

  /**
   * 合并知识库
   */
  async mergeKnowledge(
    targetKnowledgeId: string,
    sourceKnowledgeIds: string[]
  ): Promise<ApiResponse<Knowledge>> {
    return await apiClient.post(`/knowledge/${targetKnowledgeId}/merge`, {
      sourceKnowledgeIds,
    })
  }

  /**
   * 获取知识库使用情况
   */
  async getKnowledgeUsage(knowledgeId: string): Promise<ApiResponse<{
    totalQueries: number
    averageResponseTime: number
    topQueries: Array<{ query: string; count: number }>
    usageByCharacter: Array<{ characterId: string; characterName: string; queryCount: number }>
    usageTrend: Array<{ date: string; queryCount: number }>
  }>> {
    return await apiClient.get(`/knowledge/${knowledgeId}/usage`)
  }

  /**
   * 优化知识库
   */
  async optimizeKnowledge(knowledgeId: string): Promise<ApiResponse<{
    optimizationReport: {
      duplicateChunks: number
      lowQualityChunks: number
      recommendedActions: string[]
    }
  }>> {
    return await apiClient.post(`/knowledge/${knowledgeId}/optimize`)
  }

  /**
   * 验证知识库
   */
  async validateKnowledge(knowledgeId: string): Promise<ApiResponse<{
    validationReport: {
      totalDocuments: number
      validDocuments: number
      invalidDocuments: number
      issues: Array<{
        documentId: string
        documentName: string
        issue: string
        severity: 'low' | 'medium' | 'high'
      }>
    }
  }>> {
    return await apiClient.get(`/knowledge/${knowledgeId}/validate`)
  }

  /**
   * 获取知识库备份
   */
  async backupKnowledge(knowledgeId: string): Promise<ApiResponse<{
    backupId: string
    downloadUrl: string
    expiresAt: string
  }>> {
    return await apiClient.post(`/knowledge/${knowledgeId}/backup`)
  }

  /**
   * 恢复知识库
   */
  async restoreKnowledge(
    knowledgeId: string,
    backupFile: File
  ): Promise<ApiResponse<Knowledge>> {
    return await apiClient.upload(`/knowledge/${knowledgeId}/restore`, backupFile)
  }
}

// 创建服务实例
export const knowledgeService = new KnowledgeService()

// 导出默认实例
export default knowledgeService
