import { apiClient } from './api'
import type { 
  Conversation, 
  Message, 
  ApiResponse, 
  PaginatedResponse,
  PaginationParams 
} from '@/types'

/**
 * 对话服务类
 */
export class ConversationService {
  /**
   * 创建对话
   */
  async createConversation(data: {
    characterId: string
    title?: string
  }): Promise<ApiResponse<Conversation>> {
    return await apiClient.post('/conversations', data)
  }

  /**
   * 获取对话列表
   */
  async getConversations(params?: PaginationParams & {
    status?: string
    characterId?: string
  }): Promise<ApiResponse<PaginatedResponse<Conversation>>> {
    const queryParams = new URLSearchParams()
    
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder)
    if (params?.search) queryParams.append('search', params.search)
    if (params?.status) queryParams.append('status', params.status)
    if (params?.characterId) queryParams.append('characterId', params.characterId)

    const url = `/conversations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return await apiClient.get(url)
  }

  /**
   * 获取对话详情
   */
  async getConversation(conversationId: string): Promise<ApiResponse<Conversation>> {
    return await apiClient.get(`/conversations/${conversationId}`)
  }

  /**
   * 发送消息
   */
  async sendMessage(
    conversationId: string,
    data: {
      content: string
      useRAG?: boolean
    }
  ): Promise<ApiResponse<{ userMessage: Message; assistantMessage?: Message }>> {
    return await apiClient.post(`/conversations/${conversationId}/messages`, data)
  }

  /**
   * 更新对话标题
   */
  async updateConversationTitle(
    conversationId: string,
    title: string
  ): Promise<ApiResponse<Conversation>> {
    return await apiClient.put(`/conversations/${conversationId}/title`, { title })
  }

  /**
   * 归档对话
   */
  async archiveConversation(conversationId: string): Promise<ApiResponse<Conversation>> {
    return await apiClient.post(`/conversations/${conversationId}/archive`)
  }

  /**
   * 恢复对话
   */
  async restoreConversation(conversationId: string): Promise<ApiResponse<Conversation>> {
    return await apiClient.post(`/conversations/${conversationId}/restore`)
  }

  /**
   * 删除对话
   */
  async deleteConversation(conversationId: string): Promise<ApiResponse> {
    return await apiClient.delete(`/conversations/${conversationId}`)
  }

  /**
   * 获取对话统计
   */
  async getConversationStats(): Promise<ApiResponse<any>> {
    return await apiClient.get('/conversations/stats')
  }

  /**
   * 搜索对话
   */
  async searchConversations(
    query: string,
    options?: {
      characterId?: string
      dateRange?: {
        start: string
        end: string
      }
      limit?: number
    }
  ): Promise<ApiResponse<Conversation[]>> {
    const queryParams = new URLSearchParams()
    queryParams.append('q', query)
    
    if (options?.characterId) queryParams.append('characterId', options.characterId)
    if (options?.dateRange) {
      queryParams.append('startDate', options.dateRange.start)
      queryParams.append('endDate', options.dateRange.end)
    }
    if (options?.limit) queryParams.append('limit', options.limit.toString())

    return await apiClient.get(`/conversations/search?${queryParams.toString()}`)
  }

  /**
   * 导出对话
   */
  async exportConversation(
    conversationId: string,
    format: 'json' | 'txt' | 'pdf' = 'json'
  ): Promise<void> {
    return await apiClient.download(
      `/conversations/${conversationId}/export?format=${format}`,
      `conversation-${conversationId}.${format}`
    )
  }

  /**
   * 批量删除对话
   */
  async batchDeleteConversations(conversationIds: string[]): Promise<ApiResponse<any>> {
    return await apiClient.post('/conversations/batch-delete', { conversationIds })
  }

  /**
   * 批量归档对话
   */
  async batchArchiveConversations(conversationIds: string[]): Promise<ApiResponse<any>> {
    return await apiClient.post('/conversations/batch-archive', { conversationIds })
  }

  /**
   * 获取对话摘要
   */
  async getConversationSummary(conversationId: string): Promise<ApiResponse<{
    summary: string
    keyPoints: string[]
    sentiment: 'positive' | 'neutral' | 'negative'
    topics: string[]
  }>> {
    return await apiClient.get(`/conversations/${conversationId}/summary`)
  }

  /**
   * 生成对话摘要
   */
  async generateConversationSummary(conversationId: string): Promise<ApiResponse<any>> {
    return await apiClient.post(`/conversations/${conversationId}/generate-summary`)
  }

  /**
   * 获取对话分析
   */
  async getConversationAnalysis(
    conversationId: string
  ): Promise<ApiResponse<{
    messageCount: number
    averageResponseTime: number
    userSatisfaction: number
    topicDistribution: Array<{ topic: string; percentage: number }>
    emotionAnalysis: Array<{ emotion: string; score: number }>
  }>> {
    return await apiClient.get(`/conversations/${conversationId}/analysis`)
  }

  /**
   * 获取最近对话
   */
  async getRecentConversations(limit = 10): Promise<ApiResponse<Conversation[]>> {
    return await apiClient.get(`/conversations/recent?limit=${limit}`)
  }

  /**
   * 获取活跃对话
   */
  async getActiveConversations(): Promise<ApiResponse<Conversation[]>> {
    return await apiClient.get('/conversations/active')
  }

  /**
   * 标记对话为重要
   */
  async markConversationAsImportant(conversationId: string): Promise<ApiResponse> {
    return await apiClient.post(`/conversations/${conversationId}/mark-important`)
  }

  /**
   * 取消标记对话为重要
   */
  async unmarkConversationAsImportant(conversationId: string): Promise<ApiResponse> {
    return await apiClient.delete(`/conversations/${conversationId}/mark-important`)
  }

  /**
   * 获取对话建议
   */
  async getConversationSuggestions(
    conversationId: string
  ): Promise<ApiResponse<string[]>> {
    return await apiClient.get(`/conversations/${conversationId}/suggestions`)
  }

  /**
   * 重新生成消息
   */
  async regenerateMessage(
    conversationId: string,
    messageId: string
  ): Promise<ApiResponse<Message>> {
    return await apiClient.post(`/conversations/${conversationId}/messages/${messageId}/regenerate`)
  }

  /**
   * 编辑消息
   */
  async editMessage(
    conversationId: string,
    messageId: string,
    content: string
  ): Promise<ApiResponse<Message>> {
    return await apiClient.put(`/conversations/${conversationId}/messages/${messageId}`, {
      content,
    })
  }

  /**
   * 删除消息
   */
  async deleteMessage(
    conversationId: string,
    messageId: string
  ): Promise<ApiResponse> {
    return await apiClient.delete(`/conversations/${conversationId}/messages/${messageId}`)
  }

  /**
   * 获取对话模板
   */
  async getConversationTemplates(): Promise<ApiResponse<Array<{
    id: string
    name: string
    description: string
    messages: Array<{ role: string; content: string }>
  }>>> {
    return await apiClient.get('/conversations/templates')
  }

  /**
   * 从模板创建对话
   */
  async createConversationFromTemplate(
    templateId: string,
    characterId: string
  ): Promise<ApiResponse<Conversation>> {
    return await apiClient.post('/conversations/from-template', {
      templateId,
      characterId,
    })
  }

  /**
   * 分享对话
   */
  async shareConversation(
    conversationId: string,
    options: {
      expiresAt?: string
      password?: string
      allowDownload?: boolean
    }
  ): Promise<ApiResponse<{ shareUrl: string; shareId: string }>> {
    return await apiClient.post(`/conversations/${conversationId}/share`, options)
  }

  /**
   * 获取分享的对话
   */
  async getSharedConversation(
    shareId: string,
    password?: string
  ): Promise<ApiResponse<Conversation>> {
    const data = password ? { password } : undefined
    return await apiClient.post(`/conversations/shared/${shareId}`, data)
  }
}

// 创建服务实例
export const conversationService = new ConversationService()

// 导出默认实例
export default conversationService
