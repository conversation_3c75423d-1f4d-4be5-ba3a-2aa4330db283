import { io, Socket } from 'socket.io-client'
import type { Message } from '@/types'

/**
 * WebSocket服务类
 */
export class SocketService {
  private socket: Socket | null = null
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private eventListeners: Map<string, Function[]> = new Map()

  /**
   * 连接WebSocket
   */
  connect(token: string): void {
    if (this.socket?.connected) {
      return
    }

    const wsUrl = import.meta.env.VITE_WS_URL || 'http://localhost:3001'
    
    this.socket = io(wsUrl, {
      auth: {
        token,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectDelay,
    })

    this.setupEventListeners()
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
      this.isConnected = false
      this.reconnectAttempts = 0
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.socket) return

    // 连接成功
    this.socket.on('connect', () => {
      console.log('WebSocket连接成功')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.emit('connected')
    })

    // 连接断开
    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket连接断开:', reason)
      this.isConnected = false
      this.emit('disconnected', reason)
    })

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.error('WebSocket连接错误:', error)
      this.reconnectAttempts++
      this.emit('error', error)
    })

    // 重连尝试
    this.socket.on('reconnect_attempt', (attemptNumber) => {
      console.log(`WebSocket重连尝试 ${attemptNumber}/${this.maxReconnectAttempts}`)
      this.emit('reconnecting', attemptNumber)
    })

    // 重连成功
    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`WebSocket重连成功，尝试次数: ${attemptNumber}`)
      this.isConnected = true
      this.reconnectAttempts = 0
      this.emit('reconnected', attemptNumber)
    })

    // 重连失败
    this.socket.on('reconnect_failed', () => {
      console.error('WebSocket重连失败')
      this.emit('reconnect_failed')
    })

    // 加入对话房间成功
    this.socket.on('joined_conversation', (data) => {
      console.log('加入对话房间成功:', data)
      this.emit('joined_conversation', data)
    })

    // 离开对话房间成功
    this.socket.on('left_conversation', (data) => {
      console.log('离开对话房间成功:', data)
      this.emit('left_conversation', data)
    })

    // 接收消息
    this.socket.on('message_received', (message: Message) => {
      console.log('收到消息:', message)
      this.emit('message_received', message)
    })

    // 接收流式消息
    this.socket.on('message_stream', (data: {
      conversationId: string
      content: string
      delta: string
      isComplete: boolean
    }) => {
      this.emit('message_stream', data)
    })

    // 打字状态开始
    this.socket.on('typing_start', (data: { userId: string }) => {
      this.emit('typing_start', data)
    })

    // 打字状态结束
    this.socket.on('typing_stop', (data: { userId: string }) => {
      this.emit('typing_stop', data)
    })

    // 服务器错误
    this.socket.on('error', (error: { message: string; code?: string; details?: any }) => {
      console.error('WebSocket服务器错误:', error)
      this.emit('server_error', error)
    })
  }

  /**
   * 加入对话房间
   */
  joinConversation(conversationId: string): void {
    if (!this.socket?.connected) {
      console.warn('WebSocket未连接，无法加入对话房间')
      return
    }

    this.socket.emit('join_conversation', { conversationId })
  }

  /**
   * 离开对话房间
   */
  leaveConversation(conversationId: string): void {
    if (!this.socket?.connected) {
      console.warn('WebSocket未连接，无法离开对话房间')
      return
    }

    this.socket.emit('leave_conversation', { conversationId })
  }

  /**
   * 发送消息
   */
  sendMessage(data: {
    conversationId: string
    content: string
    useRAG?: boolean
  }): void {
    if (!this.socket?.connected) {
      console.warn('WebSocket未连接，无法发送消息')
      return
    }

    this.socket.emit('send_message', data)
  }

  /**
   * 发送打字状态开始
   */
  startTyping(conversationId: string): void {
    if (!this.socket?.connected) {
      return
    }

    this.socket.emit('typing_start', { conversationId })
  }

  /**
   * 发送打字状态结束
   */
  stopTyping(conversationId: string): void {
    if (!this.socket?.connected) {
      return
    }

    this.socket.emit('typing_stop', { conversationId })
  }

  /**
   * 添加事件监听器
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(callback)
  }

  /**
   * 移除事件监听器
   */
  off(event: string, callback?: Function): void {
    if (!this.eventListeners.has(event)) {
      return
    }

    if (callback) {
      const listeners = this.eventListeners.get(event)!
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    } else {
      this.eventListeners.delete(event)
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件监听器执行错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 获取连接状态
   */
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true
  }

  /**
   * 获取连接ID
   */
  getSocketId(): string | undefined {
    return this.socket?.id
  }

  /**
   * 手动重连
   */
  reconnect(): void {
    if (this.socket) {
      this.socket.connect()
    }
  }

  /**
   * 设置重连配置
   */
  setReconnectConfig(options: {
    maxAttempts?: number
    delay?: number
  }): void {
    if (options.maxAttempts !== undefined) {
      this.maxReconnectAttempts = options.maxAttempts
    }
    if (options.delay !== undefined) {
      this.reconnectDelay = options.delay
    }

    if (this.socket) {
      this.socket.io.opts.reconnectionAttempts = this.maxReconnectAttempts
      this.socket.io.opts.reconnectionDelay = this.reconnectDelay
    }
  }

  /**
   * 获取网络延迟
   */
  async getPing(): Promise<number> {
    return new Promise((resolve, reject) => {
      if (!this.socket?.connected) {
        reject(new Error('WebSocket未连接'))
        return
      }

      const start = Date.now()
      this.socket.emit('ping', start, () => {
        const latency = Date.now() - start
        resolve(latency)
      })

      // 超时处理
      setTimeout(() => {
        reject(new Error('Ping超时'))
      }, 5000)
    })
  }

  /**
   * 发送自定义事件
   */
  emit(event: string, data?: any): void {
    if (!this.socket?.connected) {
      console.warn(`WebSocket未连接，无法发送事件: ${event}`)
      return
    }

    this.socket.emit(event, data)
  }

  /**
   * 监听自定义事件
   */
  listen(event: string, callback: Function): void {
    if (!this.socket) {
      console.warn(`WebSocket未初始化，无法监听事件: ${event}`)
      return
    }

    this.socket.on(event, callback)
  }

  /**
   * 取消监听自定义事件
   */
  unlisten(event: string, callback?: Function): void {
    if (!this.socket) {
      return
    }

    if (callback) {
      this.socket.off(event, callback)
    } else {
      this.socket.off(event)
    }
  }
}

// 创建全局实例
export const socketService = new SocketService()

// 导出默认实例
export default socketService
