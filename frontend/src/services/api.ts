import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { ApiResponse } from '@types/index'

// 创建axios实例
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 添加请求时间戳
      config.metadata = { startTime: Date.now() }
      
      // 从localStorage获取token
      const token = localStorage.getItem('auth-token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      
      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId()
      
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // 计算请求耗时
      const duration = Date.now() - response.config.metadata?.startTime
      console.log(`API请求耗时: ${duration}ms - ${response.config.method?.toUpperCase()} ${response.config.url}`)
      
      return response
    },
    async (error) => {
      const originalRequest = error.config
      
      // 处理401错误 - token过期
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true
        
        try {
          // 尝试刷新token
          const refreshResponse = await instance.post('/auth/refresh')
          const newToken = refreshResponse.data.data.token
          
          // 更新token
          localStorage.setItem('auth-token', newToken)
          originalRequest.headers.Authorization = `Bearer ${newToken}`
          
          // 重试原请求
          return instance(originalRequest)
        } catch (refreshError) {
          // 刷新失败，清除认证状态
          localStorage.removeItem('auth-token')
          window.location.href = '/login'
          return Promise.reject(refreshError)
        }
      }
      
      // 处理网络错误
      if (!error.response) {
        console.error('网络错误:', error.message)
        return Promise.reject({
          code: 'NETWORK_ERROR',
          message: '网络连接失败，请检查网络设置',
          details: error.message,
        })
      }
      
      // 处理服务器错误
      const errorData = error.response.data
      console.error('API错误:', errorData)
      
      return Promise.reject(errorData || {
        code: 'UNKNOWN_ERROR',
        message: '未知错误',
        details: error.message,
      })
    }
  )

  return instance
}

// 生成请求ID
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 创建API实例
export const api = createApiInstance()

// API客户端类
export class ApiClient {
  private instance: AxiosInstance

  constructor(instance: AxiosInstance) {
    this.instance = instance
  }

  // GET请求
  async get<T = any>(
    url: string, 
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.instance.get(url, config)
    return response.data
  }

  // POST请求
  async post<T = any>(
    url: string, 
    data?: any, 
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.instance.post(url, data, config)
    return response.data
  }

  // PUT请求
  async put<T = any>(
    url: string, 
    data?: any, 
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.instance.put(url, data, config)
    return response.data
  }

  // PATCH请求
  async patch<T = any>(
    url: string, 
    data?: any, 
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.instance.patch(url, data, config)
    return response.data
  }

  // DELETE请求
  async delete<T = any>(
    url: string, 
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.instance.delete(url, config)
    return response.data
  }

  // 文件上传
  async upload<T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.instance.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(progress)
        }
      },
    })

    return response.data
  }

  // 下载文件
  async download(
    url: string,
    filename?: string,
    config?: AxiosRequestConfig
  ): Promise<void> {
    const response = await this.instance.get(url, {
      ...config,
      responseType: 'blob',
    })

    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }

  // 设置认证token
  setAuthToken(token: string): void {
    this.instance.defaults.headers.common['Authorization'] = `Bearer ${token}`
    localStorage.setItem('auth-token', token)
  }

  // 清除认证token
  clearAuthToken(): void {
    delete this.instance.defaults.headers.common['Authorization']
    localStorage.removeItem('auth-token')
  }

  // 获取当前token
  getAuthToken(): string | null {
    return localStorage.getItem('auth-token')
  }
}

// 创建默认API客户端
export const apiClient = new ApiClient(api)

// 导出默认实例
export default apiClient
