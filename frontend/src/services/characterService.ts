import { apiClient } from './api'
import type { 
  Character, 
  CharacterForm, 
  ApiResponse, 
  PaginatedResponse,
  PaginationParams 
} from '@/types'

/**
 * 数字人服务类
 */
export class CharacterService {
  /**
   * 创建数字人
   */
  async createCharacter(characterData: CharacterForm): Promise<ApiResponse<Character>> {
    return await apiClient.post('/characters', characterData)
  }

  /**
   * 获取用户的数字人列表
   */
  async getUserCharacters(params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Character>>> {
    const queryParams = new URLSearchParams()
    
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder)
    if (params?.search) queryParams.append('search', params.search)

    const url = `/characters${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return await apiClient.get(url)
  }

  /**
   * 获取公开数字人列表
   */
  async getPublicCharacters(params?: PaginationParams & {
    tags?: string[]
    language?: string
  }): Promise<ApiResponse<PaginatedResponse<Character>>> {
    const queryParams = new URLSearchParams()
    
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder)
    if (params?.search) queryParams.append('search', params.search)
    if (params?.tags) params.tags.forEach(tag => queryParams.append('tags', tag))
    if (params?.language) queryParams.append('language', params.language)

    const url = `/characters/public${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return await apiClient.get(url)
  }

  /**
   * 获取数字人详情
   */
  async getCharacter(characterId: string): Promise<ApiResponse<Character>> {
    return await apiClient.get(`/characters/${characterId}`)
  }

  /**
   * 更新数字人
   */
  async updateCharacter(
    characterId: string, 
    updates: Partial<CharacterForm>
  ): Promise<ApiResponse<Character>> {
    return await apiClient.put(`/characters/${characterId}`, updates)
  }

  /**
   * 删除数字人
   */
  async deleteCharacter(characterId: string): Promise<ApiResponse> {
    return await apiClient.delete(`/characters/${characterId}`)
  }

  /**
   * 克隆数字人
   */
  async cloneCharacter(
    characterId: string, 
    newName?: string
  ): Promise<ApiResponse<Character>> {
    return await apiClient.post(`/characters/${characterId}/clone`, { name: newName })
  }

  /**
   * 获取数字人统计信息
   */
  async getCharacterStats(characterId: string): Promise<ApiResponse<any>> {
    return await apiClient.get(`/characters/${characterId}/stats`)
  }

  /**
   * 绑定知识库
   */
  async bindKnowledgeBase(
    characterId: string, 
    knowledgeId: string
  ): Promise<ApiResponse<Character>> {
    return await apiClient.post(`/characters/${characterId}/knowledge`, { knowledgeId })
  }

  /**
   * 解绑知识库
   */
  async unbindKnowledgeBase(characterId: string): Promise<ApiResponse<Character>> {
    return await apiClient.delete(`/characters/${characterId}/knowledge`)
  }

  /**
   * 获取可用的AI模型
   */
  async getAvailableModels(provider?: string): Promise<ApiResponse<any>> {
    const url = `/characters/models${provider ? `?provider=${provider}` : ''}`
    return await apiClient.get(url)
  }

  /**
   * 获取可用的语音选项
   */
  async getAvailableVoices(provider?: string): Promise<ApiResponse<any>> {
    const url = `/characters/voices${provider ? `?provider=${provider}` : ''}`
    return await apiClient.get(url)
  }

  /**
   * 测试数字人配置
   */
  async testCharacterConfig(config: {
    aiModel: any
    testMessage?: string
  }): Promise<ApiResponse<any>> {
    return await apiClient.post('/characters/test-config', config)
  }

  /**
   * 搜索数字人
   */
  async searchCharacters(
    query: string,
    options?: {
      includePublic?: boolean
      tags?: string[]
      language?: string
      limit?: number
    }
  ): Promise<ApiResponse<Character[]>> {
    const queryParams = new URLSearchParams()
    queryParams.append('q', query)
    
    if (options?.includePublic) queryParams.append('includePublic', 'true')
    if (options?.tags) options.tags.forEach(tag => queryParams.append('tags', tag))
    if (options?.language) queryParams.append('language', options.language)
    if (options?.limit) queryParams.append('limit', options.limit.toString())

    return await apiClient.get(`/characters/search?${queryParams.toString()}`)
  }

  /**
   * 获取数字人标签
   */
  async getCharacterTags(): Promise<ApiResponse<string[]>> {
    return await apiClient.get('/characters/tags')
  }

  /**
   * 获取推荐数字人
   */
  async getRecommendedCharacters(limit = 6): Promise<ApiResponse<Character[]>> {
    return await apiClient.get(`/characters/recommended?limit=${limit}`)
  }

  /**
   * 收藏数字人
   */
  async favoriteCharacter(characterId: string): Promise<ApiResponse> {
    return await apiClient.post(`/characters/${characterId}/favorite`)
  }

  /**
   * 取消收藏数字人
   */
  async unfavoriteCharacter(characterId: string): Promise<ApiResponse> {
    return await apiClient.delete(`/characters/${characterId}/favorite`)
  }

  /**
   * 获取收藏的数字人
   */
  async getFavoriteCharacters(params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Character>>> {
    const queryParams = new URLSearchParams()
    
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder)

    const url = `/characters/favorites${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return await apiClient.get(url)
  }

  /**
   * 举报数字人
   */
  async reportCharacter(
    characterId: string, 
    reason: string, 
    description?: string
  ): Promise<ApiResponse> {
    return await apiClient.post(`/characters/${characterId}/report`, {
      reason,
      description,
    })
  }

  /**
   * 获取数字人使用历史
   */
  async getCharacterHistory(
    characterId: string,
    params?: PaginationParams
  ): Promise<ApiResponse<any>> {
    const queryParams = new URLSearchParams()
    
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())

    const url = `/characters/${characterId}/history${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return await apiClient.get(url)
  }

  /**
   * 导出数字人配置
   */
  async exportCharacter(characterId: string): Promise<void> {
    return await apiClient.download(`/characters/${characterId}/export`, `character-${characterId}.json`)
  }

  /**
   * 导入数字人配置
   */
  async importCharacter(file: File): Promise<ApiResponse<Character>> {
    return await apiClient.upload('/characters/import', file)
  }

  /**
   * 批量删除数字人
   */
  async batchDeleteCharacters(characterIds: string[]): Promise<ApiResponse<any>> {
    return await apiClient.post('/characters/batch-delete', { characterIds })
  }

  /**
   * 获取数字人分析数据
   */
  async getCharacterAnalytics(
    characterId: string,
    timeRange: '7d' | '30d' | '90d' = '30d'
  ): Promise<ApiResponse<any>> {
    return await apiClient.get(`/characters/${characterId}/analytics?timeRange=${timeRange}`)
  }
}

// 创建服务实例
export const characterService = new CharacterService()

// 导出默认实例
export default characterService
