import type { ApiResponse, User, LoginForm, RegisterForm } from '@types/index'
import { apiClient } from './api'

export class AuthService {
  // 登录
  async login(credentials: LoginForm): Promise<ApiResponse<{ user: User; token: string }>> {
    return await apiClient.post('/api/auth/login', credentials)
  }

  // 注册
  async register(userData: RegisterForm): Promise<ApiResponse<{ user: User; token: string }>> {
    return await apiClient.post('/api/auth/register', userData)
  }

  // 登出
  async logout(): Promise<ApiResponse> {
    return await apiClient.post('/api/auth/logout')
  }

  // 刷新token
  async refreshToken(): Promise<ApiResponse<{ user: User; token: string }>> {
    return await apiClient.post('/api/auth/refresh')
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<ApiResponse<User>> {
    return await apiClient.get('/api/auth/profile')
  }

  // 更新用户信息
  async updateProfile(updates: Partial<User>): Promise<ApiResponse<User>> {
    return await apiClient.put('/api/auth/profile', updates)
  }

  // 修改密码
  async changePassword(data: {
    currentPassword: string
    newPassword: string
  }): Promise<ApiResponse> {
    return await apiClient.post('/api/auth/change-password', data)
  }

  // 忘记密码
  async forgotPassword(email: string): Promise<ApiResponse> {
    return await apiClient.post('/api/auth/forgot-password', { email })
  }

  // 重置密码
  async resetPassword(data: {
    token: string
    newPassword: string
  }): Promise<ApiResponse> {
    return await apiClient.post('/api/auth/reset-password', data)
  }

  // 验证邮箱
  async verifyEmail(token: string): Promise<ApiResponse> {
    return await apiClient.post('/api/auth/verify-email', { token })
  }

  // 重发验证邮件
  async resendVerificationEmail(): Promise<ApiResponse> {
    return await apiClient.post('/api/auth/resend-verification')
  }

  // 设置认证token
  setAuthToken(token: string): void {
    apiClient.setAuthToken(token)
  }

  // 清除认证token
  clearAuthToken(): void {
    apiClient.clearAuthToken()
  }

  // 检查token是否有效
  async validateToken(): Promise<boolean> {
    try {
      const response = await this.getCurrentUser()
      return response.success
    } catch {
      return false
    }
  }

  // 检查邮箱是否已存在
  async checkEmailExists(email: string): Promise<ApiResponse<{ exists: boolean }>> {
    return await apiClient.get(`/api/auth/check-email?email=${encodeURIComponent(email)}`)
  }

  // 检查用户名是否已存在
  async checkUsernameExists(username: string): Promise<ApiResponse<{ exists: boolean }>> {
    return await apiClient.get(`/api/auth/check-username?username=${encodeURIComponent(username)}`)
  }
}

// 创建服务实例
export const authService = new AuthService()

// 导出默认实例
export default authService
