// 基础类型定义

export interface BaseEntity {
  _id: string
  createdAt: string
  updatedAt: string
}

// 用户相关类型
export interface User extends BaseEntity {
  username: string
  email: string
  avatar?: string
  role: 'admin' | 'user'
  subscription: {
    plan: 'free' | 'pro' | 'enterprise'
    expiresAt: string
    tokensUsed: number
    tokensLimit: number
  }
  preferences: {
    language: string
    theme: 'light' | 'dark' | 'auto'
    notifications: {
      email: boolean
      browser: boolean
    }
  }
}

// 数字人相关类型
export interface Character extends BaseEntity {
  name: string
  description: string
  avatar?: string
  userId: string
  config: CharacterConfig
  knowledgeBase: KnowledgeBaseConfig
  isPublic: boolean
  tags: string[]
  stats: CharacterStats
}

export interface CharacterConfig {
  personality: string
  expertise: string[]
  language: string
  voice: VoiceConfig
  aiModel: AIModelConfig
}

export interface VoiceConfig {
  provider: 'azure' | 'aliyun' | 'local'
  voiceId: string
  speed: number
  pitch: number
}

export interface AIModelConfig {
  provider: 'openai' | 'anthropic' | 'local'
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
}

export interface KnowledgeBaseConfig {
  enabled: boolean
  vectorStoreId?: string
  documents: DocumentInfo[]
}

export interface DocumentInfo {
  id: string
  name: string
  type: string
  uploadedAt: string
}

export interface CharacterStats {
  conversationCount: number
  messageCount: number
  lastUsedAt: string
}

// 对话相关类型
export interface Conversation extends BaseEntity {
  userId: string
  characterId: string
  title: string
  messages: Message[]
  status: 'active' | 'archived' | 'deleted'
}

export interface Message {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  metadata?: MessageMetadata
}

export interface MessageMetadata {
  tokens?: number
  model?: string
  ragUsed?: boolean
  sources?: string[]
  duration?: number
}

// 知识库相关类型
export interface Knowledge extends BaseEntity {
  name: string
  description: string
  userId: string
  documents: Document[]
  vectorStore: VectorStoreConfig
  settings: KnowledgeSettings
}

export interface Document extends BaseEntity {
  name: string
  originalName: string
  type: 'pdf' | 'docx' | 'txt' | 'md' | 'rtf'
  size: number
  url: string
  status: 'processing' | 'completed' | 'failed'
  chunks: number
  uploadedAt: string
  processedAt?: string
}

export interface VectorStoreConfig {
  provider: 'pinecone' | 'weaviate'
  indexId: string
  dimensions: number
  totalVectors: number
}

export interface KnowledgeSettings {
  chunkSize: number
  chunkOverlap: number
  embeddingModel: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 表单类型
export interface LoginForm {
  email: string
  password: string
  remember?: boolean
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
}

export interface CharacterForm {
  name: string
  description: string
  avatar?: string
  personality: string
  expertise: string[]
  language: string
  aiModel: {
    provider: string
    model: string
    temperature: number
    maxTokens: number
    systemPrompt: string
  }
  voice: {
    provider: string
    voiceId: string
    speed: number
    pitch: number
  }
  tags: string[]
  isPublic: boolean
}

// WebSocket事件类型
export interface SocketEvents {
  // 连接事件
  connect: () => void
  disconnect: () => void
  
  // 对话事件
  join_conversation: (data: { conversationId: string; characterId: string }) => void
  leave_conversation: (data: { conversationId: string }) => void
  send_message: (data: { conversationId: string; content: string; type: 'text' | 'voice' }) => void
  
  // 接收事件
  message_received: (data: Message) => void
  message_stream: (data: { messageId: string; chunk: string; isComplete: boolean }) => void
  typing_start: (data: { userId: string; characterId: string }) => void
  typing_stop: (data: { userId: string; characterId: string }) => void
  error: (data: { message: string; code?: string }) => void
}

// 主题类型
export interface Theme {
  name: string
  colors: {
    primary: string
    secondary: string
    success: string
    warning: string
    error: string
    info: string
    text: {
      primary: string
      secondary: string
      disabled: string
    }
    background: {
      default: string
      secondary: string
      container: string
    }
    border: string
  }
}

// 应用配置类型
export interface AppConfig {
  apiUrl: string
  wsUrl: string
  appName: string
  version: string
  features: {
    voiceEnabled: boolean
    ragEnabled: boolean
    multiLanguage: boolean
  }
  limits: {
    maxFileSize: number
    allowedFileTypes: string[]
    maxCharacters: number
    maxConversations: number
  }
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: string
}

// 加载状态类型
export interface LoadingState {
  isLoading: boolean
  error?: AppError | null
}

// 分页参数类型
export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
  filters?: Record<string, any>
}
