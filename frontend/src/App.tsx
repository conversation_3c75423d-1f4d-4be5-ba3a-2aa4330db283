import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import { useAuthStore } from '@stores/authStore'
import { MainLayout } from '@components/layout/MainLayout'
import { LoginPage } from '@pages/auth/LoginPage'
import { RegisterPage } from '@pages/auth/RegisterPage'
import { DashboardPage } from '@pages/dashboard/DashboardPage'
import { CharactersPage } from '@pages/characters/CharactersPage'
import { ChatPage } from '@pages/chat/ChatPage'
import { KnowledgePage } from '@pages/knowledge/KnowledgePage'
import { SettingsPage } from '@pages/settings/SettingsPage'
import { NotFoundPage } from '@pages/common/NotFoundPage'
import { LoadingSpinner } from '@components/common/LoadingSpinner'

const { Content } = Layout

function App() {
  const { user, isLoading } = useAuthStore()

  // 显示加载状态
  if (isLoading) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <Content
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <LoadingSpinner size="large" tip="正在加载应用..." />
        </Content>
      </Layout>
    )
  }

  // 未登录用户显示认证页面
  if (!user) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <Content>
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </Content>
      </Layout>
    )
  }

  // 已登录用户显示主应用
  return (
    <MainLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<DashboardPage />} />
        <Route path="/characters" element={<CharactersPage />} />
        <Route path="/characters/:id" element={<CharactersPage />} />
        <Route path="/chat" element={<ChatPage />} />
        <Route path="/chat/:conversationId" element={<ChatPage />} />
        <Route path="/knowledge" element={<KnowledgePage />} />
        <Route path="/knowledge/:id" element={<KnowledgePage />} />
        <Route path="/settings" element={<SettingsPage />} />
        <Route path="/login" element={<Navigate to="/dashboard" replace />} />
        <Route path="/register" element={<Navigate to="/dashboard" replace />} />
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </MainLayout>
  )
}

export default App
