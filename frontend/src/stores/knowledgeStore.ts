import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import type { Knowledge, Document, PaginationParams } from '@/types'
import { knowledgeService } from '@/services/knowledgeService'

interface KnowledgeState {
  // 状态
  knowledgeList: Knowledge[]
  currentKnowledge: Knowledge | null
  isLoading: boolean
  error: string | null
  
  // 文档上传相关
  uploadProgress: { [key: string]: number }
  isUploading: boolean
  
  // 分页信息
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  
  // 搜索结果
  searchResults: any | null
  
  // 操作
  createKnowledge: (data: {
    name: string
    description?: string
    settings?: any
  }) => Promise<Knowledge>
  updateKnowledge: (knowledgeId: string, updates: any) => Promise<Knowledge>
  deleteKnowledge: (knowledgeId: string) => Promise<void>
  
  // 文档操作
  uploadDocument: (knowledgeId: string, file: File) => Promise<Document>
  deleteDocument: (knowledgeId: string, documentId: string) => Promise<void>
  batchDeleteDocuments: (knowledgeId: string, documentIds: string[]) => Promise<void>
  reprocessDocument: (knowledgeId: string, documentId: string) => Promise<void>
  
  // 数据获取
  fetchKnowledgeList: (params?: PaginationParams) => Promise<void>
  fetchKnowledge: (knowledgeId: string) => Promise<Knowledge>
  fetchKnowledgeStats: (knowledgeId: string) => Promise<any>
  fetchDocumentProgress: (knowledgeId: string) => Promise<any>
  
  // 搜索
  searchKnowledge: (knowledgeId: string, query: string, options?: any) => Promise<any>
  
  // 工具方法
  setCurrentKnowledge: (knowledge: Knowledge | null) => void
  setUploadProgress: (fileId: string, progress: number) => void
  clearUploadProgress: (fileId: string) => void
  clearSearchResults: () => void
  clearError: () => void
  resetState: () => void
}

const initialPagination = {
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0,
}

export const useKnowledgeStore = create<KnowledgeState>()(
  subscribeWithSelector((set, get) => ({
    // 初始状态
    knowledgeList: [],
    currentKnowledge: null,
    isLoading: false,
    error: null,
    uploadProgress: {},
    isUploading: false,
    pagination: initialPagination,
    searchResults: null,

    // 创建知识库
    createKnowledge: async (data: {
      name: string
      description?: string
      settings?: any
    }) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await knowledgeService.createKnowledge(data)
        
        if (response.success && response.data) {
          const newKnowledge = response.data
          
          set(state => ({
            knowledgeList: [newKnowledge, ...state.knowledgeList],
            currentKnowledge: newKnowledge,
            isLoading: false,
            error: null,
          }))
          
          return newKnowledge
        } else {
          throw new Error(response.error?.message || '创建知识库失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '创建知识库失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 更新知识库
    updateKnowledge: async (knowledgeId: string, updates: any) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await knowledgeService.updateKnowledge(knowledgeId, updates)
        
        if (response.success && response.data) {
          const updatedKnowledge = response.data
          
          set(state => ({
            knowledgeList: state.knowledgeList.map(kb => 
              kb._id === knowledgeId ? updatedKnowledge : kb
            ),
            currentKnowledge: state.currentKnowledge?._id === knowledgeId 
              ? updatedKnowledge 
              : state.currentKnowledge,
            isLoading: false,
            error: null,
          }))
          
          return updatedKnowledge
        } else {
          throw new Error(response.error?.message || '更新知识库失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '更新知识库失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 删除知识库
    deleteKnowledge: async (knowledgeId: string) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await knowledgeService.deleteKnowledge(knowledgeId)
        
        if (response.success) {
          set(state => ({
            knowledgeList: state.knowledgeList.filter(kb => kb._id !== knowledgeId),
            currentKnowledge: state.currentKnowledge?._id === knowledgeId 
              ? null 
              : state.currentKnowledge,
            isLoading: false,
            error: null,
          }))
        } else {
          throw new Error(response.error?.message || '删除知识库失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '删除知识库失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 上传文档
    uploadDocument: async (knowledgeId: string, file: File) => {
      try {
        set({ isUploading: true, error: null })
        
        const fileId = `${file.name}-${Date.now()}`
        
        const response = await knowledgeService.uploadDocument(
          knowledgeId,
          file,
          (progress) => {
            get().setUploadProgress(fileId, progress)
          }
        )
        
        if (response.success && response.data) {
          const newDocument = response.data
          
          // 更新当前知识库的文档列表
          set(state => {
            if (state.currentKnowledge?._id === knowledgeId) {
              return {
                currentKnowledge: {
                  ...state.currentKnowledge,
                  documents: [...state.currentKnowledge.documents, newDocument],
                },
                isUploading: false,
                error: null,
              }
            }
            return { isUploading: false, error: null }
          })
          
          // 清除上传进度
          get().clearUploadProgress(fileId)
          
          return newDocument
        } else {
          throw new Error(response.error?.message || '上传文档失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '上传文档失败'
        set({ isUploading: false, error: errorMessage })
        throw error
      }
    },

    // 删除文档
    deleteDocument: async (knowledgeId: string, documentId: string) => {
      try {
        const response = await knowledgeService.deleteDocument(knowledgeId, documentId)
        
        if (response.success) {
          // 更新当前知识库的文档列表
          set(state => {
            if (state.currentKnowledge?._id === knowledgeId) {
              return {
                currentKnowledge: {
                  ...state.currentKnowledge,
                  documents: state.currentKnowledge.documents.filter(
                    doc => doc._id !== documentId
                  ),
                },
              }
            }
            return state
          })
        } else {
          throw new Error(response.error?.message || '删除文档失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '删除文档失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 批量删除文档
    batchDeleteDocuments: async (knowledgeId: string, documentIds: string[]) => {
      try {
        const response = await knowledgeService.batchDeleteDocuments(knowledgeId, documentIds)
        
        if (response.success) {
          // 更新当前知识库的文档列表
          set(state => {
            if (state.currentKnowledge?._id === knowledgeId) {
              return {
                currentKnowledge: {
                  ...state.currentKnowledge,
                  documents: state.currentKnowledge.documents.filter(
                    doc => !documentIds.includes(doc._id)
                  ),
                },
              }
            }
            return state
          })
        } else {
          throw new Error(response.error?.message || '批量删除文档失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '批量删除文档失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 重新处理文档
    reprocessDocument: async (knowledgeId: string, documentId: string) => {
      try {
        const response = await knowledgeService.reprocessDocument(knowledgeId, documentId)
        
        if (response.success) {
          // 更新文档状态为处理中
          set(state => {
            if (state.currentKnowledge?._id === knowledgeId) {
              return {
                currentKnowledge: {
                  ...state.currentKnowledge,
                  documents: state.currentKnowledge.documents.map(doc =>
                    doc._id === documentId 
                      ? { ...doc, status: 'processing' }
                      : doc
                  ),
                },
              }
            }
            return state
          })
        } else {
          throw new Error(response.error?.message || '重新处理文档失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '重新处理文档失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 获取知识库列表
    fetchKnowledgeList: async (params?: PaginationParams) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await knowledgeService.getKnowledgeList(params)
        
        if (response.success && response.data) {
          const { items, total, page, limit, totalPages } = response.data
          
          set({
            knowledgeList: items,
            pagination: { page, limit, total, totalPages },
            isLoading: false,
            error: null,
          })
        } else {
          throw new Error(response.error?.message || '获取知识库列表失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取知识库列表失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 获取知识库详情
    fetchKnowledge: async (knowledgeId: string) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await knowledgeService.getKnowledge(knowledgeId)
        
        if (response.success && response.data) {
          const knowledge = response.data
          
          set({
            currentKnowledge: knowledge,
            isLoading: false,
            error: null,
          })
          
          return knowledge
        } else {
          throw new Error(response.error?.message || '获取知识库详情失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取知识库详情失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 获取知识库统计
    fetchKnowledgeStats: async (knowledgeId: string) => {
      try {
        const response = await knowledgeService.getKnowledgeStats(knowledgeId)
        
        if (response.success && response.data) {
          return response.data
        } else {
          throw new Error(response.error?.message || '获取统计信息失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取统计信息失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 获取文档处理进度
    fetchDocumentProgress: async (knowledgeId: string) => {
      try {
        const response = await knowledgeService.getDocumentProgress(knowledgeId)
        
        if (response.success && response.data) {
          return response.data
        } else {
          throw new Error(response.error?.message || '获取处理进度失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取处理进度失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 搜索知识库
    searchKnowledge: async (knowledgeId: string, query: string, options?: any) => {
      try {
        const response = await knowledgeService.searchKnowledge(knowledgeId, query, options)
        
        if (response.success && response.data) {
          set({ searchResults: response.data })
          return response.data
        } else {
          throw new Error(response.error?.message || '搜索失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '搜索失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 设置当前知识库
    setCurrentKnowledge: (knowledge: Knowledge | null) => {
      set({ currentKnowledge: knowledge })
    },

    // 设置上传进度
    setUploadProgress: (fileId: string, progress: number) => {
      set(state => ({
        uploadProgress: {
          ...state.uploadProgress,
          [fileId]: progress,
        },
      }))
    },

    // 清除上传进度
    clearUploadProgress: (fileId: string) => {
      set(state => {
        const newProgress = { ...state.uploadProgress }
        delete newProgress[fileId]
        return { uploadProgress: newProgress }
      })
    },

    // 清除搜索结果
    clearSearchResults: () => {
      set({ searchResults: null })
    },

    // 清除错误
    clearError: () => {
      set({ error: null })
    },

    // 重置状态
    resetState: () => {
      set({
        knowledgeList: [],
        currentKnowledge: null,
        isLoading: false,
        error: null,
        uploadProgress: {},
        isUploading: false,
        pagination: initialPagination,
        searchResults: null,
      })
    },
  }))
)

// 导出选择器
export const useKnowledgeList = () => useKnowledgeStore(state => state.knowledgeList)
export const useCurrentKnowledge = () => useKnowledgeStore(state => state.currentKnowledge)
export const useKnowledgeLoading = () => useKnowledgeStore(state => state.isLoading)
export const useKnowledgeError = () => useKnowledgeStore(state => state.error)
export const useUploadProgress = () => useKnowledgeStore(state => state.uploadProgress)
export const useIsUploading = () => useKnowledgeStore(state => state.isUploading)
export const useKnowledgePagination = () => useKnowledgeStore(state => state.pagination)
export const useSearchResults = () => useKnowledgeStore(state => state.searchResults)
