import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import type { Conversation, Message, PaginationParams } from '@/types'
import { conversationService } from '@/services/conversationService'
import { socketService } from '@/services/socketService'

interface ConversationState {
  // 状态
  conversations: Conversation[]
  currentConversation: Conversation | null
  isLoading: boolean
  error: string | null
  
  // 消息相关
  isTyping: boolean
  typingUsers: string[]
  streamingMessage: {
    content: string
    isComplete: boolean
  } | null
  
  // 分页信息
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  
  // 操作
  createConversation: (data: { characterId: string; title?: string }) => Promise<Conversation>
  sendMessage: (conversationId: string, content: string, useRAG?: boolean) => Promise<void>
  updateConversationTitle: (conversationId: string, title: string) => Promise<void>
  deleteConversation: (conversationId: string) => Promise<void>
  archiveConversation: (conversationId: string) => Promise<void>
  restoreConversation: (conversationId: string) => Promise<void>
  
  // 数据获取
  fetchConversations: (params?: PaginationParams) => Promise<void>
  fetchConversation: (conversationId: string) => Promise<Conversation>
  
  // WebSocket相关
  joinConversation: (conversationId: string) => void
  leaveConversation: (conversationId: string) => void
  startTyping: (conversationId: string) => void
  stopTyping: (conversationId: string) => void
  
  // 工具方法
  setCurrentConversation: (conversation: Conversation | null) => void
  addMessage: (message: Message) => void
  updateStreamingMessage: (content: string, isComplete: boolean) => void
  clearStreamingMessage: () => void
  setTypingUsers: (users: string[]) => void
  clearError: () => void
  resetState: () => void
}

const initialPagination = {
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0,
}

export const useConversationStore = create<ConversationState>()(
  subscribeWithSelector((set, get) => ({
    // 初始状态
    conversations: [],
    currentConversation: null,
    isLoading: false,
    error: null,
    isTyping: false,
    typingUsers: [],
    streamingMessage: null,
    pagination: initialPagination,

    // 创建对话
    createConversation: async (data: { characterId: string; title?: string }) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await conversationService.createConversation(data)
        
        if (response.success && response.data) {
          const newConversation = response.data
          
          set(state => ({
            conversations: [newConversation, ...state.conversations],
            currentConversation: newConversation,
            isLoading: false,
            error: null,
          }))
          
          return newConversation
        } else {
          throw new Error(response.error?.message || '创建对话失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '创建对话失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 发送消息
    sendMessage: async (conversationId: string, content: string, useRAG = false) => {
      try {
        // 通过WebSocket发送消息
        socketService.sendMessage({
          conversationId,
          content,
          useRAG,
        })
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '发送消息失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 更新对话标题
    updateConversationTitle: async (conversationId: string, title: string) => {
      try {
        const response = await conversationService.updateConversationTitle(conversationId, title)
        
        if (response.success && response.data) {
          const updatedConversation = response.data
          
          set(state => ({
            conversations: state.conversations.map(conv => 
              conv._id === conversationId ? updatedConversation : conv
            ),
            currentConversation: state.currentConversation?._id === conversationId 
              ? updatedConversation 
              : state.currentConversation,
          }))
        } else {
          throw new Error(response.error?.message || '更新标题失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '更新标题失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 删除对话
    deleteConversation: async (conversationId: string) => {
      try {
        const response = await conversationService.deleteConversation(conversationId)
        
        if (response.success) {
          set(state => ({
            conversations: state.conversations.filter(conv => conv._id !== conversationId),
            currentConversation: state.currentConversation?._id === conversationId 
              ? null 
              : state.currentConversation,
          }))
        } else {
          throw new Error(response.error?.message || '删除对话失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '删除对话失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 归档对话
    archiveConversation: async (conversationId: string) => {
      try {
        const response = await conversationService.archiveConversation(conversationId)
        
        if (response.success && response.data) {
          const updatedConversation = response.data
          
          set(state => ({
            conversations: state.conversations.map(conv => 
              conv._id === conversationId ? updatedConversation : conv
            ),
          }))
        } else {
          throw new Error(response.error?.message || '归档对话失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '归档对话失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 恢复对话
    restoreConversation: async (conversationId: string) => {
      try {
        const response = await conversationService.restoreConversation(conversationId)
        
        if (response.success && response.data) {
          const updatedConversation = response.data
          
          set(state => ({
            conversations: state.conversations.map(conv => 
              conv._id === conversationId ? updatedConversation : conv
            ),
          }))
        } else {
          throw new Error(response.error?.message || '恢复对话失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '恢复对话失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 获取对话列表
    fetchConversations: async (params?: PaginationParams) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await conversationService.getConversations(params)
        
        if (response.success && response.data) {
          const { items, total, page, limit, totalPages } = response.data
          
          set({
            conversations: items,
            pagination: { page, limit, total, totalPages },
            isLoading: false,
            error: null,
          })
        } else {
          throw new Error(response.error?.message || '获取对话列表失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取对话列表失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 获取对话详情
    fetchConversation: async (conversationId: string) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await conversationService.getConversation(conversationId)
        
        if (response.success && response.data) {
          const conversation = response.data
          
          set({
            currentConversation: conversation,
            isLoading: false,
            error: null,
          })
          
          return conversation
        } else {
          throw new Error(response.error?.message || '获取对话详情失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取对话详情失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 加入对话房间
    joinConversation: (conversationId: string) => {
      socketService.joinConversation(conversationId)
    },

    // 离开对话房间
    leaveConversation: (conversationId: string) => {
      socketService.leaveConversation(conversationId)
    },

    // 开始打字
    startTyping: (conversationId: string) => {
      set({ isTyping: true })
      socketService.startTyping(conversationId)
    },

    // 停止打字
    stopTyping: (conversationId: string) => {
      set({ isTyping: false })
      socketService.stopTyping(conversationId)
    },

    // 设置当前对话
    setCurrentConversation: (conversation: Conversation | null) => {
      set({ currentConversation: conversation })
    },

    // 添加消息
    addMessage: (message: Message) => {
      set(state => {
        if (!state.currentConversation) return state
        
        const updatedConversation = {
          ...state.currentConversation,
          messages: [...state.currentConversation.messages, message],
        }
        
        return {
          currentConversation: updatedConversation,
          conversations: state.conversations.map(conv => 
            conv._id === updatedConversation._id ? updatedConversation : conv
          ),
        }
      })
    },

    // 更新流式消息
    updateStreamingMessage: (content: string, isComplete: boolean) => {
      set({
        streamingMessage: { content, isComplete }
      })
      
      if (isComplete) {
        // 流式消息完成后清除
        setTimeout(() => {
          set({ streamingMessage: null })
        }, 100)
      }
    },

    // 清除流式消息
    clearStreamingMessage: () => {
      set({ streamingMessage: null })
    },

    // 设置正在打字的用户
    setTypingUsers: (users: string[]) => {
      set({ typingUsers: users })
    },

    // 清除错误
    clearError: () => {
      set({ error: null })
    },

    // 重置状态
    resetState: () => {
      set({
        conversations: [],
        currentConversation: null,
        isLoading: false,
        error: null,
        isTyping: false,
        typingUsers: [],
        streamingMessage: null,
        pagination: initialPagination,
      })
    },
  }))
)

// 设置WebSocket事件监听
socketService.on('message_received', (message: Message) => {
  useConversationStore.getState().addMessage(message)
})

socketService.on('message_stream', (data: {
  conversationId: string
  content: string
  delta: string
  isComplete: boolean
}) => {
  useConversationStore.getState().updateStreamingMessage(data.content, data.isComplete)
})

socketService.on('typing_start', (data: { userId: string }) => {
  const state = useConversationStore.getState()
  const newTypingUsers = [...state.typingUsers, data.userId].filter((user, index, arr) => 
    arr.indexOf(user) === index
  )
  state.setTypingUsers(newTypingUsers)
})

socketService.on('typing_stop', (data: { userId: string }) => {
  const state = useConversationStore.getState()
  const newTypingUsers = state.typingUsers.filter(user => user !== data.userId)
  state.setTypingUsers(newTypingUsers)
})

// 导出选择器
export const useConversations = () => useConversationStore(state => state.conversations)
export const useCurrentConversation = () => useConversationStore(state => state.currentConversation)
export const useConversationLoading = () => useConversationStore(state => state.isLoading)
export const useConversationError = () => useConversationStore(state => state.error)
export const useIsTyping = () => useConversationStore(state => state.isTyping)
export const useTypingUsers = () => useConversationStore(state => state.typingUsers)
export const useStreamingMessage = () => useConversationStore(state => state.streamingMessage)
export const useConversationPagination = () => useConversationStore(state => state.pagination)
