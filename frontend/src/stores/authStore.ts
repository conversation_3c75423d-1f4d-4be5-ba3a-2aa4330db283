import { create } from 'zustand'
import { persist, subscribeWithSelector } from 'zustand/middleware'
import type { User, LoginForm, RegisterForm } from '@types/index'
import { authService } from '@services/authService'

interface AuthState {
  // 状态
  user: User | null
  token: string | null
  isLoading: boolean
  error: string | null
  
  // 操作
  login: (credentials: LoginForm) => Promise<void>
  register: (userData: RegisterForm) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<void>
  clearError: () => void
  
  // 内部方法
  setUser: (user: User | null) => void
  setToken: (token: string | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

export const useAuthStore = create<AuthState>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        // 初始状态
        user: null,
        token: null,
        isLoading: false,
        error: null,

        // 登录
        login: async (credentials: LoginForm) => {
          try {
            set({ isLoading: true, error: null })
            
            const response = await authService.login(credentials)
            
            if (response.success && response.data) {
              const { user, token } = response.data
              set({ 
                user, 
                token, 
                isLoading: false,
                error: null 
              })
              
              // 设置axios默认header
              authService.setAuthToken(token)
            } else {
              throw new Error(response.error?.message || '登录失败')
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '登录失败'
            set({ 
              isLoading: false, 
              error: errorMessage,
              user: null,
              token: null
            })
            throw error
          }
        },

        // 注册
        register: async (userData: RegisterForm) => {
          try {
            set({ isLoading: true, error: null })
            
            const response = await authService.register(userData)
            
            if (response.success && response.data) {
              const { user, token } = response.data
              set({ 
                user, 
                token, 
                isLoading: false,
                error: null 
              })
              
              // 设置axios默认header
              authService.setAuthToken(token)
            } else {
              throw new Error(response.error?.message || '注册失败')
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '注册失败'
            set({ 
              isLoading: false, 
              error: errorMessage,
              user: null,
              token: null
            })
            throw error
          }
        },

        // 登出
        logout: () => {
          set({ 
            user: null, 
            token: null, 
            error: null 
          })
          
          // 清除axios默认header
          authService.clearAuthToken()
          
          // 清除本地存储
          localStorage.removeItem('auth-storage')
        },

        // 刷新token
        refreshToken: async () => {
          try {
            const { token } = get()
            if (!token) {
              throw new Error('没有有效的token')
            }

            const response = await authService.refreshToken()
            
            if (response.success && response.data) {
              const { token: newToken, user } = response.data
              set({ 
                token: newToken, 
                user,
                error: null 
              })
              
              // 更新axios默认header
              authService.setAuthToken(newToken)
            } else {
              throw new Error(response.error?.message || 'Token刷新失败')
            }
          } catch (error) {
            // Token刷新失败，清除认证状态
            get().logout()
            throw error
          }
        },

        // 更新用户信息
        updateProfile: async (updates: Partial<User>) => {
          try {
            set({ isLoading: true, error: null })
            
            const response = await authService.updateProfile(updates)
            
            if (response.success && response.data) {
              set({ 
                user: response.data, 
                isLoading: false,
                error: null 
              })
            } else {
              throw new Error(response.error?.message || '更新失败')
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '更新失败'
            set({ 
              isLoading: false, 
              error: errorMessage 
            })
            throw error
          }
        },

        // 清除错误
        clearError: () => {
          set({ error: null })
        },

        // 内部方法
        setUser: (user: User | null) => set({ user }),
        setToken: (token: string | null) => set({ token }),
        setLoading: (loading: boolean) => set({ isLoading: loading }),
        setError: (error: string | null) => set({ error }),
      }),
      {
        name: 'auth-storage',
        partialize: (state) => ({
          user: state.user,
          token: state.token,
        }),
        onRehydrateStorage: () => (state) => {
          // 恢复状态后设置axios默认header
          if (state?.token) {
            authService.setAuthToken(state.token)
          }
        },
      }
    )
  )
)

// 订阅token变化，自动设置axios header
useAuthStore.subscribe(
  (state) => state.token,
  (token) => {
    if (token) {
      authService.setAuthToken(token)
    } else {
      authService.clearAuthToken()
    }
  }
)

// 导出选择器
export const useUser = () => useAuthStore((state) => state.user)
export const useIsAuthenticated = () => useAuthStore((state) => !!state.user)
export const useAuthLoading = () => useAuthStore((state) => state.isLoading)
export const useAuthError = () => useAuthStore((state) => state.error)
