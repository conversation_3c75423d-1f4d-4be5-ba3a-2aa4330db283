// 导出所有store
export * from './authStore'
export * from './characterStore'
export * from './conversationStore'
export * from './knowledgeStore'

// 导出常用的选择器
export {
  useUser,
  useIsAuthenticated,
  useAuthLoading,
  useAuthError,
} from './authStore'

export {
  useCharacters,
  usePublicCharacters,
  useCurrentCharacter,
  useCharacterLoading,
  useCharacterError,
  useCharacterPagination,
  useCharacterFilters,
} from './characterStore'

export {
  useConversations,
  useCurrentConversation,
  useConversationLoading,
  useConversationError,
  useIsTyping,
  useTypingUsers,
  useStreamingMessage,
  useConversationPagination,
} from './conversationStore'

export {
  useKnowledgeList,
  useCurrentKnowledge,
  useKnowledgeLoading,
  useKnowledgeError,
  useUploadProgress,
  useIsUploading,
  useKnowledgePagination,
  useSearchResults,
} from './knowledgeStore'
