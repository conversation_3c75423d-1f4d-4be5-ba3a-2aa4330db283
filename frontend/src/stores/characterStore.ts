import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import type { Character, CharacterForm, PaginationParams } from '@/types'
import { characterService } from '@/services/characterService'

interface CharacterState {
  // 状态
  characters: Character[]
  publicCharacters: Character[]
  currentCharacter: Character | null
  isLoading: boolean
  error: string | null
  
  // 分页信息
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  
  // 过滤和搜索
  filters: {
    search: string
    tags: string[]
    language: string
    sortBy: string
    sortOrder: 'asc' | 'desc'
  }
  
  // 操作
  createCharacter: (characterData: CharacterForm) => Promise<Character>
  updateCharacter: (characterId: string, updates: Partial<CharacterForm>) => Promise<Character>
  deleteCharacter: (characterId: string) => Promise<void>
  cloneCharacter: (characterId: string, newName?: string) => Promise<Character>
  
  // 数据获取
  fetchUserCharacters: (params?: PaginationParams) => Promise<void>
  fetchPublicCharacters: (params?: PaginationParams) => Promise<void>
  fetchCharacter: (characterId: string) => Promise<Character>
  searchCharacters: (query: string, options?: any) => Promise<Character[]>
  
  // 知识库操作
  bindKnowledgeBase: (characterId: string, knowledgeId: string) => Promise<void>
  unbindKnowledgeBase: (characterId: string) => Promise<void>
  
  // 统计信息
  fetchCharacterStats: (characterId: string) => Promise<any>
  
  // 工具方法
  setCurrentCharacter: (character: Character | null) => void
  setFilters: (filters: Partial<CharacterState['filters']>) => void
  clearError: () => void
  resetState: () => void
}

const initialFilters = {
  search: '',
  tags: [],
  language: '',
  sortBy: 'stats.lastUsedAt',
  sortOrder: 'desc' as const,
}

const initialPagination = {
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0,
}

export const useCharacterStore = create<CharacterState>()(
  subscribeWithSelector((set, get) => ({
    // 初始状态
    characters: [],
    publicCharacters: [],
    currentCharacter: null,
    isLoading: false,
    error: null,
    pagination: initialPagination,
    filters: initialFilters,

    // 创建数字人
    createCharacter: async (characterData: CharacterForm) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await characterService.createCharacter(characterData)
        
        if (response.success && response.data) {
          const newCharacter = response.data
          
          set(state => ({
            characters: [newCharacter, ...state.characters],
            isLoading: false,
            error: null,
          }))
          
          return newCharacter
        } else {
          throw new Error(response.error?.message || '创建数字人失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '创建数字人失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 更新数字人
    updateCharacter: async (characterId: string, updates: Partial<CharacterForm>) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await characterService.updateCharacter(characterId, updates)
        
        if (response.success && response.data) {
          const updatedCharacter = response.data
          
          set(state => ({
            characters: state.characters.map(char => 
              char._id === characterId ? updatedCharacter : char
            ),
            currentCharacter: state.currentCharacter?._id === characterId 
              ? updatedCharacter 
              : state.currentCharacter,
            isLoading: false,
            error: null,
          }))
          
          return updatedCharacter
        } else {
          throw new Error(response.error?.message || '更新数字人失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '更新数字人失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 删除数字人
    deleteCharacter: async (characterId: string) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await characterService.deleteCharacter(characterId)
        
        if (response.success) {
          set(state => ({
            characters: state.characters.filter(char => char._id !== characterId),
            currentCharacter: state.currentCharacter?._id === characterId 
              ? null 
              : state.currentCharacter,
            isLoading: false,
            error: null,
          }))
        } else {
          throw new Error(response.error?.message || '删除数字人失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '删除数字人失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 克隆数字人
    cloneCharacter: async (characterId: string, newName?: string) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await characterService.cloneCharacter(characterId, newName)
        
        if (response.success && response.data) {
          const clonedCharacter = response.data
          
          set(state => ({
            characters: [clonedCharacter, ...state.characters],
            isLoading: false,
            error: null,
          }))
          
          return clonedCharacter
        } else {
          throw new Error(response.error?.message || '克隆数字人失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '克隆数字人失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 获取用户数字人列表
    fetchUserCharacters: async (params?: PaginationParams) => {
      try {
        set({ isLoading: true, error: null })
        
        const { filters } = get()
        const requestParams = {
          ...filters,
          ...params,
        }
        
        const response = await characterService.getUserCharacters(requestParams)
        
        if (response.success && response.data) {
          const { items, total, page, limit, totalPages } = response.data
          
          set({
            characters: items,
            pagination: { page, limit, total, totalPages },
            isLoading: false,
            error: null,
          })
        } else {
          throw new Error(response.error?.message || '获取数字人列表失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取数字人列表失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 获取公开数字人列表
    fetchPublicCharacters: async (params?: PaginationParams) => {
      try {
        set({ isLoading: true, error: null })
        
        const { filters } = get()
        const requestParams = {
          ...filters,
          ...params,
        }
        
        const response = await characterService.getPublicCharacters(requestParams)
        
        if (response.success && response.data) {
          const { items } = response.data
          
          set({
            publicCharacters: items,
            isLoading: false,
            error: null,
          })
        } else {
          throw new Error(response.error?.message || '获取公开数字人列表失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取公开数字人列表失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 获取数字人详情
    fetchCharacter: async (characterId: string) => {
      try {
        set({ isLoading: true, error: null })
        
        const response = await characterService.getCharacter(characterId)
        
        if (response.success && response.data) {
          const character = response.data
          
          set({
            currentCharacter: character,
            isLoading: false,
            error: null,
          })
          
          return character
        } else {
          throw new Error(response.error?.message || '获取数字人详情失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取数字人详情失败'
        set({ isLoading: false, error: errorMessage })
        throw error
      }
    },

    // 搜索数字人
    searchCharacters: async (query: string, options?: any) => {
      try {
        const response = await characterService.searchCharacters(query, options)
        
        if (response.success && response.data) {
          return response.data
        } else {
          throw new Error(response.error?.message || '搜索数字人失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '搜索数字人失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 绑定知识库
    bindKnowledgeBase: async (characterId: string, knowledgeId: string) => {
      try {
        const response = await characterService.bindKnowledgeBase(characterId, knowledgeId)
        
        if (response.success && response.data) {
          const updatedCharacter = response.data
          
          set(state => ({
            characters: state.characters.map(char => 
              char._id === characterId ? updatedCharacter : char
            ),
            currentCharacter: state.currentCharacter?._id === characterId 
              ? updatedCharacter 
              : state.currentCharacter,
          }))
        } else {
          throw new Error(response.error?.message || '绑定知识库失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '绑定知识库失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 解绑知识库
    unbindKnowledgeBase: async (characterId: string) => {
      try {
        const response = await characterService.unbindKnowledgeBase(characterId)
        
        if (response.success && response.data) {
          const updatedCharacter = response.data
          
          set(state => ({
            characters: state.characters.map(char => 
              char._id === characterId ? updatedCharacter : char
            ),
            currentCharacter: state.currentCharacter?._id === characterId 
              ? updatedCharacter 
              : state.currentCharacter,
          }))
        } else {
          throw new Error(response.error?.message || '解绑知识库失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '解绑知识库失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 获取数字人统计
    fetchCharacterStats: async (characterId: string) => {
      try {
        const response = await characterService.getCharacterStats(characterId)
        
        if (response.success && response.data) {
          return response.data
        } else {
          throw new Error(response.error?.message || '获取统计信息失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取统计信息失败'
        set({ error: errorMessage })
        throw error
      }
    },

    // 设置当前数字人
    setCurrentCharacter: (character: Character | null) => {
      set({ currentCharacter: character })
    },

    // 设置过滤条件
    setFilters: (newFilters: Partial<CharacterState['filters']>) => {
      set(state => ({
        filters: { ...state.filters, ...newFilters }
      }))
    },

    // 清除错误
    clearError: () => {
      set({ error: null })
    },

    // 重置状态
    resetState: () => {
      set({
        characters: [],
        publicCharacters: [],
        currentCharacter: null,
        isLoading: false,
        error: null,
        pagination: initialPagination,
        filters: initialFilters,
      })
    },
  }))
)

// 导出选择器
export const useCharacters = () => useCharacterStore(state => state.characters)
export const usePublicCharacters = () => useCharacterStore(state => state.publicCharacters)
export const useCurrentCharacter = () => useCharacterStore(state => state.currentCharacter)
export const useCharacterLoading = () => useCharacterStore(state => state.isLoading)
export const useCharacterError = () => useCharacterStore(state => state.error)
export const useCharacterPagination = () => useCharacterStore(state => state.pagination)
export const useCharacterFilters = () => useCharacterStore(state => state.filters)
