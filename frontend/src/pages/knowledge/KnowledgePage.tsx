import { useState, useEffect } from 'react'
import {
  Typo<PERSON>,
  Button,
  Card,
  Table,
  Space,
  Tag,
  Progress,
  Modal,
  Form,
  Input,
  Upload,
  message,
  Popconfirm,
  Tooltip,
  Row,
  Col,
  Statistic,
  Empty,
  Tabs
} from 'antd'
import {
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileMarkdownOutlined,
  PlusOutlined,
  SearchOutlined,
  DownloadOutlined
} from '@ant-design/icons'
import { useKnowledgeStore } from '@stores/knowledgeStore'
import type { Knowledge, Document } from '@types/index'
import type { ColumnsType } from 'antd/es/table'
import type { UploadFile } from 'antd/es/upload/interface'

const { Title, Text } = Typography
const { TextArea } = Input
const { TabPane } = Tabs

export const KnowledgePage: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)
  const [selectedKnowledge, setSelectedKnowledge] = useState<Knowledge | null>(null)
  const [form] = Form.useForm()
  const [fileList, setFileList] = useState<UploadFile[]>([])

  const {
    knowledgeBases,
    documents,
    isLoading,
    fetchKnowledgeBases,
    createKnowledgeBase,
    deleteKnowledgeBase,
    uploadDocuments,
    deleteDocument
  } = useKnowledgeStore()

  // 组件挂载时获取知识库列表
  useEffect(() => {
    fetchKnowledgeBases()
  }, [fetchKnowledgeBases])

  // 创建知识库
  const handleCreateKnowledge = async (values: any) => {
    try {
      await createKnowledgeBase(values)
      message.success('知识库创建成功！')
      setIsModalOpen(false)
      form.resetFields()
    } catch (error) {
      message.error(error instanceof Error ? error.message : '创建失败')
    }
  }

  // 删除知识库
  const handleDeleteKnowledge = async (id: string) => {
    try {
      await deleteKnowledgeBase(id)
      message.success('知识库删除成功！')
    } catch (error) {
      message.error(error instanceof Error ? error.message : '删除失败')
    }
  }

  // 上传文档
  const handleUploadDocuments = async () => {
    if (!selectedKnowledge || fileList.length === 0) {
      message.error('请选择文件')
      return
    }

    try {
      const files = fileList.map(file => file.originFileObj as File)
      await uploadDocuments(selectedKnowledge._id, files)
      message.success('文档上传成功！')
      setIsUploadModalOpen(false)
      setFileList([])
    } catch (error) {
      message.error(error instanceof Error ? error.message : '上传失败')
    }
  }

  // 删除文档
  const handleDeleteDocument = async (documentId: string) => {
    try {
      await deleteDocument(documentId)
      message.success('文档删除成功！')
    } catch (error) {
      message.error(error instanceof Error ? error.message : '删除失败')
    }
  }

  // 获取文件图标
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FilePdfOutlined style={{ color: '#ff4d4f' }} />
      case 'docx':
        return <FileWordOutlined style={{ color: '#1890ff' }} />
      case 'md':
        return <FileMarkdownOutlined style={{ color: '#52c41a' }} />
      default:
        return <FileTextOutlined style={{ color: '#666' }} />
    }
  }

  // 知识库表格列定义
  const knowledgeColumns: ColumnsType<Knowledge> = [
    {
      title: '知识库名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Knowledge) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>{text}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.description}
          </Text>
        </div>
      ),
    },
    {
      title: '文档数量',
      dataIndex: 'documents',
      key: 'documentCount',
      render: (documents: Document[]) => (
        <Tag color="blue">{documents.length} 个文档</Tag>
      ),
    },
    {
      title: '向量数量',
      dataIndex: ['vectorStore', 'totalVectors'],
      key: 'vectorCount',
      render: (count: number) => (
        <Text>{count?.toLocaleString() || 0} 个向量</Text>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record: Knowledge) => (
        <Space size="small">
          <Tooltip title="查看文档">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => setSelectedKnowledge(record)}
            />
          </Tooltip>
          <Tooltip title="上传文档">
            <Button
              type="text"
              icon={<UploadOutlined />}
              size="small"
              onClick={() => {
                setSelectedKnowledge(record)
                setIsUploadModalOpen(true)
              }}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确认删除"
              description="删除后无法恢复，确定要删除这个知识库吗？"
              onConfirm={() => handleDeleteKnowledge(record._id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  // 文档表格列定义
  const documentColumns: ColumnsType<Document> = [
    {
      title: '文档名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Document) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {getFileIcon(record.type)}
          <div>
            <div style={{ fontWeight: 500 }}>{text}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {(record.size / 1024 / 1024).toFixed(2)} MB
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '处理状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          processing: { color: 'processing', text: '处理中' },
          completed: { color: 'success', text: '已完成' },
          failed: { color: 'error', text: '失败' },
        }
        const config = statusConfig[status as keyof typeof statusConfig]
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: '分块数量',
      dataIndex: 'chunks',
      key: 'chunks',
      render: (chunks: number) => (
        <Text>{chunks} 个分块</Text>
      ),
    },
    {
      title: '上传时间',
      dataIndex: 'uploadedAt',
      key: 'uploadedAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record: Document) => (
        <Space size="small">
          <Tooltip title="下载">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              size="small"
              onClick={() => window.open(record.url, '_blank')}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确认删除"
              description="删除后无法恢复，确定要删除这个文档吗？"
              onConfirm={() => handleDeleteDocument(record._id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2}>知识库管理</Title>
          <Text type="secondary">管理您的文档和知识库，支持RAG检索增强生成</Text>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setIsModalOpen(true)}
        >
          创建知识库
        </Button>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="知识库数量"
              value={knowledgeBases.length}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="文档总数"
              value={knowledgeBases.reduce((sum: number, kb: Knowledge) => sum + kb.documents.length, 0)}
              prefix={<FilePdfOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="向量总数"
              value={knowledgeBases.reduce((sum: number, kb: Knowledge) => sum + (kb.vectorStore?.totalVectors || 0), 0)}
              prefix={<SearchOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs defaultActiveKey="knowledge" type="card">
          <TabPane tab="知识库列表" key="knowledge">
            {knowledgeBases.length === 0 ? (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无知识库"
                style={{ padding: '40px 0' }}
              >
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setIsModalOpen(true)}
                >
                  创建第一个知识库
                </Button>
              </Empty>
            ) : (
              <Table
                columns={knowledgeColumns}
                dataSource={knowledgeBases}
                rowKey="_id"
                loading={isLoading}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total: number, range: [number, number]) =>
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }}
              />
            )}
          </TabPane>

          <TabPane tab="文档管理" key="documents">
            {selectedKnowledge ? (
              <div>
                <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Text strong>{selectedKnowledge.name}</Text>
                    <Text type="secondary" style={{ marginLeft: 8 }}>
                      - {selectedKnowledge.documents.length} 个文档
                    </Text>
                  </div>
                  <Button
                    type="primary"
                    icon={<UploadOutlined />}
                    onClick={() => setIsUploadModalOpen(true)}
                  >
                    上传文档
                  </Button>
                </div>
                <Table
                  columns={documentColumns}
                  dataSource={selectedKnowledge.documents}
                  rowKey="_id"
                  loading={isLoading}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number, range: [number, number]) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }}
                />
              </div>
            ) : (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="请先选择一个知识库"
                style={{ padding: '40px 0' }}
              />
            )}
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建知识库模态框 */}
      <Modal
        title="创建知识库"
        open={isModalOpen}
        onCancel={() => {
          setIsModalOpen(false)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateKnowledge}
        >
          <Form.Item
            name="name"
            label="知识库名称"
            rules={[
              { required: true, message: '请输入知识库名称' },
              { min: 2, max: 50, message: '名称长度为2-50个字符' }
            ]}
          >
            <Input placeholder="请输入知识库名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[
              { required: true, message: '请输入描述' },
              { max: 200, message: '描述不能超过200个字符' }
            ]}
          >
            <TextArea
              rows={3}
              placeholder="请简要描述这个知识库的用途和内容"
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => {
                setIsModalOpen(false)
                form.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={isLoading}>
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 上传文档模态框 */}
      <Modal
        title="上传文档"
        open={isUploadModalOpen}
        onCancel={() => {
          setIsUploadModalOpen(false)
          setFileList([])
        }}
        footer={null}
        width={600}
      >
        <div style={{ padding: '20px 0' }}>
          <Text strong style={{ marginBottom: 16, display: 'block' }}>
            选择要上传的文档：
          </Text>

          <Upload.Dragger
            multiple
            fileList={fileList}
            onChange={({ fileList }) => setFileList(fileList)}
            beforeUpload={() => false}
            accept=".pdf,.docx,.txt,.md"
          >
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持 PDF、DOCX、TXT、MD 格式，单个文件不超过 10MB
            </p>
          </Upload.Dragger>

          <div style={{ textAlign: 'right', marginTop: 20 }}>
            <Space>
              <Button onClick={() => {
                setIsUploadModalOpen(false)
                setFileList([])
              }}>
                取消
              </Button>
              <Button
                type="primary"
                disabled={fileList.length === 0}
                loading={isLoading}
                onClick={handleUploadDocuments}
              >
                上传 ({fileList.length} 个文件)
              </Button>
            </Space>
          </div>
        </div>
      </Modal>
    </div>
  )
}
