import { Row, Col, Card, Statistic, Typography, Button, List, Avatar, Tag, Progress } from 'antd'
import {
  RobotOutlined,
  MessageOutlined,
  ThunderboltOutlined,
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

const { Title, Text } = Typography

export const DashboardPage: React.FC = () => {
  const navigate = useNavigate()

  // 模拟数据
  const stats = {
    characters: 5,
    conversations: 128,
    tokensUsed: 45678,
    tokensLimit: 100000,
  }

  const recentConversations = [
    {
      id: '1',
      title: '与客服助手的对话',
      character: '智能客服',
      lastMessage: '感谢您的咨询，还有其他问题吗？',
      time: '2小时前',
      avatar: '🤖',
    },
    {
      id: '2',
      title: '技术支持咨询',
      character: '技术专家',
      lastMessage: '这个问题需要检查日志文件...',
      time: '1天前',
      avatar: '👨‍💻',
    },
    {
      id: '3',
      title: '产品介绍对话',
      character: '销售顾问',
      lastMessage: '我们的产品具有以下特点...',
      time: '3天前',
      avatar: '👩‍💼',
    },
  ]

  const myCharacters = [
    {
      id: '1',
      name: '智能客服',
      description: '专业的客服助手，能够回答常见问题',
      avatar: '🤖',
      status: 'online',
      conversations: 45,
    },
    {
      id: '2',
      name: '技术专家',
      description: '精通各种技术问题的专家',
      avatar: '👨‍💻',
      status: 'offline',
      conversations: 23,
    },
    {
      id: '3',
      name: '销售顾问',
      description: '专业的销售和产品介绍专家',
      avatar: '👩‍💼',
      status: 'online',
      conversations: 31,
    },
  ]

  const quickActions = [
    {
      title: '创建数字人',
      description: '创建一个新的AI数字人角色',
      icon: <RobotOutlined />,
      action: () => navigate('/characters/new'),
    },
    {
      title: '开始对话',
      description: '与您的数字人开始新的对话',
      icon: <MessageOutlined />,
      action: () => navigate('/chat'),
    },
    {
      title: '上传文档',
      description: '为知识库添加新的文档',
      icon: <PlusOutlined />,
      action: () => navigate('/knowledge'),
    },
  ]

  const tokenUsagePercent = (stats.tokensUsed / stats.tokensLimit) * 100

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>仪表板</Title>
        <Text type="secondary">欢迎回来！这里是您的AI数字人管理中心。</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="数字人数量"
              value={stats.characters}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="对话次数"
              value={stats.conversations}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Token使用"
              value={stats.tokensUsed}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: '#faad14' }}
              suffix={`/ ${stats.tokensLimit.toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <div style={{ marginBottom: 8 }}>
              <Text strong>使用率</Text>
            </div>
            <Progress
              percent={Math.round(tokenUsagePercent)}
              strokeColor={tokenUsagePercent > 80 ? '#ff4d4f' : '#1890ff'}
            />
            <Text type="secondary" style={{ fontSize: 12 }}>
              本月剩余 {(stats.tokensLimit - stats.tokensUsed).toLocaleString()} tokens
            </Text>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 最近对话 */}
        <Col xs={24} lg={12}>
          <Card
            title="最近对话"
            extra={
              <Button type="link" onClick={() => navigate('/chat')}>
                查看全部
              </Button>
            }
          >
            <List
              dataSource={recentConversations}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      onClick={() => navigate(`/chat/${item.id}`)}
                    >
                      查看
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={<Avatar>{item.avatar}</Avatar>}
                    title={item.title}
                    description={
                      <div>
                        <div style={{ marginBottom: 4 }}>
                          <Tag size="small">{item.character}</Tag>
                        </div>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {item.lastMessage}
                        </Text>
                      </div>
                    }
                  />
                  <div style={{ textAlign: 'right' }}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {item.time}
                    </Text>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 我的数字人 */}
        <Col xs={24} lg={12}>
          <Card
            title="我的数字人"
            extra={
              <Button type="link" onClick={() => navigate('/characters')}>
                查看全部
              </Button>
            }
          >
            <List
              dataSource={myCharacters}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() => navigate(`/characters/${item.id}`)}
                    >
                      编辑
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={<Avatar>{item.avatar}</Avatar>}
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        {item.name}
                        <Tag
                          color={item.status === 'online' ? 'green' : 'default'}
                          size="small"
                        >
                          {item.status === 'online' ? '在线' : '离线'}
                        </Tag>
                      </div>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: 4 }}>
                          {item.description}
                        </div>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {item.conversations} 次对话
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={8} key={index}>
              <Card
                hoverable
                onClick={action.action}
                style={{ textAlign: 'center', cursor: 'pointer' }}
              >
                <div style={{ fontSize: 32, marginBottom: 16 }}>
                  {action.icon}
                </div>
                <Title level={4} style={{ marginBottom: 8 }}>
                  {action.title}
                </Title>
                <Text type="secondary">{action.description}</Text>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    </div>
  )
}
