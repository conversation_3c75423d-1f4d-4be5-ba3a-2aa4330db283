import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import { CharactersPage } from '../CharactersPage'
import { useCharacterStore } from '@stores/characterStore'
import type { Character } from '@types/index'

// Mock the character store
jest.mock('@stores/characterStore')
const mockUseCharacterStore = useCharacterStore as jest.MockedFunction<typeof useCharacterStore>

// Mock react-router-dom
const mockNavigate = jest.fn()
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}))

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>{children}</BrowserRouter>
)

// Mock character data
const mockCharacter: Character = {
  _id: '1',
  name: '测试数字人',
  description: '这是一个测试数字人',
  avatar: 'https://example.com/avatar.jpg',
  userId: 'user1',
  config: {
    personality: '友善、专业',
    expertise: ['客服', '技术支持'],
    language: 'zh-CN',
    voice: {
      provider: 'azure',
      voiceId: 'zh-CN-XiaoxiaoNeural',
      speed: 1.0,
      pitch: 1.0,
    },
    aiModel: {
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 2000,
      systemPrompt: '你是一个友善的AI助手',
    },
  },
  knowledgeBase: {
    enabled: false,
    documents: [],
  },
  isPublic: false,
  tags: ['测试'],
  stats: {
    conversationCount: 5,
    messageCount: 50,
    lastUsedAt: '2024-01-01T00:00:00Z',
  },
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
}

describe('CharactersPage', () => {
  const mockFetchUserCharacters = jest.fn()
  const mockCreateCharacter = jest.fn()
  const mockUpdateCharacter = jest.fn()
  const mockDeleteCharacter = jest.fn()
  const mockCloneCharacter = jest.fn()
  
  beforeEach(() => {
    mockUseCharacterStore.mockReturnValue({
      characters: [mockCharacter],
      publicCharacters: [],
      currentCharacter: null,
      isLoading: false,
      error: null,
      pagination: {
        page: 1,
        limit: 20,
        total: 1,
        totalPages: 1,
      },
      filters: {
        search: '',
        tags: [],
        language: '',
        sortBy: 'stats.lastUsedAt',
        sortOrder: 'desc',
      },
      fetchUserCharacters: mockFetchUserCharacters,
      fetchPublicCharacters: jest.fn(),
      fetchCharacter: jest.fn(),
      searchCharacters: jest.fn(),
      createCharacter: mockCreateCharacter,
      updateCharacter: mockUpdateCharacter,
      deleteCharacter: mockDeleteCharacter,
      cloneCharacter: mockCloneCharacter,
      bindKnowledgeBase: jest.fn(),
      unbindKnowledgeBase: jest.fn(),
      fetchCharacterStats: jest.fn(),
      setCurrentCharacter: jest.fn(),
      setFilters: jest.fn(),
      clearError: jest.fn(),
      resetState: jest.fn(),
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('应该正确渲染数字人管理页面', () => {
    render(
      <TestWrapper>
        <CharactersPage />
      </TestWrapper>
    )

    expect(screen.getByText('数字人管理')).toBeInTheDocument()
    expect(screen.getByText('创建和管理您的AI数字人角色')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '创建数字人' })).toBeInTheDocument()
  })

  it('应该在组件挂载时获取数字人列表', () => {
    render(
      <TestWrapper>
        <CharactersPage />
      </TestWrapper>
    )

    expect(mockFetchUserCharacters).toHaveBeenCalled()
  })

  it('应该显示数字人统计信息', () => {
    render(
      <TestWrapper>
        <CharactersPage />
      </TestWrapper>
    )

    expect(screen.getByText('总数字人')).toBeInTheDocument()
    expect(screen.getByText('1')).toBeInTheDocument() // 数字人数量
    expect(screen.getByText('公开数字人')).toBeInTheDocument()
    expect(screen.getByText('总对话数')).toBeInTheDocument()
  })

  it('应该显示数字人列表', () => {
    render(
      <TestWrapper>
        <CharactersPage />
      </TestWrapper>
    )

    expect(screen.getByText('测试数字人')).toBeInTheDocument()
    expect(screen.getByText('这是一个测试数字人')).toBeInTheDocument()
    expect(screen.getByText('客服')).toBeInTheDocument()
    expect(screen.getByText('技术支持')).toBeInTheDocument()
    expect(screen.getByText('openai')).toBeInTheDocument()
    expect(screen.getByText('5 对话')).toBeInTheDocument()
    expect(screen.getByText('50 消息')).toBeInTheDocument()
  })

  it('应该打开创建数字人模态框', async () => {
    render(
      <TestWrapper>
        <CharactersPage />
      </TestWrapper>
    )

    const createButton = screen.getByRole('button', { name: '创建数字人' })
    fireEvent.click(createButton)

    await waitFor(() => {
      expect(screen.getByText('创建数字人')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('请输入数字人名称')).toBeInTheDocument()
    })
  })

  it('应该能够编辑数字人', async () => {
    render(
      <TestWrapper>
        <CharactersPage />
      </TestWrapper>
    )

    const editButton = screen.getByLabelText('编辑')
    fireEvent.click(editButton)

    await waitFor(() => {
      expect(screen.getByText('编辑数字人')).toBeInTheDocument()
      expect(screen.getByDisplayValue('测试数字人')).toBeInTheDocument()
    })
  })

  it('应该能够删除数字人', async () => {
    render(
      <TestWrapper>
        <CharactersPage />
      </TestWrapper>
    )

    const deleteButton = screen.getByLabelText('删除')
    fireEvent.click(deleteButton)

    await waitFor(() => {
      expect(screen.getByText('确认删除')).toBeInTheDocument()
      expect(screen.getByText('删除后无法恢复，确定要删除这个数字人吗？')).toBeInTheDocument()
    })

    const confirmButton = screen.getByRole('button', { name: '确定' })
    fireEvent.click(confirmButton)

    await waitFor(() => {
      expect(mockDeleteCharacter).toHaveBeenCalledWith('1')
    })
  })

  it('应该能够克隆数字人', async () => {
    render(
      <TestWrapper>
        <CharactersPage />
      </TestWrapper>
    )

    const cloneButton = screen.getByLabelText('克隆')
    fireEvent.click(cloneButton)

    await waitFor(() => {
      expect(mockCloneCharacter).toHaveBeenCalledWith('1')
    })
  })

  it('应该显示空状态当没有数字人时', () => {
    mockUseCharacterStore.mockReturnValue({
      characters: [],
      publicCharacters: [],
      currentCharacter: null,
      isLoading: false,
      error: null,
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
      },
      filters: {
        search: '',
        tags: [],
        language: '',
        sortBy: 'stats.lastUsedAt',
        sortOrder: 'desc',
      },
      fetchUserCharacters: mockFetchUserCharacters,
      fetchPublicCharacters: jest.fn(),
      fetchCharacter: jest.fn(),
      searchCharacters: jest.fn(),
      createCharacter: mockCreateCharacter,
      updateCharacter: mockUpdateCharacter,
      deleteCharacter: mockDeleteCharacter,
      cloneCharacter: mockCloneCharacter,
      bindKnowledgeBase: jest.fn(),
      unbindKnowledgeBase: jest.fn(),
      fetchCharacterStats: jest.fn(),
      setCurrentCharacter: jest.fn(),
      setFilters: jest.fn(),
      clearError: jest.fn(),
      resetState: jest.fn(),
    })

    render(
      <TestWrapper>
        <CharactersPage />
      </TestWrapper>
    )

    expect(screen.getByText('暂无数字人')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '创建第一个数字人' })).toBeInTheDocument()
  })

  it('应该显示加载状态', () => {
    mockUseCharacterStore.mockReturnValue({
      characters: [],
      publicCharacters: [],
      currentCharacter: null,
      isLoading: true,
      error: null,
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
      },
      filters: {
        search: '',
        tags: [],
        language: '',
        sortBy: 'stats.lastUsedAt',
        sortOrder: 'desc',
      },
      fetchUserCharacters: mockFetchUserCharacters,
      fetchPublicCharacters: jest.fn(),
      fetchCharacter: jest.fn(),
      searchCharacters: jest.fn(),
      createCharacter: mockCreateCharacter,
      updateCharacter: mockUpdateCharacter,
      deleteCharacter: mockDeleteCharacter,
      cloneCharacter: mockCloneCharacter,
      bindKnowledgeBase: jest.fn(),
      unbindKnowledgeBase: jest.fn(),
      fetchCharacterStats: jest.fn(),
      setCurrentCharacter: jest.fn(),
      setFilters: jest.fn(),
      clearError: jest.fn(),
      resetState: jest.fn(),
    })

    render(
      <TestWrapper>
        <CharactersPage />
      </TestWrapper>
    )

    // 检查表格是否显示加载状态
    expect(screen.getByRole('table')).toHaveClass('ant-table-loading')
  })
})
