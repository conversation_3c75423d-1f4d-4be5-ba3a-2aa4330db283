import { useState, useEffect } from 'react'
import {
  Typography,
  Button,
  Card,
  Table,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Slider,
  Switch,
  message,
  Popconfirm,
  Tooltip,
  Row,
  Col,
  Statistic,
  Empty
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
  SettingOutlined,
  RobotOutlined,
  MessageOutlined,
  UserOutlined
} from '@ant-design/icons'
import { useCharacterStore } from '@stores/characterStore'
import type { Character, CharacterForm } from '@types/index'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input

export const CharactersPage: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingCharacter, setEditingCharacter] = useState<Character | null>(null)
  const [form] = Form.useForm()

  const {
    characters,
    isLoading,
    fetchUserCharacters,
    createCharacter,
    updateCharacter,
    deleteCharacter,
    cloneCharacter
  } = useCharacterStore()

  // 组件挂载时获取数字人列表
  useEffect(() => {
    fetchUserCharacters()
  }, [fetchUserCharacters])

  // 打开创建/编辑模态框
  const handleOpenModal = (character?: Character) => {
    setEditingCharacter(character || null)
    if (character) {
      // 编辑模式：填充表单数据
      form.setFieldsValue({
        name: character.name,
        description: character.description,
        personality: character.config.personality,
        expertise: character.config.expertise,
        language: character.config.language,
        aiModel: character.config.aiModel,
        voice: character.config.voice,
        tags: character.tags,
        isPublic: character.isPublic
      })
    } else {
      // 创建模式：重置表单
      form.resetFields()
    }
    setIsModalOpen(true)
  }

  // 关闭模态框
  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingCharacter(null)
    form.resetFields()
  }

  // 提交表单
  const handleSubmit = async (values: CharacterForm) => {
    try {
      if (editingCharacter) {
        // 更新数字人
        await updateCharacter(editingCharacter._id, values)
        message.success('数字人更新成功！')
      } else {
        // 创建数字人
        await createCharacter(values)
        message.success('数字人创建成功！')
      }
      handleCloseModal()
    } catch (error) {
      message.error(error instanceof Error ? error.message : '操作失败')
    }
  }

  // 删除数字人
  const handleDelete = async (id: string) => {
    try {
      await deleteCharacter(id)
      message.success('数字人删除成功！')
    } catch (error) {
      message.error(error instanceof Error ? error.message : '删除失败')
    }
  }

  // 克隆数字人
  const handleClone = async (character: Character) => {
    try {
      await cloneCharacter(character._id)
      message.success('数字人克隆成功！')
    } catch (error) {
      message.error(error instanceof Error ? error.message : '克隆失败')
    }
  }

  // 表格列定义
  const columns: ColumnsType<Character> = [
    {
      title: '数字人',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Character) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <Avatar
            src={record.avatar}
            icon={<RobotOutlined />}
            size={48}
          />
          <div>
            <div style={{ fontWeight: 500, marginBottom: 4 }}>{text}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.description}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '专业领域',
      dataIndex: ['config', 'expertise'],
      key: 'expertise',
      render: (expertise: string[]) => (
        <div>
          {expertise.slice(0, 2).map((item, index) => (
            <Tag key={index} color="blue" style={{ marginBottom: 4 }}>
              {item}
            </Tag>
          ))}
          {expertise.length > 2 && (
            <Tag color="default">+{expertise.length - 2}</Tag>
          )}
        </div>
      ),
    },
    {
      title: 'AI模型',
      dataIndex: ['config', 'aiModel'],
      key: 'aiModel',
      render: (aiModel: any) => (
        <div>
          <Tag color="green">{aiModel.provider}</Tag>
          <div style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
            {aiModel.model}
          </div>
        </div>
      ),
    },
    {
      title: '使用统计',
      dataIndex: 'stats',
      key: 'stats',
      render: (stats: any) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 4, marginBottom: 2 }}>
            <MessageOutlined style={{ fontSize: 12, color: '#1890ff' }} />
            <Text style={{ fontSize: 12 }}>{stats.conversationCount} 对话</Text>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
            <UserOutlined style={{ fontSize: 12, color: '#52c41a' }} />
            <Text style={{ fontSize: 12 }}>{stats.messageCount} 消息</Text>
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isPublic',
      key: 'status',
      render: (isPublic: boolean) => (
        <Tag color={isPublic ? 'green' : 'orange'}>
          {isPublic ? '公开' : '私有'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record: Character) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleOpenModal(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleOpenModal(record)}
            />
          </Tooltip>
          <Tooltip title="克隆">
            <Button
              type="text"
              icon={<CopyOutlined />}
              size="small"
              onClick={() => handleClone(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确认删除"
              description="删除后无法恢复，确定要删除这个数字人吗？"
              onConfirm={() => handleDelete(record._id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2}>数字人管理</Title>
          <Text type="secondary">创建和管理您的AI数字人角色</Text>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => handleOpenModal()}
        >
          创建数字人
        </Button>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="总数字人"
              value={characters.length}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="公开数字人"
              value={characters.filter((c: Character) => c.isPublic).length}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="总对话数"
              value={characters.reduce((sum: number, c: Character) => sum + c.stats.conversationCount, 0)}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 数字人列表 */}
      <Card>
        {characters.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无数字人"
            style={{ padding: '40px 0' }}
          >
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleOpenModal()}
            >
              创建第一个数字人
            </Button>
          </Empty>
        ) : (
          <Table
            columns={columns}
            dataSource={characters}
            rowKey="_id"
            loading={isLoading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total: number, range: [number, number]) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }}
          />
        )}
      </Card>

      {/* 创建/编辑数字人模态框 */}
      <Modal
        title={editingCharacter ? '编辑数字人' : '创建数字人'}
        open={isModalOpen}
        onCancel={handleCloseModal}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            language: 'zh-CN',
            aiModel: {
              provider: 'openai',
              model: 'gpt-3.5-turbo',
              temperature: 0.7,
              maxTokens: 2000,
              systemPrompt: '你是一个友善、专业的AI助手。'
            },
            voice: {
              provider: 'azure',
              voiceId: 'zh-CN-XiaoxiaoNeural',
              speed: 1.0,
              pitch: 1.0
            },
            isPublic: false
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="数字人名称"
                rules={[
                  { required: true, message: '请输入数字人名称' },
                  { min: 2, max: 50, message: '名称长度为2-50个字符' }
                ]}
              >
                <Input placeholder="请输入数字人名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="language"
                label="语言"
                rules={[{ required: true, message: '请选择语言' }]}
              >
                <Select placeholder="请选择语言">
                  <Option value="zh-CN">中文</Option>
                  <Option value="en-US">English</Option>
                  <Option value="ja-JP">日本語</Option>
                  <Option value="ko-KR">한국어</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
            rules={[
              { required: true, message: '请输入描述' },
              { max: 200, message: '描述不能超过200个字符' }
            ]}
          >
            <TextArea
              rows={3}
              placeholder="请简要描述这个数字人的特点和用途"
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Form.Item
            name="personality"
            label="个性特征"
            rules={[
              { required: true, message: '请输入个性特征' },
              { max: 500, message: '个性特征不能超过500个字符' }
            ]}
          >
            <TextArea
              rows={4}
              placeholder="请描述数字人的个性特征，如：友善、专业、幽默等"
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="expertise"
            label="专业领域"
            rules={[{ required: true, message: '请选择至少一个专业领域' }]}
          >
            <Select
              mode="tags"
              placeholder="请输入或选择专业领域"
              style={{ width: '100%' }}
            >
              <Option value="客服">客服</Option>
              <Option value="销售">销售</Option>
              <Option value="技术支持">技术支持</Option>
              <Option value="教育培训">教育培训</Option>
              <Option value="医疗健康">医疗健康</Option>
              <Option value="金融理财">金融理财</Option>
              <Option value="法律咨询">法律咨询</Option>
              <Option value="心理咨询">心理咨询</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Select
              mode="tags"
              placeholder="请添加标签"
              style={{ width: '100%' }}
            />
          </Form.Item>

          {/* AI模型配置 */}
          <Card title="AI模型配置" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name={['aiModel', 'provider']}
                  label="AI提供商"
                  rules={[{ required: true, message: '请选择AI提供商' }]}
                >
                  <Select placeholder="请选择AI提供商">
                    <Option value="openai">OpenAI</Option>
                    <Option value="anthropic">Anthropic</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['aiModel', 'model']}
                  label="模型"
                  rules={[{ required: true, message: '请选择模型' }]}
                >
                  <Select placeholder="请选择模型">
                    <Option value="gpt-3.5-turbo">GPT-3.5 Turbo</Option>
                    <Option value="gpt-4">GPT-4</Option>
                    <Option value="claude-3-haiku">Claude 3 Haiku</Option>
                    <Option value="claude-3-sonnet">Claude 3 Sonnet</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name={['aiModel', 'temperature']}
                  label="创造性"
                >
                  <Slider
                    min={0}
                    max={2}
                    step={0.1}
                    marks={{
                      0: '保守',
                      1: '平衡',
                      2: '创新'
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['aiModel', 'maxTokens']}
                  label="最大回复长度"
                >
                  <Slider
                    min={100}
                    max={4000}
                    step={100}
                    marks={{
                      100: '简短',
                      2000: '适中',
                      4000: '详细'
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name={['aiModel', 'systemPrompt']}
              label="系统提示词"
              rules={[{ required: true, message: '请输入系统提示词' }]}
            >
              <TextArea
                rows={3}
                placeholder="请输入系统提示词，定义数字人的行为和回复风格"
              />
            </Form.Item>
          </Card>

          {/* 语音配置 */}
          <Card title="语音配置" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name={['voice', 'provider']}
                  label="语音提供商"
                >
                  <Select placeholder="请选择语音提供商">
                    <Option value="azure">Azure</Option>
                    <Option value="aliyun">阿里云</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['voice', 'voiceId']}
                  label="音色"
                >
                  <Select placeholder="请选择音色">
                    <Option value="zh-CN-XiaoxiaoNeural">晓晓（女声）</Option>
                    <Option value="zh-CN-YunxiNeural">云希（男声）</Option>
                    <Option value="zh-CN-XiaoyiNeural">晓伊（女声）</Option>
                    <Option value="zh-CN-YunjianNeural">云健（男声）</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name={['voice', 'speed']}
                  label="语速"
                >
                  <Slider
                    min={0.5}
                    max={2}
                    step={0.1}
                    marks={{
                      0.5: '慢',
                      1: '正常',
                      2: '快'
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['voice', 'pitch']}
                  label="音调"
                >
                  <Slider
                    min={0.5}
                    max={2}
                    step={0.1}
                    marks={{
                      0.5: '低',
                      1: '正常',
                      2: '高'
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 其他设置 */}
          <Card title="其他设置" size="small" style={{ marginBottom: 24 }}>
            <Form.Item
              name="isPublic"
              label="公开设置"
              valuePropName="checked"
            >
              <Switch
                checkedChildren="公开"
                unCheckedChildren="私有"
              />
            </Form.Item>
          </Card>

          {/* 表单按钮 */}
          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={handleCloseModal}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={isLoading}>
                {editingCharacter ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
