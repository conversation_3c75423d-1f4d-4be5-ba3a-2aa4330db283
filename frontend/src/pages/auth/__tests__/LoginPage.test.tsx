import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { LoginPage } from '../LoginPage'
import { useAuthStore } from '@stores/authStore'

// Mock the auth store
jest.mock('@stores/authStore')
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>

// Mock react-router-dom
const mockNavigate = jest.fn()
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}))

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>{children}</BrowserRouter>
)

describe('LoginPage', () => {
  const mockLogin = jest.fn()
  
  beforeEach(() => {
    mockUseAuthStore.mockReturnValue({
      login: mockLogin,
      isLoading: false,
      user: null,
      token: null,
      error: null,
      register: jest.fn(),
      logout: jest.fn(),
      refreshToken: jest.fn(),
      updateProfile: jest.fn(),
      clearError: jest.fn(),
      setUser: jest.fn(),
      setToken: jest.fn(),
      setLoading: jest.fn(),
      setError: jest.fn(),
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('应该正确渲染登录页面', () => {
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    )

    expect(screen.getByText('欢迎回来')).toBeInTheDocument()
    expect(screen.getByText('登录您的AI数字人账户')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('请输入邮箱')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('请输入密码')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument()
  })

  it('应该显示表单验证错误', async () => {
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    )

    const submitButton = screen.getByRole('button', { name: '登录' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('请输入邮箱')).toBeInTheDocument()
      expect(screen.getByText('请输入密码')).toBeInTheDocument()
    })
  })

  it('应该验证邮箱格式', async () => {
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    )

    const emailInput = screen.getByPlaceholderText('请输入邮箱')
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } })
    
    const submitButton = screen.getByRole('button', { name: '登录' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('请输入有效的邮箱地址')).toBeInTheDocument()
    })
  })

  it('应该验证密码长度', async () => {
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    )

    const emailInput = screen.getByPlaceholderText('请输入邮箱')
    const passwordInput = screen.getByPlaceholderText('请输入密码')
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: '123' } })
    
    const submitButton = screen.getByRole('button', { name: '登录' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('密码至少6位')).toBeInTheDocument()
    })
  })

  it('应该成功提交登录表单', async () => {
    mockLogin.mockResolvedValueOnce(undefined)
    
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    )

    const emailInput = screen.getByPlaceholderText('请输入邮箱')
    const passwordInput = screen.getByPlaceholderText('请输入密码')
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    
    const submitButton = screen.getByRole('button', { name: '登录' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        remember: false,
      })
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard')
    })
  })

  it('应该处理登录错误', async () => {
    const errorMessage = '登录失败'
    mockLogin.mockRejectedValueOnce(new Error(errorMessage))
    
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    )

    const emailInput = screen.getByPlaceholderText('请输入邮箱')
    const passwordInput = screen.getByPlaceholderText('请输入密码')
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    
    const submitButton = screen.getByRole('button', { name: '登录' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalled()
      // Note: In a real test, you might want to check for error message display
    })
  })

  it('应该切换记住我选项', () => {
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    )

    const rememberCheckbox = screen.getByRole('checkbox', { name: '记住我' })
    expect(rememberCheckbox).not.toBeChecked()
    
    fireEvent.click(rememberCheckbox)
    expect(rememberCheckbox).toBeChecked()
  })

  it('应该显示加载状态', () => {
    mockUseAuthStore.mockReturnValue({
      login: mockLogin,
      isLoading: true,
      user: null,
      token: null,
      error: null,
      register: jest.fn(),
      logout: jest.fn(),
      refreshToken: jest.fn(),
      updateProfile: jest.fn(),
      clearError: jest.fn(),
      setUser: jest.fn(),
      setToken: jest.fn(),
      setLoading: jest.fn(),
      setError: jest.fn(),
    })

    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    )

    const submitButton = screen.getByRole('button', { name: '登录' })
    expect(submitButton).toHaveClass('ant-btn-loading')
  })

  it('应该有注册链接', () => {
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    )

    const registerLink = screen.getByText('立即注册')
    expect(registerLink).toBeInTheDocument()
    expect(registerLink.closest('a')).toHaveAttribute('href', '/register')
  })

  it('应该有忘记密码链接', () => {
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    )

    const forgotPasswordLink = screen.getByText('忘记密码？')
    expect(forgotPasswordLink).toBeInTheDocument()
    expect(forgotPasswordLink.closest('a')).toHaveAttribute('href', '/forgot-password')
  })
})
