import { useState } from 'react'
import {
  Typography,
  Card,
  Tabs,
  Form,
  Input,
  Button,
  Avatar,
  Upload,
  Switch,
  Select,
  Divider,
  Space,
  message,
  Row,
  Col,
  Statistic,
  Progress,
  Tag,
  List,
  Modal
} from 'antd'
import {
  UserOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  BellOutlined,
  UploadOutlined,
  EditOutlined,
  KeyOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { useAuthStore, useUser } from '@stores/authStore'
import type { User } from '@types/index'

const { Title, Text } = Typography
const { TabPane } = Tabs
const { Option } = Select
const { TextArea } = Input

export const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile')
  const [profileForm] = Form.useForm()
  const [passwordForm] = Form.useForm()
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false)

  const { updateProfile, logout, isLoading } = useAuthStore()
  const user = useUser()

  // 更新个人信息
  const handleUpdateProfile = async (values: Partial<User>) => {
    try {
      await updateProfile(values)
      message.success('个人信息更新成功！')
    } catch (error) {
      message.error(error instanceof Error ? error.message : '更新失败')
    }
  }

  // 修改密码
  const handleChangePassword = async (values: any) => {
    try {
      // TODO: 实现修改密码API
      message.success('密码修改成功！')
      setIsPasswordModalOpen(false)
      passwordForm.resetFields()
    } catch (error) {
      message.error(error instanceof Error ? error.message : '修改密码失败')
    }
  }

  // 头像上传
  const handleAvatarUpload = (info: any) => {
    if (info.file.status === 'done') {
      message.success('头像上传成功！')
      // TODO: 更新用户头像
    } else if (info.file.status === 'error') {
      message.error('头像上传失败！')
    }
  }

  // 注销账户
  const handleDeleteAccount = () => {
    Modal.confirm({
      title: '确认注销账户',
      icon: <ExclamationCircleOutlined />,
      content: '注销账户后，所有数据将被永久删除且无法恢复。确定要继续吗？',
      okText: '确认注销',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        // TODO: 实现注销账户API
        message.success('账户注销成功')
        logout()
      },
    })
  }

  if (!user) {
    return null
  }

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>设置</Title>
        <Text type="secondary">管理您的账户和应用设置</Text>
      </div>

      {/* 用户信息卡片 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={24} align="middle">
          <Col>
            <Avatar
              size={80}
              src={user.avatar}
              icon={<UserOutlined />}
            />
          </Col>
          <Col flex={1}>
            <div>
              <Title level={4} style={{ marginBottom: 4 }}>
                {user.username}
              </Title>
              <Text type="secondary" style={{ display: 'block', marginBottom: 8 }}>
                {user.email}
              </Text>
              <Space>
                <Tag color={user.subscription.plan === 'free' ? 'default' : 'blue'}>
                  {user.subscription.plan === 'free' ? '免费版' : '专业版'}
                </Tag>
                <Tag color={user.role === 'admin' ? 'red' : 'green'}>
                  {user.role === 'admin' ? '管理员' : '普通用户'}
                </Tag>
              </Space>
            </div>
          </Col>
          <Col>
            <Space direction="vertical" style={{ textAlign: 'center' }}>
              <Statistic
                title="Token使用量"
                value={user.subscription.tokensUsed}
                suffix={`/ ${user.subscription.tokensLimit.toLocaleString()}`}
                valueStyle={{ fontSize: 16 }}
              />
              <Progress
                percent={Math.round((user.subscription.tokensUsed / user.subscription.tokensLimit) * 100)}
                size="small"
                strokeColor={
                  user.subscription.tokensUsed / user.subscription.tokensLimit > 0.8
                    ? '#ff4d4f'
                    : '#1890ff'
                }
              />
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 设置选项卡 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          type="card"
        >
          {/* 个人资料 */}
          <TabPane
            tab={
              <span>
                <UserOutlined />
                个人资料
              </span>
            }
            key="profile"
          >
            <Form
              form={profileForm}
              layout="vertical"
              initialValues={{
                username: user.username,
                email: user.email,
                language: user.preferences.language,
                theme: user.preferences.theme
              }}
              onFinish={handleUpdateProfile}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="username"
                    label="用户名"
                    rules={[
                      { required: true, message: '请输入用户名' },
                      { min: 3, max: 20, message: '用户名长度为3-20个字符' }
                    ]}
                  >
                    <Input placeholder="请输入用户名" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="email"
                    label="邮箱"
                    rules={[
                      { required: true, message: '请输入邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input placeholder="请输入邮箱" disabled />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item label="头像">
                <Upload
                  name="avatar"
                  listType="picture-card"
                  className="avatar-uploader"
                  showUploadList={false}
                  action="/api/upload/avatar"
                  beforeUpload={(file) => {
                    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
                    if (!isJpgOrPng) {
                      message.error('只能上传 JPG/PNG 格式的图片!')
                    }
                    const isLt2M = file.size / 1024 / 1024 < 2
                    if (!isLt2M) {
                      message.error('图片大小不能超过 2MB!')
                    }
                    return isJpgOrPng && isLt2M
                  }}
                  onChange={handleAvatarUpload}
                >
                  <div>
                    <Avatar
                      size={100}
                      src={user.avatar}
                      icon={<UserOutlined />}
                    />
                    <div style={{ marginTop: 8 }}>
                      <UploadOutlined /> 更换头像
                    </div>
                  </div>
                </Upload>
              </Form.Item>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="language"
                    label="语言"
                  >
                    <Select placeholder="请选择语言">
                      <Option value="zh-CN">中文</Option>
                      <Option value="en-US">English</Option>
                      <Option value="ja-JP">日本語</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="theme"
                    label="主题"
                  >
                    <Select placeholder="请选择主题">
                      <Option value="light">浅色</Option>
                      <Option value="dark">深色</Option>
                      <Option value="auto">跟随系统</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button type="primary" htmlType="submit" loading={isLoading}>
                  保存更改
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          {/* 账户安全 */}
          <TabPane
            tab={
              <span>
                <SecurityScanOutlined />
                账户安全
              </span>
            }
            key="security"
          >
            <List
              itemLayout="horizontal"
              dataSource={[
                {
                  title: '登录密码',
                  description: '定期更换密码可以提高账户安全性',
                  action: (
                    <Button
                      icon={<EditOutlined />}
                      onClick={() => setIsPasswordModalOpen(true)}
                    >
                      修改密码
                    </Button>
                  )
                },
                {
                  title: '两步验证',
                  description: '开启两步验证可以大大提高账户安全性',
                  action: (
                    <Switch
                      checkedChildren="已开启"
                      unCheckedChildren="已关闭"
                      defaultChecked={false}
                      onChange={(checked) => {
                        message.info(checked ? '两步验证已开启' : '两步验证已关闭')
                      }}
                    />
                  )
                },
                {
                  title: '登录设备管理',
                  description: '查看和管理已登录的设备',
                  action: (
                    <Button icon={<SettingOutlined />}>
                      管理设备
                    </Button>
                  )
                }
              ]}
              renderItem={(item) => (
                <List.Item actions={[item.action]}>
                  <List.Item.Meta
                    title={item.title}
                    description={item.description}
                  />
                </List.Item>
              )}
            />

            <Divider />

            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Title level={5} type="danger">危险操作</Title>
              <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
                注销账户后，所有数据将被永久删除且无法恢复
              </Text>
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={handleDeleteAccount}
              >
                注销账户
              </Button>
            </div>
          </TabPane>

          {/* 通知设置 */}
          <TabPane
            tab={
              <span>
                <BellOutlined />
                通知设置
              </span>
            }
            key="notifications"
          >
            <List
              itemLayout="horizontal"
              dataSource={[
                {
                  title: '邮件通知',
                  description: '接收重要的系统通知和更新',
                  action: (
                    <Switch
                      defaultChecked={user.preferences.notifications.email}
                      onChange={(checked) => {
                        // TODO: 更新通知设置
                        message.info(checked ? '邮件通知已开启' : '邮件通知已关闭')
                      }}
                    />
                  )
                },
                {
                  title: '浏览器通知',
                  description: '在浏览器中接收实时通知',
                  action: (
                    <Switch
                      defaultChecked={user.preferences.notifications.browser}
                      onChange={(checked) => {
                        // TODO: 更新通知设置
                        message.info(checked ? '浏览器通知已开启' : '浏览器通知已关闭')
                      }}
                    />
                  )
                },
                {
                  title: '对话通知',
                  description: '收到新消息时发送通知',
                  action: (
                    <Switch
                      defaultChecked={true}
                      onChange={(checked) => {
                        message.info(checked ? '对话通知已开启' : '对话通知已关闭')
                      }}
                    />
                  )
                },
                {
                  title: '系统维护通知',
                  description: '系统维护和更新时发送通知',
                  action: (
                    <Switch
                      defaultChecked={true}
                      onChange={(checked) => {
                        message.info(checked ? '系统维护通知已开启' : '系统维护通知已关闭')
                      }}
                    />
                  )
                }
              ]}
              renderItem={(item) => (
                <List.Item actions={[item.action]}>
                  <List.Item.Meta
                    title={item.title}
                    description={item.description}
                  />
                </List.Item>
              )}
            />
          </TabPane>

          {/* 应用设置 */}
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                应用设置
              </span>
            }
            key="app"
          >
            <Form layout="vertical">
              <Form.Item label="默认AI模型">
                <Select defaultValue="gpt-3.5-turbo" style={{ width: '100%' }}>
                  <Option value="gpt-3.5-turbo">GPT-3.5 Turbo</Option>
                  <Option value="gpt-4">GPT-4</Option>
                  <Option value="claude-3-haiku">Claude 3 Haiku</Option>
                  <Option value="claude-3-sonnet">Claude 3 Sonnet</Option>
                </Select>
              </Form.Item>

              <Form.Item label="默认语音">
                <Select defaultValue="zh-CN-XiaoxiaoNeural" style={{ width: '100%' }}>
                  <Option value="zh-CN-XiaoxiaoNeural">晓晓（女声）</Option>
                  <Option value="zh-CN-YunxiNeural">云希（男声）</Option>
                  <Option value="zh-CN-XiaoyiNeural">晓伊（女声）</Option>
                  <Option value="zh-CN-YunjianNeural">云健（男声）</Option>
                </Select>
              </Form.Item>

              <Form.Item label="自动保存对话">
                <Switch defaultChecked />
              </Form.Item>

              <Form.Item label="启用RAG检索">
                <Switch defaultChecked />
              </Form.Item>

              <Form.Item label="数据导出格式">
                <Select defaultValue="json" style={{ width: '100%' }}>
                  <Option value="json">JSON</Option>
                  <Option value="csv">CSV</Option>
                  <Option value="xlsx">Excel</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Button type="primary">
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Card>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={isPasswordModalOpen}
        onCancel={() => {
          setIsPasswordModalOpen(false)
          passwordForm.resetFields()
        }}
        footer={null}
        width={500}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleChangePassword}
        >
          <Form.Item
            name="currentPassword"
            label="当前密码"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password placeholder="请输入当前密码" />
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 8, message: '密码至少8位' }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'))
                },
              }),
            ]}
          >
            <Input.Password placeholder="请再次输入新密码" />
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => {
                setIsPasswordModalOpen(false)
                passwordForm.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={isLoading}>
                确认修改
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
