import { useState, useEffect, useRef } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Typography,
  Card,
  Input,
  Button,
  Avatar,
  List,
  Space,
  Dropdown,
  Modal,
  Select,
  message,
  Spin,
  Empty,
  Tooltip,
  Row,
  Col,
  Divider
} from 'antd'
import {
  SendOutlined,
  PlusOutlined,
  MoreOutlined,
  DeleteOutlined,
  EditOutlined,
  RobotOutlined,
  UserOutlined,
  HistoryOutlined,
  SettingOutlined,
  AudioOutlined,
  StopOutlined
} from '@ant-design/icons'
import { useConversationStore } from '@stores/conversationStore'
import { useCharacterStore } from '@stores/characterStore'
import { useSocketService } from '@hooks/useSocketService'
import type { Message, Conversation, Character } from '@types/index'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

export const ChatPage: React.FC = () => {
  const { conversationId } = useParams<{ conversationId?: string }>()
  const navigate = useNavigate()

  // 状态管理
  const [messageInput, setMessageInput] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null)
  const [isNewConversationModalOpen, setIsNewConversationModalOpen] = useState(false)
  const [streamingMessage, setStreamingMessage] = useState('')
  const [isRecording, setIsRecording] = useState(false)

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<any>(null)

  // Store hooks
  const {
    conversations,
    currentConversation,
    messages,
    isLoading,
    fetchConversations,
    fetchConversation,
    createConversation,
    deleteConversation,
    sendMessage,
    clearMessages
  } = useConversationStore()

  const {
    characters,
    fetchUserCharacters
  } = useCharacterStore()

  // Socket service
  const {
    isConnected,
    joinConversation,
    leaveConversation,
    sendChatMessage,
    onMessageReceived,
    onMessageStream,
    onTypingStart,
    onTypingStop
  } = useSocketService()

  // 组件挂载时获取数据
  useEffect(() => {
    fetchConversations()
    fetchUserCharacters()
  }, [fetchConversations, fetchUserCharacters])

  // 处理对话ID变化
  useEffect(() => {
    if (conversationId) {
      fetchConversation(conversationId)
      if (isConnected) {
        joinConversation(conversationId)
      }
    } else {
      clearMessages()
    }

    return () => {
      if (conversationId && isConnected) {
        leaveConversation(conversationId)
      }
    }
  }, [conversationId, isConnected, fetchConversation, joinConversation, leaveConversation, clearMessages])

  // Socket事件监听
  useEffect(() => {
    const unsubscribeMessage = onMessageReceived((message: Message) => {
      // 消息已通过store处理
      setIsTyping(false)
      setStreamingMessage('')
    })

    const unsubscribeStream = onMessageStream((data: { messageId: string; chunk: string; isComplete: boolean }) => {
      if (data.isComplete) {
        setStreamingMessage('')
        setIsTyping(false)
      } else {
        setStreamingMessage(prev => prev + data.chunk)
      }
    })

    const unsubscribeTypingStart = onTypingStart(() => {
      setIsTyping(true)
    })

    const unsubscribeTypingStop = onTypingStop(() => {
      setIsTyping(false)
    })

    return () => {
      unsubscribeMessage()
      unsubscribeStream()
      unsubscribeTypingStart()
      unsubscribeTypingStop()
    }
  }, [onMessageReceived, onMessageStream, onTypingStart, onTypingStop])

  // 自动滚动到底部
  useEffect(() => {
    scrollToBottom()
  }, [messages, streamingMessage])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  // 创建新对话
  const handleCreateConversation = async (characterId: string) => {
    try {
      const character = characters.find(c => c._id === characterId)
      if (!character) {
        message.error('未找到选择的数字人')
        return
      }

      const conversation = await createConversation({
        characterId,
        title: `与${character.name}的对话`
      })

      setIsNewConversationModalOpen(false)
      navigate(`/chat/${conversation._id}`)
      message.success('对话创建成功')
    } catch (error) {
      message.error(error instanceof Error ? error.message : '创建对话失败')
    }
  }

  // 发送消息
  const handleSendMessage = async () => {
    if (!messageInput.trim() || !currentConversation) {
      return
    }

    const content = messageInput.trim()
    setMessageInput('')

    try {
      if (isConnected) {
        // 使用WebSocket发送
        sendChatMessage({
          conversationId: currentConversation._id,
          content,
          type: 'text'
        })
      } else {
        // 使用HTTP API发送
        await sendMessage(currentConversation._id, content)
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '发送消息失败')
      setMessageInput(content) // 恢复输入内容
    }
  }

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // 删除对话
  const handleDeleteConversation = async (id: string) => {
    try {
      await deleteConversation(id)
      message.success('对话删除成功')
      if (conversationId === id) {
        navigate('/chat')
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '删除对话失败')
    }
  }

  // 语音录制（占位符）
  const handleVoiceRecord = () => {
    if (isRecording) {
      setIsRecording(false)
      message.info('语音录制功能即将上线')
    } else {
      setIsRecording(true)
      // 模拟录制过程
      setTimeout(() => {
        setIsRecording(false)
      }, 3000)
    }
  }

  // 渲染消息项
  const renderMessage = (message: Message) => {
    const isUser = message.role === 'user'
    const isSystem = message.role === 'system'

    if (isSystem) {
      return (
        <div key={message.id} style={{ textAlign: 'center', margin: '16px 0' }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {message.content}
          </Text>
        </div>
      )
    }

    return (
      <div
        key={message.id}
        style={{
          display: 'flex',
          justifyContent: isUser ? 'flex-end' : 'flex-start',
          marginBottom: 16,
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: isUser ? 'row-reverse' : 'row',
            alignItems: 'flex-start',
            maxWidth: '70%',
            gap: 8,
          }}
        >
          <Avatar
            size={32}
            icon={isUser ? <UserOutlined /> : <RobotOutlined />}
            src={isUser ? undefined : currentConversation?.character?.avatar}
            style={{
              backgroundColor: isUser ? '#1890ff' : '#52c41a',
              flexShrink: 0,
            }}
          />
          <div
            style={{
              backgroundColor: isUser ? '#1890ff' : '#f5f5f5',
              color: isUser ? '#fff' : '#000',
              padding: '8px 12px',
              borderRadius: 8,
              maxWidth: '100%',
              wordBreak: 'break-word',
            }}
          >
            <div>{message.content}</div>
            <div
              style={{
                fontSize: 11,
                opacity: 0.7,
                marginTop: 4,
                textAlign: 'right',
              }}
            >
              {new Date(message.timestamp).toLocaleTimeString()}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 对话列表项操作菜单
  const getConversationMenuItems = (conversation: Conversation) => [
    {
      key: 'edit',
      icon: <EditOutlined />,
      label: '重命名',
      onClick: () => {
        // TODO: 实现重命名功能
        message.info('重命名功能即将上线')
      },
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: '删除',
      danger: true,
      onClick: () => {
        Modal.confirm({
          title: '确认删除',
          content: '删除后无法恢复，确定要删除这个对话吗？',
          onOk: () => handleDeleteConversation(conversation._id),
        })
      },
    },
  ]

  return (
    <Row style={{ height: 'calc(100vh - 112px)' }} gutter={16}>
      {/* 左侧对话列表 */}
      <Col span={6}>
        <Card
          title="对话列表"
          style={{ height: '100%' }}
          extra={
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="small"
              onClick={() => setIsNewConversationModalOpen(true)}
            >
              新对话
            </Button>
          }
        >
          <div style={{ height: 'calc(100% - 60px)', overflowY: 'auto' }}>
            {conversations.length === 0 ? (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无对话"
                style={{ marginTop: 40 }}
              >
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setIsNewConversationModalOpen(true)}
                >
                  开始对话
                </Button>
              </Empty>
            ) : (
              <List
                dataSource={conversations}
                renderItem={(conversation) => (
                  <List.Item
                    style={{
                      padding: '12px 8px',
                      cursor: 'pointer',
                      backgroundColor: conversationId === conversation._id ? '#f0f8ff' : 'transparent',
                      borderRadius: 6,
                      marginBottom: 4,
                    }}
                    onClick={() => navigate(`/chat/${conversation._id}`)}
                    actions={[
                      <Dropdown
                        menu={{ items: getConversationMenuItems(conversation) }}
                        trigger={['click']}
                        key="more"
                      >
                        <Button type="text" icon={<MoreOutlined />} size="small" />
                      </Dropdown>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          src={conversation.character?.avatar}
                          icon={<RobotOutlined />}
                          size={32}
                        />
                      }
                      title={
                        <Text
                          ellipsis
                          style={{
                            fontSize: 14,
                            fontWeight: conversationId === conversation._id ? 500 : 400
                          }}
                        >
                          {conversation.title}
                        </Text>
                      }
                      description={
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {conversation.character?.name} • {new Date(conversation.updatedAt).toLocaleDateString()}
                        </Text>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </div>
        </Card>
      </Col>

      {/* 右侧聊天区域 */}
      <Col span={18}>
        <Card style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {currentConversation ? (
            <>
              {/* 聊天头部 */}
              <div
                style={{
                  padding: '12px 16px',
                  borderBottom: '1px solid #f0f0f0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                  <Avatar
                    src={currentConversation.character?.avatar}
                    icon={<RobotOutlined />}
                    size={40}
                  />
                  <div>
                    <div style={{ fontWeight: 500, fontSize: 16 }}>
                      {currentConversation.character?.name}
                    </div>
                    <div style={{ fontSize: 12, color: '#666' }}>
                      {isConnected ? (
                        <span style={{ color: '#52c41a' }}>● 在线</span>
                      ) : (
                        <span style={{ color: '#faad14' }}>● 连接中...</span>
                      )}
                    </div>
                  </div>
                </div>

                <Space>
                  <Tooltip title="对话设置">
                    <Button type="text" icon={<SettingOutlined />} />
                  </Tooltip>
                  <Tooltip title="对话历史">
                    <Button type="text" icon={<HistoryOutlined />} />
                  </Tooltip>
                </Space>
              </div>

              {/* 消息区域 */}
              <div
                style={{
                  flex: 1,
                  padding: '16px',
                  overflowY: 'auto',
                  backgroundColor: '#fafafa',
                }}
              >
                {isLoading ? (
                  <div style={{ textAlign: 'center', padding: '40px 0' }}>
                    <Spin size="large" tip="加载消息中..." />
                  </div>
                ) : messages.length === 0 ? (
                  <div style={{ textAlign: 'center', padding: '40px 0' }}>
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="暂无消息，开始对话吧！"
                    />
                  </div>
                ) : (
                  <>
                    {messages.map(renderMessage)}

                    {/* 流式消息显示 */}
                    {streamingMessage && (
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'flex-start',
                          marginBottom: 16,
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'flex-start',
                            maxWidth: '70%',
                            gap: 8,
                          }}
                        >
                          <Avatar
                            size={32}
                            icon={<RobotOutlined />}
                            src={currentConversation.character?.avatar}
                            style={{
                              backgroundColor: '#52c41a',
                              flexShrink: 0,
                            }}
                          />
                          <div
                            style={{
                              backgroundColor: '#f5f5f5',
                              color: '#000',
                              padding: '8px 12px',
                              borderRadius: 8,
                              maxWidth: '100%',
                              wordBreak: 'break-word',
                            }}
                          >
                            <div>{streamingMessage}</div>
                            <div style={{ fontSize: 11, opacity: 0.7, marginTop: 4 }}>
                              正在输入...
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* 打字指示器 */}
                    {isTyping && !streamingMessage && (
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'flex-start',
                          marginBottom: 16,
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'flex-start',
                            gap: 8,
                          }}
                        >
                          <Avatar
                            size={32}
                            icon={<RobotOutlined />}
                            src={currentConversation.character?.avatar}
                            style={{
                              backgroundColor: '#52c41a',
                              flexShrink: 0,
                            }}
                          />
                          <div
                            style={{
                              backgroundColor: '#f5f5f5',
                              padding: '8px 12px',
                              borderRadius: 8,
                            }}
                          >
                            <Spin size="small" />
                            <Text type="secondary" style={{ marginLeft: 8, fontSize: 12 }}>
                              正在思考...
                            </Text>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* 输入区域 */}
              <div
                style={{
                  padding: '16px',
                  borderTop: '1px solid #f0f0f0',
                  backgroundColor: '#fff',
                }}
              >
                <div style={{ display: 'flex', gap: 8, alignItems: 'flex-end' }}>
                  <div style={{ flex: 1 }}>
                    <TextArea
                      ref={inputRef}
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="输入消息... (Enter发送，Shift+Enter换行)"
                      autoSize={{ minRows: 1, maxRows: 4 }}
                      style={{ resize: 'none' }}
                    />
                  </div>
                  <Space>
                    <Tooltip title={isRecording ? '停止录音' : '语音输入'}>
                      <Button
                        type={isRecording ? 'primary' : 'default'}
                        icon={isRecording ? <StopOutlined /> : <AudioOutlined />}
                        onClick={handleVoiceRecord}
                        loading={isRecording}
                      />
                    </Tooltip>
                    <Button
                      type="primary"
                      icon={<SendOutlined />}
                      onClick={handleSendMessage}
                      disabled={!messageInput.trim() || isLoading}
                    >
                      发送
                    </Button>
                  </Space>
                </div>
              </div>
            </>
          ) : (
            /* 未选择对话时的欢迎界面 */
            <div
              style={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                textAlign: 'center',
                padding: '40px',
              }}
            >
              <RobotOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 16 }} />
              <Title level={3} style={{ color: '#999', marginBottom: 8 }}>
                欢迎使用AI数字人对话
              </Title>
              <Text type="secondary" style={{ marginBottom: 24 }}>
                选择一个对话开始聊天，或创建新的对话
              </Text>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                onClick={() => setIsNewConversationModalOpen(true)}
              >
                开始新对话
              </Button>
            </div>
          )}
        </Card>
      </Col>

      {/* 创建新对话模态框 */}
      <Modal
        title="创建新对话"
        open={isNewConversationModalOpen}
        onCancel={() => setIsNewConversationModalOpen(false)}
        footer={null}
        width={500}
      >
        <div style={{ padding: '20px 0' }}>
          <Text strong style={{ marginBottom: 16, display: 'block' }}>
            选择数字人：
          </Text>
          <Select
            placeholder="请选择要对话的数字人"
            style={{ width: '100%', marginBottom: 20 }}
            size="large"
            value={selectedCharacter?._id}
            onChange={(value) => {
              const character = characters.find(c => c._id === value)
              setSelectedCharacter(character || null)
            }}
          >
            {characters.map((character) => (
              <Option key={character._id} value={character._id}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Avatar
                    src={character.avatar}
                    icon={<RobotOutlined />}
                    size={24}
                  />
                  <div>
                    <div>{character.name}</div>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {character.description}
                    </Text>
                  </div>
                </div>
              </Option>
            ))}
          </Select>

          {selectedCharacter && (
            <Card size="small" style={{ marginBottom: 20 }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <Avatar
                  src={selectedCharacter.avatar}
                  icon={<RobotOutlined />}
                  size={48}
                />
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: 500, marginBottom: 4 }}>
                    {selectedCharacter.name}
                  </div>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {selectedCharacter.description}
                  </Text>
                  <div style={{ marginTop: 8 }}>
                    {selectedCharacter.config.expertise.slice(0, 3).map((expertise, index) => (
                      <span
                        key={index}
                        style={{
                          display: 'inline-block',
                          backgroundColor: '#f0f0f0',
                          padding: '2px 6px',
                          borderRadius: 4,
                          fontSize: 11,
                          marginRight: 4,
                        }}
                      >
                        {expertise}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          )}

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsNewConversationModalOpen(false)}>
                取消
              </Button>
              <Button
                type="primary"
                disabled={!selectedCharacter}
                onClick={() => selectedCharacter && handleCreateConversation(selectedCharacter._id)}
              >
                开始对话
              </Button>
            </Space>
          </div>
        </div>
      </Modal>
    </Row>
  )
}
