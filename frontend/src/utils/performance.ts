/**
 * 前端性能优化工具
 * 提供各种性能优化功能
 */

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }

    const callNow = immediate && !timeout

    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)

    if (callNow) func(...args)
  }
}

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean = false

  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

// 图片懒加载
export const lazyLoadImage = (img: HTMLImageElement, src: string) => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const target = entry.target as HTMLImageElement
          target.src = src
          target.classList.remove('lazy')
          observer.unobserve(target)
        }
      })
    },
    {
      rootMargin: '50px 0px',
      threshold: 0.01,
    }
  )

  observer.observe(img)
}

// 虚拟滚动Hook
export const useVirtualScroll = (
  items: any[],
  itemHeight: number,
  containerHeight: number
) => {
  const [scrollTop, setScrollTop] = useState(0)
  
  const startIndex = Math.floor(scrollTop / itemHeight)
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  )
  
  const visibleItems = items.slice(startIndex, endIndex)
  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight
  
  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop,
  }
}

// 内存泄漏检测
export const memoryLeakDetector = {
  listeners: new Set<() => void>(),
  
  // 添加清理函数
  addCleanup: (cleanup: () => void) => {
    memoryLeakDetector.listeners.add(cleanup)
  },
  
  // 移除清理函数
  removeCleanup: (cleanup: () => void) => {
    memoryLeakDetector.listeners.delete(cleanup)
  },
  
  // 执行所有清理函数
  cleanup: () => {
    memoryLeakDetector.listeners.forEach(cleanup => {
      try {
        cleanup()
      } catch (error) {
        console.error('清理函数执行失败:', error)
      }
    })
    memoryLeakDetector.listeners.clear()
  }
}

// 性能监控
export const performanceMonitor = {
  // 记录页面加载时间
  recordPageLoad: () => {
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      const metrics = {
        dns: navigation.domainLookupEnd - navigation.domainLookupStart,
        tcp: navigation.connectEnd - navigation.connectStart,
        request: navigation.responseStart - navigation.requestStart,
        response: navigation.responseEnd - navigation.responseStart,
        dom: navigation.domContentLoadedEventEnd - navigation.responseEnd,
        load: navigation.loadEventEnd - navigation.loadEventStart,
        total: navigation.loadEventEnd - navigation.navigationStart,
      }
      
      console.log('页面加载性能指标:', metrics)
      return metrics
    }
  },
  
  // 记录API请求时间
  recordApiCall: (url: string, duration: number) => {
    console.log(`API请求: ${url} - ${duration}ms`)
    
    // 慢请求警告
    if (duration > 3000) {
      console.warn(`慢API请求警告: ${url} - ${duration}ms`)
    }
  },
  
  // 记录组件渲染时间
  recordComponentRender: (componentName: string, duration: number) => {
    if (duration > 16) { // 超过一帧的时间
      console.warn(`组件渲染过慢: ${componentName} - ${duration}ms`)
    }
  },
  
  // 获取内存使用情况
  getMemoryUsage: () => {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024),
      }
    }
    return null
  }
}

// 缓存管理
export class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  
  // 设置缓存
  set(key: string, data: any, ttl: number = 300000) { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    })
  }
  
  // 获取缓存
  get(key: string) {
    const item = this.cache.get(key)
    
    if (!item) return null
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }
  
  // 删除缓存
  delete(key: string) {
    this.cache.delete(key)
  }
  
  // 清空缓存
  clear() {
    this.cache.clear()
  }
  
  // 获取缓存大小
  size() {
    return this.cache.size
  }
  
  // 清理过期缓存
  cleanup() {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

// 全局缓存实例
export const globalCache = new CacheManager()

// 定期清理过期缓存
if (typeof window !== 'undefined') {
  setInterval(() => {
    globalCache.cleanup()
  }, 60000) // 每分钟清理一次
}

// 资源预加载
export const preloadResource = (url: string, type: 'script' | 'style' | 'image' = 'script') => {
  if (typeof document === 'undefined') return
  
  const link = document.createElement('link')
  link.rel = 'preload'
  link.href = url
  
  switch (type) {
    case 'script':
      link.as = 'script'
      break
    case 'style':
      link.as = 'style'
      break
    case 'image':
      link.as = 'image'
      break
  }
  
  document.head.appendChild(link)
}

// 代码分割辅助函数
export const loadComponent = async (importFunc: () => Promise<any>) => {
  try {
    const start = performance.now()
    const module = await importFunc()
    const duration = performance.now() - start
    
    performanceMonitor.recordComponentRender('DynamicImport', duration)
    
    return module
  } catch (error) {
    console.error('动态导入失败:', error)
    throw error
  }
}

// 批量处理函数
export const batchProcess = <T>(
  items: T[],
  processor: (item: T) => Promise<any>,
  batchSize: number = 10,
  delay: number = 0
): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const results: any[] = []
    let currentIndex = 0
    
    const processBatch = async () => {
      const batch = items.slice(currentIndex, currentIndex + batchSize)
      
      if (batch.length === 0) {
        resolve(results)
        return
      }
      
      try {
        const batchResults = await Promise.all(batch.map(processor))
        results.push(...batchResults)
        currentIndex += batchSize
        
        if (delay > 0) {
          setTimeout(processBatch, delay)
        } else {
          processBatch()
        }
      } catch (error) {
        reject(error)
      }
    }
    
    processBatch()
  })
}

// Web Worker 辅助函数
export const createWorker = (workerFunction: Function) => {
  const blob = new Blob([`(${workerFunction.toString()})()`], {
    type: 'application/javascript',
  })
  
  return new Worker(URL.createObjectURL(blob))
}

// 性能优化Hook
export const usePerformanceOptimization = () => {
  useEffect(() => {
    // 页面加载完成后记录性能指标
    const handleLoad = () => {
      setTimeout(() => {
        performanceMonitor.recordPageLoad()
      }, 0)
    }
    
    if (document.readyState === 'complete') {
      handleLoad()
    } else {
      window.addEventListener('load', handleLoad)
    }
    
    // 监控内存使用
    const memoryInterval = setInterval(() => {
      const memory = performanceMonitor.getMemoryUsage()
      if (memory && memory.used > 100) { // 超过100MB警告
        console.warn('内存使用过高:', memory)
      }
    }, 30000) // 每30秒检查一次
    
    return () => {
      window.removeEventListener('load', handleLoad)
      clearInterval(memoryInterval)
    }
  }, [])
}
