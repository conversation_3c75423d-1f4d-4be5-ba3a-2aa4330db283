/**
 * 工具函数测试
 * 测试各种辅助函数的功能
 */

describe('工具函数测试', () => {
  it('应该通过基本测试', () => {
    expect(1 + 1).toBe(2)
  })

  it('应该正确处理字符串', () => {
    const str = 'Hello World'
    expect(str.toLowerCase()).toBe('hello world')
    expect(str.toUpperCase()).toBe('HELLO WORLD')
  })

  it('应该正确处理数组', () => {
    const arr = [1, 2, 3, 4, 5]
    expect(arr.length).toBe(5)
    expect(arr.includes(3)).toBe(true)
    expect(arr.includes(6)).toBe(false)
  })

  it('应该正确处理对象', () => {
    const obj = { name: '测试', age: 25 }
    expect(obj.name).toBe('测试')
    expect(obj.age).toBe(25)
    expect(Object.keys(obj)).toEqual(['name', 'age'])
  })
})

// 测试日期格式化函数
describe('日期格式化测试', () => {
  it('应该正确格式化日期', () => {
    const date = new Date('2024-01-01T00:00:00Z')
    expect(date.getFullYear()).toBe(2024)
    expect(date.getMonth()).toBe(0) // 月份从0开始
    expect(date.getDate()).toBe(1)
  })
})

// 测试数据验证函数
describe('数据验证测试', () => {
  it('应该验证邮箱格式', () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    
    expect(emailRegex.test('<EMAIL>')).toBe(true)
    expect(emailRegex.test('<EMAIL>')).toBe(true)
    expect(emailRegex.test('invalid-email')).toBe(false)
    expect(emailRegex.test('test@')).toBe(false)
    expect(emailRegex.test('@example.com')).toBe(false)
  })

  it('应该验证密码强度', () => {
    const isStrongPassword = (password: string): boolean => {
      return password.length >= 8 && 
             /[A-Z]/.test(password) && 
             /[a-z]/.test(password) && 
             /[0-9]/.test(password)
    }

    expect(isStrongPassword('Password123')).toBe(true)
    expect(isStrongPassword('StrongPass1')).toBe(true)
    expect(isStrongPassword('weak')).toBe(false)
    expect(isStrongPassword('password')).toBe(false)
    expect(isStrongPassword('PASSWORD123')).toBe(false)
    expect(isStrongPassword('Password')).toBe(false)
  })
})

// 测试本地存储工具
describe('本地存储测试', () => {
  beforeEach(() => {
    // 清空localStorage
    localStorage.clear()
  })

  it('应该能够存储和读取数据', () => {
    const key = 'testKey'
    const value = 'testValue'
    
    localStorage.setItem(key, value)
    expect(localStorage.getItem(key)).toBe(value)
  })

  it('应该能够存储和读取JSON数据', () => {
    const key = 'testObject'
    const value = { name: '测试', id: 123 }
    
    localStorage.setItem(key, JSON.stringify(value))
    const retrieved = JSON.parse(localStorage.getItem(key) || '{}')
    
    expect(retrieved).toEqual(value)
    expect(retrieved.name).toBe('测试')
    expect(retrieved.id).toBe(123)
  })

  it('应该正确处理不存在的键', () => {
    expect(localStorage.getItem('nonexistent')).toBeNull()
  })
})
