import { Spin, SpinProps } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'

interface LoadingSpinnerProps extends SpinProps {
  tip?: string
  overlay?: boolean
  fullScreen?: boolean
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  tip = '加载中...',
  overlay = false,
  fullScreen = false,
  size = 'default',
  ...props
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />

  const spinner = (
    <Spin
      indicator={antIcon}
      tip={tip}
      size={size}
      {...props}
    />
  )

  if (fullScreen) {
    return (
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          zIndex: 9999,
        }}
      >
        {spinner}
      </div>
    )
  }

  if (overlay) {
    return (
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          zIndex: 1000,
        }}
      >
        {spinner}
      </div>
    )
  }

  return spinner
}
