import { useEffect, useCallback, useRef } from 'react'
import { useAuthStore } from '@stores/authStore'
import { socketService } from '@services/socketService'
import type { Message } from '@types/index'

/**
 * WebSocket服务Hook
 * 提供WebSocket连接管理和事件处理
 */
export const useSocketService = () => {
  const { token, user } = useAuthStore()
  const isConnectedRef = useRef(false)
  const eventListenersRef = useRef<Map<string, Function[]>>(new Map())

  // 连接WebSocket
  useEffect(() => {
    if (token && user && !isConnectedRef.current) {
      socketService.connect(token)
      isConnectedRef.current = true
    }

    return () => {
      if (isConnectedRef.current) {
        socketService.disconnect()
        isConnectedRef.current = false
      }
    }
  }, [token, user])

  // 添加事件监听器
  const addEventListener = useCallback((event: string, callback: Function) => {
    if (!eventListenersRef.current.has(event)) {
      eventListenersRef.current.set(event, [])
    }
    eventListenersRef.current.get(event)!.push(callback)
    socketService.on(event, callback)

    // 返回取消监听的函数
    return () => {
      const listeners = eventListenersRef.current.get(event)
      if (listeners) {
        const index = listeners.indexOf(callback)
        if (index > -1) {
          listeners.splice(index, 1)
        }
      }
      socketService.off(event, callback)
    }
  }, [])

  // 清理所有事件监听器
  useEffect(() => {
    return () => {
      eventListenersRef.current.forEach((listeners, event) => {
        listeners.forEach(callback => {
          socketService.off(event, callback)
        })
      })
      eventListenersRef.current.clear()
    }
  }, [])

  // WebSocket操作方法
  const joinConversation = useCallback((conversationId: string) => {
    socketService.joinConversation(conversationId)
  }, [])

  const leaveConversation = useCallback((conversationId: string) => {
    socketService.leaveConversation(conversationId)
  }, [])

  const sendChatMessage = useCallback((data: {
    conversationId: string
    content: string
    type: 'text' | 'voice'
  }) => {
    socketService.sendMessage({
      conversationId: data.conversationId,
      content: data.content,
      useRAG: true
    })
  }, [])

  const startTyping = useCallback((conversationId: string) => {
    socketService.startTyping(conversationId)
  }, [])

  const stopTyping = useCallback((conversationId: string) => {
    socketService.stopTyping(conversationId)
  }, [])

  // 事件监听Hook
  const onMessageReceived = useCallback((callback: (message: Message) => void) => {
    return addEventListener('message_received', callback)
  }, [addEventListener])

  const onMessageStream = useCallback((callback: (data: {
    messageId: string
    chunk: string
    isComplete: boolean
  }) => void) => {
    return addEventListener('message_stream', callback)
  }, [addEventListener])

  const onTypingStart = useCallback((callback: () => void) => {
    return addEventListener('typing_start', callback)
  }, [addEventListener])

  const onTypingStop = useCallback((callback: () => void) => {
    return addEventListener('typing_stop', callback)
  }, [addEventListener])

  const onConnected = useCallback((callback: () => void) => {
    return addEventListener('connected', callback)
  }, [addEventListener])

  const onDisconnected = useCallback((callback: (reason: string) => void) => {
    return addEventListener('disconnected', callback)
  }, [addEventListener])

  const onError = useCallback((callback: (error: any) => void) => {
    return addEventListener('error', callback)
  }, [addEventListener])

  return {
    // 状态
    isConnected: socketService.isSocketConnected(),
    socketId: socketService.getSocketId(),
    
    // 操作方法
    joinConversation,
    leaveConversation,
    sendChatMessage,
    startTyping,
    stopTyping,
    
    // 事件监听
    onMessageReceived,
    onMessageStream,
    onTypingStart,
    onTypingStop,
    onConnected,
    onDisconnected,
    onError,
    
    // 工具方法
    reconnect: socketService.reconnect.bind(socketService),
    getPing: socketService.getPing.bind(socketService),
  }
}

export default useSocketService
