import http from 'k6/http'
import { check, sleep } from 'k6'
import { Rate } from 'k6/metrics'

// 自定义指标
const errorRate = new Rate('errors')

// 测试配置
export const options = {
  stages: [
    { duration: '2m', target: 10 }, // 预热阶段
    { duration: '5m', target: 50 }, // 负载增加
    { duration: '10m', target: 100 }, // 稳定负载
    { duration: '5m', target: 200 }, // 峰值负载
    { duration: '2m', target: 0 }, // 冷却阶段
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95%的请求响应时间小于500ms
    http_req_failed: ['rate<0.1'], // 错误率小于10%
    errors: ['rate<0.1'],
  },
}

// 测试数据
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3001'
const TEST_USER = {
  username: 'loadtest_user',
  email: '<EMAIL>',
  password: 'password123',
}

let authToken = ''

// 设置阶段
export function setup() {
  console.log('开始性能测试设置...')
  
  // 注册测试用户
  const registerResponse = http.post(`${BASE_URL}/api/auth/register`, JSON.stringify(TEST_USER), {
    headers: { 'Content-Type': 'application/json' },
  })
  
  if (registerResponse.status === 201) {
    const data = JSON.parse(registerResponse.body)
    authToken = data.data.token
    console.log('测试用户注册成功')
  } else {
    console.log('测试用户注册失败，尝试登录...')
    
    // 尝试登录
    const loginResponse = http.post(`${BASE_URL}/api/auth/login`, JSON.stringify({
      email: TEST_USER.email,
      password: TEST_USER.password,
    }), {
      headers: { 'Content-Type': 'application/json' },
    })
    
    if (loginResponse.status === 200) {
      const data = JSON.parse(loginResponse.body)
      authToken = data.data.token
      console.log('测试用户登录成功')
    }
  }
  
  return { authToken }
}

// 主测试函数
export default function(data) {
  const token = data.authToken || authToken
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  }
  
  // 测试场景权重
  const scenarios = [
    { name: 'getUserProfile', weight: 20 },
    { name: 'getCharacters', weight: 30 },
    { name: 'getConversations', weight: 25 },
    { name: 'createConversation', weight: 15 },
    { name: 'sendMessage', weight: 10 },
  ]
  
  // 随机选择测试场景
  const scenario = scenarios[Math.floor(Math.random() * scenarios.length)]
  
  switch (scenario.name) {
    case 'getUserProfile':
      testGetUserProfile(headers)
      break
    case 'getCharacters':
      testGetCharacters(headers)
      break
    case 'getConversations':
      testGetConversations(headers)
      break
    case 'createConversation':
      testCreateConversation(headers)
      break
    case 'sendMessage':
      testSendMessage(headers)
      break
  }
  
  sleep(1) // 模拟用户思考时间
}

// 测试获取用户信息
function testGetUserProfile(headers) {
  const response = http.get(`${BASE_URL}/api/auth/profile`, { headers })
  
  const success = check(response, {
    'getUserProfile: status is 200': (r) => r.status === 200,
    'getUserProfile: response time < 200ms': (r) => r.timings.duration < 200,
    'getUserProfile: has user data': (r) => {
      const data = JSON.parse(r.body)
      return data.success && data.data.username
    },
  })
  
  errorRate.add(!success)
}

// 测试获取数字人列表
function testGetCharacters(headers) {
  const response = http.get(`${BASE_URL}/api/characters?page=1&limit=20`, { headers })
  
  const success = check(response, {
    'getCharacters: status is 200': (r) => r.status === 200,
    'getCharacters: response time < 300ms': (r) => r.timings.duration < 300,
    'getCharacters: has characters data': (r) => {
      const data = JSON.parse(r.body)
      return data.success && Array.isArray(data.data.items)
    },
  })
  
  errorRate.add(!success)
}

// 测试获取对话列表
function testGetConversations(headers) {
  const response = http.get(`${BASE_URL}/api/conversations?page=1&limit=20`, { headers })
  
  const success = check(response, {
    'getConversations: status is 200': (r) => r.status === 200,
    'getConversations: response time < 300ms': (r) => r.timings.duration < 300,
    'getConversations: has conversations data': (r) => {
      const data = JSON.parse(r.body)
      return data.success && Array.isArray(data.data.items)
    },
  })
  
  errorRate.add(!success)
}

// 测试创建对话
function testCreateConversation(headers) {
  // 首先获取一个数字人ID
  const charactersResponse = http.get(`${BASE_URL}/api/characters/public?limit=1`, { headers })
  
  if (charactersResponse.status !== 200) {
    errorRate.add(true)
    return
  }
  
  const charactersData = JSON.parse(charactersResponse.body)
  if (!charactersData.data.items.length) {
    errorRate.add(true)
    return
  }
  
  const characterId = charactersData.data.items[0]._id
  
  const response = http.post(`${BASE_URL}/api/conversations`, JSON.stringify({
    characterId,
    title: `Load Test Conversation ${Date.now()}`,
  }), { headers })
  
  const success = check(response, {
    'createConversation: status is 201': (r) => r.status === 201,
    'createConversation: response time < 500ms': (r) => r.timings.duration < 500,
    'createConversation: has conversation data': (r) => {
      const data = JSON.parse(r.body)
      return data.success && data.data._id
    },
  })
  
  errorRate.add(!success)
}

// 测试发送消息
function testSendMessage(headers) {
  // 首先获取一个对话ID
  const conversationsResponse = http.get(`${BASE_URL}/api/conversations?limit=1`, { headers })
  
  if (conversationsResponse.status !== 200) {
    errorRate.add(true)
    return
  }
  
  const conversationsData = JSON.parse(conversationsResponse.body)
  if (!conversationsData.data.items.length) {
    errorRate.add(true)
    return
  }
  
  const conversationId = conversationsData.data.items[0]._id
  
  const response = http.post(`${BASE_URL}/api/conversations/${conversationId}/messages`, JSON.stringify({
    content: `Load test message ${Date.now()}`,
    useRAG: false,
  }), { headers })
  
  const success = check(response, {
    'sendMessage: status is 200': (r) => r.status === 200,
    'sendMessage: response time < 2000ms': (r) => r.timings.duration < 2000, // AI响应可能较慢
    'sendMessage: has message data': (r) => {
      const data = JSON.parse(r.body)
      return data.success && data.data.userMessage
    },
  })
  
  errorRate.add(!success)
}

// 清理阶段
export function teardown(data) {
  console.log('性能测试清理完成')
}
