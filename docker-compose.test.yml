version: '3.8'

services:
  # MongoDB测试数据库
  mongodb:
    image: mongo:7.0
    container_name: ai-chat-test-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: ai_chat_test
    ports:
      - "27017:27017"
    volumes:
      - mongodb_test_data:/data/db
    networks:
      - ai-chat-test-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis测试缓存
  redis:
    image: redis:7.2-alpine
    container_name: ai-chat-test-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_test_data:/data
    networks:
      - ai-chat-test-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s

  # 后端测试服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: base
    container_name: ai-chat-test-backend
    restart: unless-stopped
    environment:
      NODE_ENV: test
      PORT: 3001
      
      # 数据库配置
      MONGODB_URI: *****************************************/ai_chat_test?authSource=admin
      
      # Redis配置
      REDIS_URL: redis://:redis123@redis:6379/15
      
      # JWT配置
      JWT_SECRET: test-jwt-secret-for-testing-only
      JWT_EXPIRES_IN: 1h
      
      # 加密配置
      ENCRYPTION_KEY: test-encryption-key-32-characters
      
      # AI服务配置（测试用）
      OPENAI_API_KEY: test-openai-key
      ANTHROPIC_API_KEY: test-anthropic-key
      PINECONE_API_KEY: test-pinecone-key
      
      # 文件上传配置
      UPLOAD_DIR: /app/uploads
      MAX_FILE_SIZE: 10485760
      
      # 应用配置
      APP_NAME: AI数字人对话系统测试
      APP_URL: http://localhost:3000
      
      # 日志配置
      LOG_LEVEL: error
      
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - uploads_test_data:/app/uploads
    networks:
      - ai-chat-test-network
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    command: npm run test:watch

  # 前端测试服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: builder
    container_name: ai-chat-test-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: test
      VITE_API_URL: http://localhost:3001
      VITE_WS_URL: http://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - ai-chat-test-network
    depends_on:
      backend:
        condition: service_healthy
    command: npm run test:watch

  # 测试数据库管理工具
  mongo-express:
    image: mongo-express:latest
    container_name: ai-chat-test-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    networks:
      - ai-chat-test-network
    depends_on:
      - mongodb
    profiles:
      - tools

  # Redis管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ai-chat-test-redis-commander
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379:15:redis123
    networks:
      - ai-chat-test-network
    depends_on:
      - redis
    profiles:
      - tools

  # 测试报告服务
  test-reporter:
    image: nginx:alpine
    container_name: ai-chat-test-reporter
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./reports:/usr/share/nginx/html:ro
      - ./scripts/nginx-test.conf:/etc/nginx/nginx.conf:ro
    networks:
      - ai-chat-test-network
    profiles:
      - reports

volumes:
  mongodb_test_data:
    driver: local
  redis_test_data:
    driver: local
  uploads_test_data:
    driver: local

networks:
  ai-chat-test-network:
    driver: bridge
