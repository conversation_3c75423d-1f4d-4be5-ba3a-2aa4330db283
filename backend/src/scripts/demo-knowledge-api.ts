#!/usr/bin/env ts-node

/**
 * 知识库 API 集成功能演示脚本
 * 
 * 此脚本演示了知识库 API 调用和降级策略的功能
 */

import dotenv from 'dotenv'
import { KnowledgeApiClient } from '../services/knowledgeApiClient'
import { KnowledgeFallbackService } from '../services/knowledgeFallbackService'
import { EnhancedKnowledgeService } from '../services/enhancedKnowledgeService'
import { knowledgeApiConfig } from '../config/knowledgeApi'
import { logger } from '../utils/logger'
import {
  KnowledgeApiConfig,
  FallbackConfig,
  KnowledgeApiRequest,
} from '../types'

// 加载环境变量
dotenv.config()

/**
 * 演示知识库 API 客户端功能
 */
async function demoApiClient() {
  console.log('\n=== 知识库 API 客户端演示 ===')
  
  try {
    // 创建模拟配置
    const mockConfig: KnowledgeApiConfig = {
      enabled: true,
      baseUrl: 'https://httpbin.org', // 使用 httpbin 作为测试端点
      apiKey: 'demo-api-key',
      apiSecret: 'demo-api-secret',
      timeout: 10000,
      retryAttempts: 2,
      retryDelay: 1000,
    }

    const client = new KnowledgeApiClient(mockConfig)
    
    console.log('✅ API 客户端创建成功')
    console.log('📋 配置有效性:', client.isConfigValid())
    console.log('🏥 服务状态:', client.getStatus())

    // 演示健康检查（会失败，因为 httpbin 没有 /health 端点）
    console.log('\n--- 健康检查演示 ---')
    const isHealthy = await client.checkHealth()
    console.log('🏥 健康状态:', isHealthy)

  } catch (error) {
    console.error('❌ API 客户端演示失败:', error instanceof Error ? error.message : String(error))
  }
}

/**
 * 演示降级策略功能
 */
async function demoFallbackService() {
  console.log('\n=== 降级策略服务演示 ===')
  
  try {
    // 创建降级配置
    const fallbackConfig: FallbackConfig = {
      enabled: true,
      strategy: 'empty_response',
      cacheTimeout: 3600,
    }

    const fallbackService = new KnowledgeFallbackService(fallbackConfig)
    
    console.log('✅ 降级服务创建成功')
    console.log('📋 配置有效性:', fallbackService.isConfigValid())
    
    // 演示降级判断
    const networkError = new Error('网络连接失败')
    const shouldFallback = fallbackService.shouldFallback(networkError)
    console.log('🔄 是否需要降级:', shouldFallback)

    // 演示空响应降级
    const request: KnowledgeApiRequest = {
      query: '什么是人工智能？',
      topK: 5,
      scoreThreshold: 0.7,
    }

    console.log('\n--- 执行降级策略 ---')
    const fallbackResponse = await fallbackService.executeFallback(request, networkError)
    console.log('📝 降级响应:', {
      source: fallbackResponse.source,
      confidence: fallbackResponse.confidence,
      content: fallbackResponse.content.substring(0, 100) + '...',
    })

    // 显示降级统计
    const stats = fallbackService.getFallbackStats()
    console.log('📊 降级统计:', stats)

  } catch (error) {
    console.error('❌ 降级服务演示失败:', error instanceof Error ? error.message : String(error))
  }
}

/**
 * 演示增强知识库服务
 */
async function demoEnhancedService() {
  console.log('\n=== 增强知识库服务演示 ===')
  
  try {
    const enhancedService = new EnhancedKnowledgeService()
    
    console.log('✅ 增强服务创建成功')
    
    // 获取服务状态
    const status = enhancedService.getServiceStatus()
    console.log('📊 服务状态:', {
      initialized: status.initialized,
      apiEnabled: status.apiEnabled,
      fallbackEnabled: status.fallbackEnabled,
    })

    // 健康检查
    console.log('\n--- 健康检查 ---')
    const health = await enhancedService.healthCheck()
    console.log('🏥 健康状态:', health.status)
    console.log('📋 详细信息:', health.details)

    // 演示搜索功能（会触发降级）
    console.log('\n--- 搜索演示 ---')
    try {
      const searchResult = await enhancedService.searchKnowledge(
        '什么是机器学习？',
        {
          knowledgeBaseId: 'demo-kb-001',
          topK: 3,
          scoreThreshold: 0.8,
          userId: 'demo-user-001',
        }
      )
      
      console.log('🔍 搜索结果:', {
        source: searchResult.source,
        confidence: searchResult.confidence,
        content: searchResult.content.substring(0, 150) + '...',
      })
    } catch (searchError) {
      console.log('⚠️  搜索失败（预期行为）:', searchError instanceof Error ? searchError.message : String(searchError))
    }

  } catch (error) {
    console.error('❌ 增强服务演示失败:', error instanceof Error ? error.message : String(error))
  }
}

/**
 * 演示配置管理
 */
function demoConfigManagement() {
  console.log('\n=== 配置管理演示 ===')
  
  try {
    console.log('📋 当前配置摘要:', knowledgeApiConfig.getConfigSummary())
    console.log('🔧 API 启用状态:', knowledgeApiConfig.isApiEnabled())
    console.log('🔄 降级启用状态:', knowledgeApiConfig.isFallbackEnabled())
    
    // 演示配置更新
    console.log('\n--- 配置更新演示 ---')
    const originalTimeout = knowledgeApiConfig.getApiConfig().timeout
    
    knowledgeApiConfig.updateApiConfig({
      timeout: 60000,
    })
    
    const newTimeout = knowledgeApiConfig.getApiConfig().timeout
    console.log('⏱️  超时配置更新:', `${originalTimeout}ms -> ${newTimeout}ms`)
    
    // 恢复原始配置
    knowledgeApiConfig.updateApiConfig({
      timeout: originalTimeout,
    })
    
    console.log('✅ 配置已恢复')
    
  } catch (error) {
    console.error('❌ 配置管理演示失败:', error instanceof Error ? error.message : String(error))
  }
}

/**
 * 主演示函数
 */
async function main() {
  console.log('🚀 知识库 API 集成功能演示开始')
  console.log('=' .repeat(50))
  
  try {
    // 演示各个组件
    await demoApiClient()
    await demoFallbackService()
    await demoEnhancedService()
    demoConfigManagement()
    
    console.log('\n' + '='.repeat(50))
    console.log('✅ 演示完成！')
    console.log('\n📝 功能总结:')
    console.log('   • 知识库 API 客户端 - 支持外部 API 调用')
    console.log('   • 降级策略服务 - 自动降级到备用方案')
    console.log('   • 增强知识库服务 - 集成 API 调用和降级')
    console.log('   • 配置管理 - 动态配置更新和验证')
    console.log('   • 健康检查 - 服务状态监控')
    console.log('   • 错误处理 - 完善的错误处理机制')
    console.log('   • 缓存机制 - 提高性能和可用性')
    
  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error instanceof Error ? error.message : String(error))
    process.exit(1)
  }
}

// 运行演示
if (require.main === module) {
  main().catch(error => {
    console.error('💥 演示脚本执行失败:', error)
    process.exit(1)
  })
}

export { main as demoKnowledgeApi }
