import { Response } from 'express'
import { ApiResponse, PaginatedResponse } from '@/types'

/**
 * 成功响应工具函数
 */
export const successResponse = <T>(
  res: Response,
  data?: T,
  message: string = '操作成功',
  statusCode: number = 200
): Response => {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
  }
  
  return res.status(statusCode).json(response)
}

/**
 * 错误响应工具函数
 */
export const errorResponse = (
  res: Response,
  message: string = '操作失败',
  statusCode: number = 500,
  code: string = 'INTERNAL_ERROR',
  details?: any
): Response => {
  const response: ApiResponse = {
    success: false,
    error: {
      code,
      message,
      details,
    },
    timestamp: new Date().toISOString(),
  }
  
  return res.status(statusCode).json(response)
}

/**
 * 分页响应工具函数
 */
export const paginatedResponse = <T>(
  res: Response,
  items: T[],
  total: number,
  page: number,
  limit: number,
  message: string = '获取成功'
): Response => {
  const totalPages = Math.ceil(total / limit)
  
  const response: ApiResponse<PaginatedResponse<T>> = {
    success: true,
    data: {
      items,
      total,
      page,
      limit,
      totalPages,
    },
    message,
    timestamp: new Date().toISOString(),
  }
  
  return res.status(200).json(response)
}

/**
 * 创建响应工具函数
 */
export const createdResponse = <T>(
  res: Response,
  data: T,
  message: string = '创建成功'
): Response => {
  return successResponse(res, data, message, 201)
}

/**
 * 无内容响应工具函数
 */
export const noContentResponse = (res: Response): Response => {
  return res.status(204).send()
}

/**
 * 未找到响应工具函数
 */
export const notFoundResponse = (
  res: Response,
  message: string = '资源不存在'
): Response => {
  return errorResponse(res, message, 404, 'NOT_FOUND')
}

/**
 * 未授权响应工具函数
 */
export const unauthorizedResponse = (
  res: Response,
  message: string = '未授权访问'
): Response => {
  return errorResponse(res, message, 401, 'UNAUTHORIZED')
}

/**
 * 禁止访问响应工具函数
 */
export const forbiddenResponse = (
  res: Response,
  message: string = '禁止访问'
): Response => {
  return errorResponse(res, message, 403, 'FORBIDDEN')
}

/**
 * 验证错误响应工具函数
 */
export const validationErrorResponse = (
  res: Response,
  details: any,
  message: string = '数据验证失败'
): Response => {
  return errorResponse(res, message, 400, 'VALIDATION_ERROR', details)
}

/**
 * 冲突响应工具函数
 */
export const conflictResponse = (
  res: Response,
  message: string = '资源冲突'
): Response => {
  return errorResponse(res, message, 409, 'CONFLICT')
}
