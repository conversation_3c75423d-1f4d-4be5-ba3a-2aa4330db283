import Joi from 'joi'

/**
 * 通用验证规则
 */
export const commonValidation = {
  // 邮箱验证
  email: Joi.string().email().required().messages({
    'string.email': '请输入有效的邮箱地址',
    'any.required': '邮箱是必填项',
  }),

  // 密码验证
  password: Joi.string()
    .min(8)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .required()
    .messages({
      'string.min': '密码至少8位',
      'string.pattern.base': '密码必须包含大小写字母和数字',
      'any.required': '密码是必填项',
    }),

  // 用户名验证
  username: Joi.string()
    .min(3)
    .max(20)
    .pattern(/^[a-zA-Z0-9_]+$/)
    .required()
    .messages({
      'string.min': '用户名至少3位',
      'string.max': '用户名最多20位',
      'string.pattern.base': '用户名只能包含字母、数字和下划线',
      'any.required': '用户名是必填项',
    }),

  // MongoDB ObjectId验证
  objectId: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .required()
    .messages({
      'string.pattern.base': '无效的ID格式',
      'any.required': 'ID是必填项',
    }),

  // 分页参数验证
  pagination: {
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
    search: Joi.string().optional(),
  },
}

/**
 * 用户相关验证规则
 */
export const userValidation = {
  // 用户注册
  register: Joi.object({
    username: commonValidation.username,
    email: commonValidation.email,
    password: commonValidation.password,
    confirmPassword: Joi.string()
      .valid(Joi.ref('password'))
      .required()
      .messages({
        'any.only': '确认密码不匹配',
        'any.required': '确认密码是必填项',
      }),
  }),

  // 用户登录
  login: Joi.object({
    email: commonValidation.email,
    password: Joi.string().required().messages({
      'any.required': '密码是必填项',
    }),
    remember: Joi.boolean().optional(),
  }),

  // 更新用户信息
  updateProfile: Joi.object({
    username: Joi.string()
      .min(3)
      .max(20)
      .pattern(/^[a-zA-Z0-9_]+$/)
      .optional(),
    avatar: Joi.string().uri().optional(),
    preferences: Joi.object({
      language: Joi.string().valid('zh-CN', 'en-US').optional(),
      theme: Joi.string().valid('light', 'dark', 'auto').optional(),
      notifications: Joi.object({
        email: Joi.boolean().optional(),
        browser: Joi.boolean().optional(),
      }).optional(),
    }).optional(),
  }),

  // 修改密码
  changePassword: Joi.object({
    currentPassword: Joi.string().required().messages({
      'any.required': '当前密码是必填项',
    }),
    newPassword: commonValidation.password,
  }),
}

/**
 * 数字人相关验证规则
 */
export const characterValidation = {
  // 创建数字人
  create: Joi.object({
    name: Joi.string().min(1).max(50).required().messages({
      'string.min': '数字人名称不能为空',
      'string.max': '数字人名称最多50个字符',
      'any.required': '数字人名称是必填项',
    }),
    description: Joi.string().max(500).optional(),
    avatar: Joi.string().uri().optional(),
    personality: Joi.string().max(1000).optional(),
    expertise: Joi.array().items(Joi.string().max(50)).max(10).optional(),
    language: Joi.string().valid('zh-CN', 'en-US', 'ja-JP').default('zh-CN'),
    aiModel: Joi.object({
      provider: Joi.string().valid('openai', 'anthropic', 'local').required(),
      model: Joi.string().required(),
      temperature: Joi.number().min(0).max(2).default(0.7),
      maxTokens: Joi.number().min(1).max(4000).default(2000),
      systemPrompt: Joi.string().max(2000).optional(),
    }).required(),
    voice: Joi.object({
      provider: Joi.string().valid('azure', 'aliyun', 'local').default('azure'),
      voiceId: Joi.string().required(),
      speed: Joi.number().min(0.5).max(2).default(1),
      pitch: Joi.number().min(0.5).max(2).default(1),
    }).optional(),
    tags: Joi.array().items(Joi.string().max(20)).max(10).optional(),
    isPublic: Joi.boolean().default(false),
  }),

  // 更新数字人
  update: Joi.object({
    name: Joi.string().min(1).max(50).optional(),
    description: Joi.string().max(500).optional(),
    avatar: Joi.string().uri().optional(),
    personality: Joi.string().max(1000).optional(),
    expertise: Joi.array().items(Joi.string().max(50)).max(10).optional(),
    language: Joi.string().valid('zh-CN', 'en-US', 'ja-JP').optional(),
    aiModel: Joi.object({
      provider: Joi.string().valid('openai', 'anthropic', 'local').optional(),
      model: Joi.string().optional(),
      temperature: Joi.number().min(0).max(2).optional(),
      maxTokens: Joi.number().min(1).max(4000).optional(),
      systemPrompt: Joi.string().max(2000).optional(),
    }).optional(),
    voice: Joi.object({
      provider: Joi.string().valid('azure', 'aliyun', 'local').optional(),
      voiceId: Joi.string().optional(),
      speed: Joi.number().min(0.5).max(2).optional(),
      pitch: Joi.number().min(0.5).max(2).optional(),
    }).optional(),
    tags: Joi.array().items(Joi.string().max(20)).max(10).optional(),
    isPublic: Joi.boolean().optional(),
  }),
}

/**
 * 对话相关验证规则
 */
export const conversationValidation = {
  // 创建对话
  create: Joi.object({
    characterId: commonValidation.objectId,
    title: Joi.string().min(1).max(100).optional(),
  }),

  // 发送消息
  sendMessage: Joi.object({
    content: Joi.string().min(1).max(5000).required().messages({
      'string.min': '消息内容不能为空',
      'string.max': '消息内容最多5000个字符',
      'any.required': '消息内容是必填项',
    }),
    type: Joi.string().valid('text', 'voice').default('text'),
  }),
}

/**
 * 知识库相关验证规则
 */
export const knowledgeValidation = {
  // 创建知识库
  create: Joi.object({
    name: Joi.string().min(1).max(50).required().messages({
      'string.min': '知识库名称不能为空',
      'string.max': '知识库名称最多50个字符',
      'any.required': '知识库名称是必填项',
    }),
    description: Joi.string().max(500).optional(),
    settings: Joi.object({
      chunkSize: Joi.number().min(100).max(2000).default(1000),
      chunkOverlap: Joi.number().min(0).max(500).default(200),
      embeddingModel: Joi.string().default('text-embedding-ada-002'),
    }).optional(),
  }),

  // 更新知识库
  update: Joi.object({
    name: Joi.string().min(1).max(50).optional(),
    description: Joi.string().max(500).optional(),
    settings: Joi.object({
      chunkSize: Joi.number().min(100).max(2000).optional(),
      chunkOverlap: Joi.number().min(0).max(500).optional(),
      embeddingModel: Joi.string().optional(),
    }).optional(),
  }),
}

/**
 * 验证中间件工厂函数
 */
export const createValidationMiddleware = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    })

    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }))

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '数据验证失败',
          details,
        },
        timestamp: new Date().toISOString(),
      })
    }

    req.body = value
    next()
  }
}
