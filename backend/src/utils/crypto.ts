import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import crypto from 'crypto'
import { JWTPayload } from '@/types'

/**
 * 密码加密工具类
 */
export class CryptoUtils {
  /**
   * 加密密码
   */
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12
    return await bcrypt.hash(password, saltRounds)
  }

  /**
   * 验证密码
   */
  static async comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    return await bcrypt.compare(password, hashedPassword)
  }

  /**
   * 生成JWT令牌
   */
  static generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    const secret = process.env.JWT_SECRET
    if (!secret) {
      throw new Error('JWT_SECRET环境变量未设置')
    }

    const expiresIn = process.env.JWT_EXPIRES_IN || '7d'
    
    return jwt.sign(payload, secret, { expiresIn })
  }

  /**
   * 验证JWT令牌
   */
  static verifyToken(token: string): JWTPayload {
    const secret = process.env.JWT_SECRET
    if (!secret) {
      throw new Error('JWT_SECRET环境变量未设置')
    }

    return jwt.verify(token, secret) as JWTPayload
  }

  /**
   * 生成刷新令牌
   */
  static generateRefreshToken(): string {
    return crypto.randomBytes(64).toString('hex')
  }

  /**
   * 生成随机字符串
   */
  static generateRandomString(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex')
  }

  /**
   * 生成UUID
   */
  static generateUUID(): string {
    return crypto.randomUUID()
  }

  /**
   * 加密敏感数据
   */
  static encrypt(text: string): { encrypted: string; iv: string } {
    const algorithm = 'aes-256-cbc'
    const secretKey = process.env.ENCRYPTION_KEY || 'default-encryption-key-change-in-production'
    const key = crypto.scryptSync(secretKey, 'salt', 32)
    const iv = crypto.randomBytes(16)
    
    const cipher = crypto.createCipher(algorithm, key)
    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    return {
      encrypted,
      iv: iv.toString('hex')
    }
  }

  /**
   * 解密敏感数据
   */
  static decrypt(encryptedData: { encrypted: string; iv: string }): string {
    const algorithm = 'aes-256-cbc'
    const secretKey = process.env.ENCRYPTION_KEY || 'default-encryption-key-change-in-production'
    const key = crypto.scryptSync(secretKey, 'salt', 32)
    
    const decipher = crypto.createDecipher(algorithm, key)
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
  }

  /**
   * 生成哈希值
   */
  static generateHash(data: string, algorithm: string = 'sha256'): string {
    return crypto.createHash(algorithm).update(data).digest('hex')
  }

  /**
   * 验证哈希值
   */
  static verifyHash(data: string, hash: string, algorithm: string = 'sha256'): boolean {
    const computedHash = this.generateHash(data, algorithm)
    return computedHash === hash
  }

  /**
   * 生成API密钥
   */
  static generateApiKey(): string {
    const prefix = 'ak_'
    const randomPart = crypto.randomBytes(32).toString('hex')
    return prefix + randomPart
  }

  /**
   * 生成邮箱验证令牌
   */
  static generateEmailVerificationToken(): string {
    return crypto.randomBytes(32).toString('hex')
  }

  /**
   * 生成密码重置令牌
   */
  static generatePasswordResetToken(): string {
    return crypto.randomBytes(32).toString('hex')
  }
}
