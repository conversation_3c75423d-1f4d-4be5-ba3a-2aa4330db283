// 基础类型定义

export interface BaseEntity {
  _id: string
  createdAt: Date
  updatedAt: Date
}

// 用户相关类型
export interface IUser extends BaseEntity {
  username: string
  email: string
  passwordHash: string
  avatar?: string
  role: 'admin' | 'user'
  subscription: {
    plan: 'free' | 'pro' | 'enterprise'
    expiresAt: Date
    tokensUsed: number
    tokensLimit: number
  }
  preferences: {
    language: string
    theme: 'light' | 'dark' | 'auto'
    notifications: {
      email: boolean
      browser: boolean
    }
  }
  isEmailVerified: boolean
  lastLoginAt?: Date
}

// 数字人相关类型
export interface ICharacter extends BaseEntity {
  name: string
  description: string
  avatar?: string
  userId: string
  config: CharacterConfig
  knowledgeBase: KnowledgeBaseConfig
  isPublic: boolean
  tags: string[]
  stats: CharacterStats
}

export interface CharacterConfig {
  personality: string
  expertise: string[]
  language: string
  voice: VoiceConfig
  aiModel: AIModelConfig
}

export interface VoiceConfig {
  provider: 'azure' | 'aliyun' | 'local'
  voiceId: string
  speed: number
  pitch: number
}

export interface AIModelConfig {
  provider: 'openai' | 'anthropic' | 'local'
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
}

export interface KnowledgeBaseConfig {
  enabled: boolean
  vectorStoreId?: string
  documents: DocumentInfo[]
}

export interface DocumentInfo {
  id: string
  name: string
  type: string
  uploadedAt: Date
}

export interface CharacterStats {
  conversationCount: number
  messageCount: number
  lastUsedAt: Date
}

// 对话相关类型
export interface IConversation extends BaseEntity {
  userId: string
  characterId: string
  title: string
  messages: IMessage[]
  status: 'active' | 'archived' | 'deleted'
}

export interface IMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  metadata?: MessageMetadata
}

export interface MessageMetadata {
  tokens?: number
  model?: string
  ragUsed?: boolean
  sources?: string[]
  duration?: number
}

// 知识库相关类型
export interface IKnowledge extends BaseEntity {
  name: string
  description: string
  userId: string
  documents: IDocument[]
  vectorStore: VectorStoreConfig
  settings: KnowledgeSettings
}

export interface IDocument extends BaseEntity {
  name: string
  originalName: string
  type: 'pdf' | 'docx' | 'txt' | 'md' | 'rtf'
  size: number
  url: string
  status: 'processing' | 'completed' | 'failed'
  chunks: number
  uploadedAt: Date
  processedAt?: Date
}

export interface VectorStoreConfig {
  provider: 'pinecone' | 'weaviate'
  indexId: string
  dimensions: number
  totalVectors: number
}

export interface KnowledgeSettings {
  chunkSize: number
  chunkOverlap: number
  embeddingModel: string
}

// 知识库 API 相关类型
export interface KnowledgeApiConfig {
  enabled: boolean
  baseUrl: string
  apiKey: string
  apiSecret?: string
  timeout: number
  retryAttempts: number
  retryDelay: number
}

export interface KnowledgeApiRequest {
  query: string
  knowledgeBaseId?: string
  topK?: number
  scoreThreshold?: number
  filters?: Record<string, any>
  metadata?: Record<string, any>
}

export interface KnowledgeApiResponse {
  success: boolean
  data: {
    results: KnowledgeSearchResult[]
    context: string
    sources: KnowledgeSource[]
    totalResults: number
    processingTime: number
  }
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}

export interface KnowledgeSearchResult {
  id: string
  content: string
  score: number
  metadata: {
    documentId: string
    documentName: string
    chunkIndex: number
    tokens: number
    [key: string]: any
  }
}

export interface KnowledgeSource {
  documentId: string
  documentName: string
  documentType: string
  relevantChunks: number
  url?: string
}

export interface KnowledgeApiError extends Error {
  code: string
  statusCode?: number
  details?: any
  isRetryable: boolean
}

// 降级策略相关类型
export interface FallbackConfig {
  enabled: boolean
  strategy: 'ai_model' | 'cached_response' | 'empty_response'
  aiModelConfig?: AIModelConfig
  cacheTimeout?: number
}

export interface FallbackResponse {
  content: string
  source: 'knowledge_api' | 'ai_model' | 'cache' | 'empty'
  confidence: number
  metadata?: Record<string, any>
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 请求类型
export interface LoginRequest {
  email: string
  password: string
  remember?: boolean
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  confirmPassword: string
}

export interface CreateCharacterRequest {
  name: string
  description: string
  avatar?: string
  personality: string
  expertise: string[]
  language: string
  aiModel: AIModelConfig
  voice: VoiceConfig
  tags: string[]
  isPublic: boolean
}

// JWT载荷类型
export interface JWTPayload {
  userId: string
  email: string
  role: string
  iat: number
  exp: number
}

// 分页参数类型
export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
  filters?: Record<string, any>
}

// 文件上传类型
export interface FileUploadInfo {
  originalName: string
  filename: string
  mimetype: string
  size: number
  path: string
}

// Socket事件类型
export interface SocketEvents {
  join_conversation: (data: { conversationId: string; characterId: string }) => void
  leave_conversation: (data: { conversationId: string }) => void
  send_message: (data: { conversationId: string; content: string; type: 'text' | 'voice' }) => void
  message_received: (data: IMessage) => void
  message_stream: (data: { messageId: string; chunk: string; isComplete: boolean }) => void
  typing_start: (data: { userId: string; characterId: string }) => void
  typing_stop: (data: { userId: string; characterId: string }) => void
  error: (data: { message: string; code?: string }) => void
}

// 环境变量类型
export interface EnvConfig {
  NODE_ENV: string
  PORT: number
  MONGODB_URI: string
  REDIS_URL: string
  JWT_SECRET: string
  JWT_EXPIRES_IN: string
  OPENAI_API_KEY?: string
  ANTHROPIC_API_KEY?: string
  PINECONE_API_KEY?: string
  PINECONE_ENVIRONMENT?: string
}
