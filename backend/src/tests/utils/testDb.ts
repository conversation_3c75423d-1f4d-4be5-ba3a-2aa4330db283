import mongoose from 'mongoose'
import { MongoMemoryServer } from 'mongodb-memory-server'

let mongoServer: MongoMemoryServer

/**
 * 连接到测试数据库
 * 使用内存数据库进行测试，避免影响真实数据
 */
export const connectTestDB = async (): Promise<void> => {
  try {
    // 创建内存数据库实例
    mongoServer = await MongoMemoryServer.create()
    const mongoUri = mongoServer.getUri()

    // 连接到内存数据库
    await mongoose.connect(mongoUri)
    
    console.log('✅ 测试数据库连接成功')
  } catch (error) {
    console.error('❌ 测试数据库连接失败:', error)
    throw error
  }
}

/**
 * 关闭测试数据库连接
 */
export const closeTestDB = async (): Promise<void> => {
  try {
    // 关闭mongoose连接
    await mongoose.connection.dropDatabase()
    await mongoose.connection.close()
    
    // 停止内存数据库服务器
    if (mongoServer) {
      await mongoServer.stop()
    }
    
    console.log('✅ 测试数据库连接已关闭')
  } catch (error) {
    console.error('❌ 关闭测试数据库失败:', error)
    throw error
  }
}

/**
 * 清空测试数据库中的所有数据
 */
export const clearTestDB = async (): Promise<void> => {
  try {
    const collections = mongoose.connection.collections
    
    // 清空所有集合
    for (const key in collections) {
      const collection = collections[key]
      await collection.deleteMany({})
    }
    
    console.log('🧹 测试数据库已清空')
  } catch (error) {
    console.error('❌ 清空测试数据库失败:', error)
    throw error
  }
}

/**
 * 创建测试用户数据
 */
export const createTestUser = async (userData?: Partial<any>) => {
  const { User } = require('../../models/User')
  
  const defaultUserData = {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    subscription: {
      plan: 'free',
      tokensUsed: 0,
      tokensLimit: 10000,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
    },
    preferences: {
      language: 'zh-CN',
      theme: 'light',
      notifications: {
        email: true,
        browser: true,
      },
    },
  }

  const user = new User({ ...defaultUserData, ...userData })
  await user.save()
  
  return user
}

/**
 * 创建测试数字人数据
 */
export const createTestCharacter = async (userId: string, characterData?: Partial<any>) => {
  const { Character } = require('../../models/Character')
  
  const defaultCharacterData = {
    name: '测试数字人',
    description: '这是一个测试数字人',
    userId,
    config: {
      personality: '友善、专业',
      expertise: ['客服', '技术支持'],
      language: 'zh-CN',
      voice: {
        provider: 'azure',
        voiceId: 'zh-CN-XiaoxiaoNeural',
        speed: 1.0,
        pitch: 1.0,
      },
      aiModel: {
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 2000,
        systemPrompt: '你是一个友善的AI助手',
      },
    },
    knowledgeBase: {
      enabled: false,
      documents: [],
    },
    isPublic: false,
    tags: ['测试'],
    stats: {
      conversationCount: 0,
      messageCount: 0,
      lastUsedAt: new Date(),
    },
  }

  const character = new Character({ ...defaultCharacterData, ...characterData })
  await character.save()
  
  return character
}

/**
 * 创建测试对话数据
 */
export const createTestConversation = async (userId: string, characterId: string, conversationData?: Partial<any>) => {
  const { Conversation } = require('../../models/Conversation')
  
  const defaultConversationData = {
    title: '测试对话',
    userId,
    characterId,
    messages: [],
    status: 'active',
    metadata: {
      totalTokens: 0,
      totalCost: 0,
    },
  }

  const conversation = new Conversation({ ...defaultConversationData, ...conversationData })
  await conversation.save()
  
  return conversation
}

/**
 * 创建测试知识库数据
 */
export const createTestKnowledgeBase = async (userId: string, knowledgeData?: Partial<any>) => {
  const { Knowledge } = require('../../models/Knowledge')
  
  const defaultKnowledgeData = {
    name: '测试知识库',
    description: '这是一个测试知识库',
    userId,
    documents: [],
    vectorStore: {
      provider: 'pinecone',
      indexName: 'test-index',
      totalVectors: 0,
    },
    settings: {
      chunkSize: 1000,
      chunkOverlap: 200,
      embeddingModel: 'text-embedding-ada-002',
    },
  }

  const knowledge = new Knowledge({ ...defaultKnowledgeData, ...knowledgeData })
  await knowledge.save()
  
  return knowledge
}

/**
 * 生成测试JWT Token
 */
export const generateTestToken = (userId: string): string => {
  const jwt = require('jsonwebtoken')
  const config = require('../../config')
  
  return jwt.sign(
    { userId },
    config.JWT_SECRET || 'test-secret',
    { expiresIn: '1h' }
  )
}

/**
 * 模拟认证中间件
 */
export const mockAuthMiddleware = (userId: string) => {
  return (req: any, res: any, next: any) => {
    req.user = { _id: userId }
    next()
  }
}

/**
 * 等待指定时间（用于测试异步操作）
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 验证对象是否包含指定属性
 */
export const expectObjectToHaveProperties = (obj: any, properties: string[]) => {
  properties.forEach(prop => {
    expect(obj).toHaveProperty(prop)
  })
}

/**
 * 验证数组是否包含指定长度
 */
export const expectArrayToHaveLength = (arr: any[], length: number) => {
  expect(Array.isArray(arr)).toBe(true)
  expect(arr).toHaveLength(length)
}

/**
 * 验证日期是否有效
 */
export const expectValidDate = (date: any) => {
  expect(date).toBeInstanceOf(Date)
  expect(isNaN(date.getTime())).toBe(false)
}

/**
 * 验证MongoDB ObjectId格式
 */
export const expectValidObjectId = (id: any) => {
  expect(typeof id).toBe('string')
  expect(id).toMatch(/^[0-9a-fA-F]{24}$/)
}
