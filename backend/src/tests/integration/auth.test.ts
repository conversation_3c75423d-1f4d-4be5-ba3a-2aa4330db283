import request from 'supertest'
import { app } from '../../app'
import { User } from '../../models/User'
import { connectTestDB, closeTestDB, clearTestDB } from '../utils/testDb'

describe('认证API集成测试', () => {
  beforeAll(async () => {
    await connectTestDB()
  })

  afterAll(async () => {
    await closeTestDB()
  })

  beforeEach(async () => {
    await clearTestDB()
  })

  describe('POST /api/auth/register', () => {
    it('应该成功注册新用户', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body).toHaveProperty('message', '注册成功')
      expect(response.body.data).toHaveProperty('user')
      expect(response.body.data).toHaveProperty('token')
      expect(response.body.data.user).toHaveProperty('email', userData.email)
      expect(response.body.data.user).toHaveProperty('username', userData.username)
      expect(response.body.data.user).not.toHaveProperty('password')

      // 验证用户是否保存到数据库
      const savedUser = await User.findOne({ email: userData.email })
      expect(savedUser).toBeTruthy()
      expect(savedUser?.username).toBe(userData.username)
    })

    it('应该拒绝重复的邮箱', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      }

      // 先注册一个用户
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201)

      // 尝试用相同邮箱再次注册
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser2',
          email: '<EMAIL>',
          password: 'password456'
        })
        .expect(400)

      expect(response.body).toHaveProperty('success', false)
      expect(response.body).toHaveProperty('message', '邮箱已被注册')
    })

    it('应该验证必填字段', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({})
        .expect(400)

      expect(response.body).toHaveProperty('success', false)
      expect(response.body).toHaveProperty('message')
    })

    it('应该验证邮箱格式', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser',
          email: 'invalid-email',
          password: 'password123'
        })
        .expect(400)

      expect(response.body).toHaveProperty('success', false)
    })

    it('应该验证密码长度', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: '123'
        })
        .expect(400)

      expect(response.body).toHaveProperty('success', false)
    })
  })

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // 创建测试用户
      await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'password123'
        })
    })

    it('应该成功登录', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body).toHaveProperty('message', '登录成功')
      expect(response.body.data).toHaveProperty('user')
      expect(response.body.data).toHaveProperty('token')
      expect(response.body.data.user).toHaveProperty('email', '<EMAIL>')
    })

    it('应该拒绝错误的密码', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
        .expect(401)

      expect(response.body).toHaveProperty('success', false)
      expect(response.body).toHaveProperty('message', '邮箱或密码错误')
    })

    it('应该拒绝不存在的用户', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(401)

      expect(response.body).toHaveProperty('success', false)
      expect(response.body).toHaveProperty('message', '邮箱或密码错误')
    })

    it('应该验证必填字段', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({})
        .expect(400)

      expect(response.body).toHaveProperty('success', false)
    })
  })

  describe('GET /api/auth/profile', () => {
    let authToken: string

    beforeEach(async () => {
      // 注册并登录用户
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'password123'
        })

      authToken = registerResponse.body.data.token
    })

    it('应该返回用户信息', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body.data).toHaveProperty('email', '<EMAIL>')
      expect(response.body.data).toHaveProperty('username', 'testuser')
      expect(response.body.data).not.toHaveProperty('password')
    })

    it('应该拒绝无效的token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401)

      expect(response.body).toHaveProperty('success', false)
      expect(response.body).toHaveProperty('message', 'Token无效')
    })

    it('应该拒绝没有token的请求', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .expect(401)

      expect(response.body).toHaveProperty('success', false)
      expect(response.body).toHaveProperty('message', '未提供认证token')
    })
  })

  describe('PUT /api/auth/profile', () => {
    let authToken: string

    beforeEach(async () => {
      // 注册并登录用户
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'password123'
        })

      authToken = registerResponse.body.data.token
    })

    it('应该更新用户信息', async () => {
      const updateData = {
        username: 'updateduser',
        preferences: {
          language: 'en-US',
          theme: 'dark'
        }
      }

      const response = await request(app)
        .put('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body).toHaveProperty('message', '个人信息更新成功')
      expect(response.body.data).toHaveProperty('username', 'updateduser')
      expect(response.body.data.preferences).toHaveProperty('language', 'en-US')
      expect(response.body.data.preferences).toHaveProperty('theme', 'dark')
    })

    it('应该拒绝无效的数据', async () => {
      const response = await request(app)
        .put('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          username: '', // 空用户名
        })
        .expect(400)

      expect(response.body).toHaveProperty('success', false)
    })

    it('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .put('/api/auth/profile')
        .send({
          username: 'updateduser'
        })
        .expect(401)

      expect(response.body).toHaveProperty('success', false)
    })
  })

  describe('POST /api/auth/refresh', () => {
    let authToken: string

    beforeEach(async () => {
      // 注册并登录用户
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'password123'
        })

      authToken = registerResponse.body.data.token
    })

    it('应该刷新token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body).toHaveProperty('message', 'Token刷新成功')
      expect(response.body.data).toHaveProperty('token')
      expect(response.body.data.token).not.toBe(authToken) // 新token应该不同
    })

    it('应该拒绝无效的token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401)

      expect(response.body).toHaveProperty('success', false)
    })
  })
})
