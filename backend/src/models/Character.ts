import mongoose, { Schema, Document } from 'mongoose'
import { ICharacter } from '@/types'

// 数字人文档接口
export interface CharacterDocument extends ICharacter, Document {}

// 语音配置子模式
const voiceConfigSchema = new Schema({
  provider: {
    type: String,
    enum: ['azure', 'aliyun', 'local'],
    default: 'azure',
  },
  voiceId: {
    type: String,
    required: [true, '语音ID是必填项'],
  },
  speed: {
    type: Number,
    min: 0.5,
    max: 2,
    default: 1,
  },
  pitch: {
    type: Number,
    min: 0.5,
    max: 2,
    default: 1,
  },
}, { _id: false })

// AI模型配置子模式
const aiModelConfigSchema = new Schema({
  provider: {
    type: String,
    enum: ['openai', 'anthropic', 'local'],
    required: [true, 'AI模型提供商是必填项'],
  },
  model: {
    type: String,
    required: [true, '模型名称是必填项'],
  },
  temperature: {
    type: Number,
    min: 0,
    max: 2,
    default: 0.7,
  },
  maxTokens: {
    type: Number,
    min: 1,
    max: 4000,
    default: 2000,
  },
  systemPrompt: {
    type: String,
    maxlength: [2000, '系统提示词最多2000个字符'],
    default: '',
  },
}, { _id: false })

// 数字人配置子模式
const characterConfigSchema = new Schema({
  personality: {
    type: String,
    maxlength: [1000, '性格描述最多1000个字符'],
    default: '',
  },
  expertise: [{
    type: String,
    maxlength: [50, '专业领域最多50个字符'],
  }],
  language: {
    type: String,
    enum: ['zh-CN', 'en-US', 'ja-JP'],
    default: 'zh-CN',
  },
  voice: {
    type: voiceConfigSchema,
    required: true,
  },
  aiModel: {
    type: aiModelConfigSchema,
    required: true,
  },
}, { _id: false })

// 文档信息子模式
const documentInfoSchema = new Schema({
  id: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
  uploadedAt: {
    type: Date,
    default: Date.now,
  },
}, { _id: false })

// 知识库配置子模式
const knowledgeBaseConfigSchema = new Schema({
  enabled: {
    type: Boolean,
    default: false,
  },
  vectorStoreId: {
    type: String,
    default: '',
  },
  documents: [documentInfoSchema],
}, { _id: false })

// 统计信息子模式
const characterStatsSchema = new Schema({
  conversationCount: {
    type: Number,
    default: 0,
    min: 0,
  },
  messageCount: {
    type: Number,
    default: 0,
    min: 0,
  },
  lastUsedAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
}, { _id: false })

// 数字人模式定义
const characterSchema = new Schema<CharacterDocument>(
  {
    name: {
      type: String,
      required: [true, '数字人名称是必填项'],
      trim: true,
      minlength: [1, '数字人名称不能为空'],
      maxlength: [50, '数字人名称最多50个字符'],
      index: true,
    },
    description: {
      type: String,
      maxlength: [500, '描述最多500个字符'],
      default: '',
    },
    avatar: {
      type: String,
      default: '',
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, '用户ID是必填项'],
      index: true,
    },
    config: {
      type: characterConfigSchema,
      required: true,
    },
    knowledgeBase: {
      type: knowledgeBaseConfigSchema,
      default: () => ({ enabled: false, documents: [] }),
    },
    isPublic: {
      type: Boolean,
      default: false,
      index: true,
    },
    tags: [{
      type: String,
      maxlength: [20, '标签最多20个字符'],
      trim: true,
    }],
    stats: {
      type: characterStatsSchema,
      default: () => ({
        conversationCount: 0,
        messageCount: 0,
        lastUsedAt: new Date(),
      }),
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: (doc, ret) => {
        ret.id = ret._id
        delete ret._id
        return ret
      },
    },
    toObject: {
      transform: (doc, ret) => {
        ret.id = ret._id
        delete ret._id
        return ret
      },
    },
  }
)

// 索引定义
characterSchema.index({ userId: 1 })
characterSchema.index({ isPublic: 1, tags: 1 })
characterSchema.index({ 'stats.lastUsedAt': -1 })
characterSchema.index({ createdAt: -1 })
characterSchema.index({ name: 'text', description: 'text' }) // 文本搜索索引

// 实例方法
characterSchema.methods.updateStats = function(conversationIncrement = 0, messageIncrement = 0) {
  this.stats.conversationCount += conversationIncrement
  this.stats.messageCount += messageIncrement
  this.stats.lastUsedAt = new Date()
  return this.save()
}

characterSchema.methods.addDocument = function(documentInfo: any) {
  this.knowledgeBase.documents.push(documentInfo)
  return this.save()
}

characterSchema.methods.removeDocument = function(documentId: string) {
  this.knowledgeBase.documents = this.knowledgeBase.documents.filter(
    (doc: any) => doc.id !== documentId
  )
  return this.save()
}

// 静态方法
characterSchema.statics.findByUserId = function(userId: string, options = {}) {
  return this.find({ userId, ...options }).sort({ 'stats.lastUsedAt': -1 })
}

characterSchema.statics.findPublicCharacters = function(options = {}) {
  return this.find({ isPublic: true, ...options }).sort({ 'stats.conversationCount': -1 })
}

characterSchema.statics.searchCharacters = function(query: string, userId?: string) {
  const searchConditions: any = {
    $text: { $search: query }
  }
  
  if (userId) {
    searchConditions.$or = [
      { userId },
      { isPublic: true }
    ]
  } else {
    searchConditions.isPublic = true
  }
  
  return this.find(searchConditions, { score: { $meta: 'textScore' } })
    .sort({ score: { $meta: 'textScore' } })
}

// 中间件
characterSchema.pre('save', function(next) {
  // 保存前的处理
  if (this.isNew) {
    this.stats.lastUsedAt = new Date()
  }
  next()
})

characterSchema.pre('findOneAndDelete', async function(next) {
  // 删除前的清理工作
  const character = await this.model.findOne(this.getQuery())
  if (character) {
    // TODO: 清理相关的对话记录
    // TODO: 清理知识库文档
  }
  next()
})

// 虚拟字段
characterSchema.virtual('isActive').get(function() {
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
  return this.stats.lastUsedAt > thirtyDaysAgo
})

characterSchema.virtual('documentCount').get(function() {
  return this.knowledgeBase.documents.length
})

// 创建模型
export const Character = mongoose.model<CharacterDocument>('Character', characterSchema)

// 导出类型
export type { CharacterDocument }
