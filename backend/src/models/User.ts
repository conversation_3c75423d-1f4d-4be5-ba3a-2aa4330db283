import mongoose, { Schema, Document } from 'mongoose'
import { IUser } from '@/types'

// 用户文档接口
export interface UserDocument extends IUser, Document {}

// 用户模式定义
const userSchema = new Schema<UserDocument>(
  {
    username: {
      type: String,
      required: [true, '用户名是必填项'],
      unique: true,
      trim: true,
      minlength: [3, '用户名至少3个字符'],
      maxlength: [20, '用户名最多20个字符'],
      match: [/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'],
      index: true,
    },
    email: {
      type: String,
      required: [true, '邮箱是必填项'],
      unique: true,
      lowercase: true,
      trim: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址'],
      index: true,
    },
    passwordHash: {
      type: String,
      required: [true, '密码哈希是必填项'],
      select: false, // 默认查询时不返回密码哈希
    },
    avatar: {
      type: String,
      default: '',
    },
    role: {
      type: String,
      enum: ['admin', 'user'],
      default: 'user',
      index: true,
    },
    subscription: {
      plan: {
        type: String,
        enum: ['free', 'pro', 'enterprise'],
        default: 'free',
        index: true,
      },
      expiresAt: {
        type: Date,
        default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
      },
      tokensUsed: {
        type: Number,
        default: 0,
        min: 0,
      },
      tokensLimit: {
        type: Number,
        default: 10000,
        min: 0,
      },
    },
    preferences: {
      language: {
        type: String,
        enum: ['zh-CN', 'en-US', 'ja-JP'],
        default: 'zh-CN',
      },
      theme: {
        type: String,
        enum: ['light', 'dark', 'auto'],
        default: 'light',
      },
      notifications: {
        email: {
          type: Boolean,
          default: true,
        },
        browser: {
          type: Boolean,
          default: true,
        },
      },
    },
    isEmailVerified: {
      type: Boolean,
      default: false,
      index: true,
    },
    lastLoginAt: {
      type: Date,
      index: true,
    },
  },
  {
    timestamps: true, // 自动添加createdAt和updatedAt字段
    versionKey: false, // 禁用__v字段
    toJSON: {
      transform: (doc, ret) => {
        // 转换为JSON时的处理
        ret.id = ret._id
        delete ret._id
        delete ret.passwordHash
        return ret
      },
    },
    toObject: {
      transform: (doc, ret) => {
        // 转换为对象时的处理
        ret.id = ret._id
        delete ret._id
        delete ret.passwordHash
        return ret
      },
    },
  }
)

// 索引定义
userSchema.index({ email: 1 }, { unique: true })
userSchema.index({ username: 1 }, { unique: true })
userSchema.index({ createdAt: -1 })
userSchema.index({ lastLoginAt: -1 })
userSchema.index({ 'subscription.plan': 1 })
userSchema.index({ role: 1, isEmailVerified: 1 })

// 实例方法
userSchema.methods.toSafeObject = function() {
  const obj = this.toObject()
  delete obj.passwordHash
  return obj
}

// 静态方法
userSchema.statics.findByEmail = function(email: string) {
  return this.findOne({ email: email.toLowerCase() })
}

userSchema.statics.findByUsername = function(username: string) {
  return this.findOne({ username })
}

userSchema.statics.findActiveUsers = function() {
  return this.find({ 
    isEmailVerified: true,
    lastLoginAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
  })
}

// 中间件
userSchema.pre('save', function(next) {
  // 保存前的处理
  if (this.isModified('email')) {
    this.email = this.email.toLowerCase()
  }
  next()
})

userSchema.pre('findOneAndUpdate', function(next) {
  // 更新前的处理
  const update = this.getUpdate() as any
  if (update.email) {
    update.email = update.email.toLowerCase()
  }
  next()
})

// 虚拟字段
userSchema.virtual('isSubscriptionActive').get(function() {
  return this.subscription.expiresAt > new Date()
})

userSchema.virtual('tokenUsagePercentage').get(function() {
  return (this.subscription.tokensUsed / this.subscription.tokensLimit) * 100
})

// 创建模型
export const User = mongoose.model<UserDocument>('User', userSchema)

// 导出类型
export type { UserDocument }
