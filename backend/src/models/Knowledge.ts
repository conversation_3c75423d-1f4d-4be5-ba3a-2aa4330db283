import mongoose, { Schema, Document } from 'mongoose'
import { IKnowledge, IDocument } from '@/types'

// 知识库文档接口
export interface KnowledgeDocument extends IKnowledge, Document {}

// 文档子模式
const documentSchema = new Schema({
  name: {
    type: String,
    required: [true, '文档名称是必填项'],
    maxlength: [100, '文档名称最多100个字符'],
  },
  originalName: {
    type: String,
    required: [true, '原始文件名是必填项'],
  },
  type: {
    type: String,
    enum: ['pdf', 'docx', 'txt', 'md', 'rtf'],
    required: [true, '文档类型是必填项'],
  },
  size: {
    type: Number,
    required: [true, '文件大小是必填项'],
    min: 0,
  },
  url: {
    type: String,
    required: [true, '文件URL是必填项'],
  },
  status: {
    type: String,
    enum: ['processing', 'completed', 'failed'],
    default: 'processing',
    index: true,
  },
  chunks: {
    type: Number,
    default: 0,
    min: 0,
  },
  uploadedAt: {
    type: Date,
    default: Date.now,
  },
  processedAt: {
    type: Date,
  },
}, { 
  timestamps: true,
  _id: true,
})

// 向量存储配置子模式
const vectorStoreConfigSchema = new Schema({
  provider: {
    type: String,
    enum: ['pinecone', 'weaviate'],
    required: [true, '向量存储提供商是必填项'],
  },
  indexId: {
    type: String,
    required: [true, '索引ID是必填项'],
  },
  dimensions: {
    type: Number,
    required: [true, '向量维度是必填项'],
    min: 1,
  },
  totalVectors: {
    type: Number,
    default: 0,
    min: 0,
  },
}, { _id: false })

// 知识库设置子模式
const knowledgeSettingsSchema = new Schema({
  chunkSize: {
    type: Number,
    min: 100,
    max: 2000,
    default: 1000,
  },
  chunkOverlap: {
    type: Number,
    min: 0,
    max: 500,
    default: 200,
  },
  embeddingModel: {
    type: String,
    default: 'text-embedding-ada-002',
  },
}, { _id: false })

// 知识库模式定义
const knowledgeSchema = new Schema<KnowledgeDocument>(
  {
    name: {
      type: String,
      required: [true, '知识库名称是必填项'],
      trim: true,
      minlength: [1, '知识库名称不能为空'],
      maxlength: [50, '知识库名称最多50个字符'],
      index: true,
    },
    description: {
      type: String,
      maxlength: [500, '描述最多500个字符'],
      default: '',
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, '用户ID是必填项'],
      index: true,
    },
    documents: [documentSchema],
    vectorStore: {
      type: vectorStoreConfigSchema,
      required: false,
    },
    settings: {
      type: knowledgeSettingsSchema,
      default: () => ({
        chunkSize: 1000,
        chunkOverlap: 200,
        embeddingModel: 'text-embedding-ada-002',
      }),
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: (doc, ret) => {
        ret.id = ret._id
        delete ret._id
        return ret
      },
    },
    toObject: {
      transform: (doc, ret) => {
        ret.id = ret._id
        delete ret._id
        return ret
      },
    },
  }
)

// 索引定义
knowledgeSchema.index({ userId: 1 })
knowledgeSchema.index({ 'documents.status': 1 })
knowledgeSchema.index({ createdAt: -1 })
knowledgeSchema.index({ name: 'text', description: 'text' }) // 文本搜索索引

// 实例方法
knowledgeSchema.methods.addDocument = function(documentData: Partial<IDocument>) {
  const newDocument = {
    name: documentData.name,
    originalName: documentData.originalName,
    type: documentData.type,
    size: documentData.size,
    url: documentData.url,
    status: 'processing',
    chunks: 0,
    uploadedAt: new Date(),
  }
  
  this.documents.push(newDocument)
  return this.save()
}

knowledgeSchema.methods.updateDocumentStatus = function(documentId: string, status: string, chunks?: number) {
  const document = this.documents.id(documentId)
  if (document) {
    document.status = status
    if (chunks !== undefined) {
      document.chunks = chunks
    }
    if (status === 'completed' || status === 'failed') {
      document.processedAt = new Date()
    }
  }
  return this.save()
}

knowledgeSchema.methods.removeDocument = function(documentId: string) {
  this.documents.pull({ _id: documentId })
  return this.save()
}

knowledgeSchema.methods.getDocumentById = function(documentId: string) {
  return this.documents.id(documentId)
}

knowledgeSchema.methods.getCompletedDocuments = function() {
  return this.documents.filter(doc => doc.status === 'completed')
}

knowledgeSchema.methods.getProcessingDocuments = function() {
  return this.documents.filter(doc => doc.status === 'processing')
}

knowledgeSchema.methods.getFailedDocuments = function() {
  return this.documents.filter(doc => doc.status === 'failed')
}

knowledgeSchema.methods.getTotalSize = function() {
  return this.documents.reduce((total, doc) => total + doc.size, 0)
}

knowledgeSchema.methods.getTotalChunks = function() {
  return this.documents.reduce((total, doc) => total + doc.chunks, 0)
}

// 静态方法
knowledgeSchema.statics.findByUserId = function(userId: string) {
  return this.find({ userId }).sort({ createdAt: -1 })
}

knowledgeSchema.statics.searchKnowledge = function(query: string, userId?: string) {
  const searchConditions: any = {
    $text: { $search: query }
  }
  
  if (userId) {
    searchConditions.userId = userId
  }
  
  return this.find(searchConditions, { score: { $meta: 'textScore' } })
    .sort({ score: { $meta: 'textScore' } })
}

knowledgeSchema.statics.getKnowledgeStats = function(userId?: string) {
  const matchStage: any = {}
  if (userId) {
    matchStage.userId = new mongoose.Types.ObjectId(userId)
  }
  
  return this.aggregate([
    { $match: matchStage },
    { $unwind: { path: '$documents', preserveNullAndEmptyArrays: true } },
    {
      $group: {
        _id: null,
        totalKnowledgeBases: { $addToSet: '$_id' },
        totalDocuments: { $sum: 1 },
        completedDocuments: {
          $sum: { $cond: [{ $eq: ['$documents.status', 'completed'] }, 1, 0] }
        },
        processingDocuments: {
          $sum: { $cond: [{ $eq: ['$documents.status', 'processing'] }, 1, 0] }
        },
        failedDocuments: {
          $sum: { $cond: [{ $eq: ['$documents.status', 'failed'] }, 1, 0] }
        },
        totalSize: { $sum: '$documents.size' },
        totalChunks: { $sum: '$documents.chunks' },
      }
    },
    {
      $project: {
        totalKnowledgeBases: { $size: '$totalKnowledgeBases' },
        totalDocuments: 1,
        completedDocuments: 1,
        processingDocuments: 1,
        failedDocuments: 1,
        totalSize: 1,
        totalChunks: 1,
      }
    }
  ])
}

// 中间件
knowledgeSchema.pre('save', function(next) {
  // 保存前的处理
  if (this.isModified('documents')) {
    // 更新向量存储统计
    if (this.vectorStore) {
      this.vectorStore.totalVectors = this.getTotalChunks()
    }
  }
  next()
})

knowledgeSchema.pre('findOneAndDelete', async function(next) {
  // 删除前的清理工作
  const knowledge = await this.model.findOne(this.getQuery())
  if (knowledge) {
    // TODO: 清理向量存储中的数据
    // TODO: 删除文件存储中的文档
  }
  next()
})

// 虚拟字段
knowledgeSchema.virtual('documentCount').get(function() {
  return this.documents.length
})

knowledgeSchema.virtual('completedDocumentCount').get(function() {
  return this.documents.filter(doc => doc.status === 'completed').length
})

knowledgeSchema.virtual('processingDocumentCount').get(function() {
  return this.documents.filter(doc => doc.status === 'processing').length
})

knowledgeSchema.virtual('failedDocumentCount').get(function() {
  return this.documents.filter(doc => doc.status === 'failed').length
})

knowledgeSchema.virtual('totalSize').get(function() {
  return this.documents.reduce((total, doc) => total + doc.size, 0)
})

knowledgeSchema.virtual('totalChunks').get(function() {
  return this.documents.reduce((total, doc) => total + doc.chunks, 0)
})

knowledgeSchema.virtual('isReady').get(function() {
  return this.documents.length > 0 && 
         this.documents.every(doc => doc.status === 'completed') &&
         this.vectorStore
})

// 创建模型
export const Knowledge = mongoose.model<KnowledgeDocument>('Knowledge', knowledgeSchema)

// 导出类型
export type { KnowledgeDocument }
