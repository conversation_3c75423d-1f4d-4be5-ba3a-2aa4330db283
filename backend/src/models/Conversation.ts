import mongoose, { Schema, Document } from 'mongoose'
import { IConversation, IMessage } from '@/types'

// 对话文档接口
export interface ConversationDocument extends IConversation, Document {}

// 消息元数据子模式
const messageMetadataSchema = new Schema({
  tokens: {
    type: Number,
    min: 0,
  },
  model: {
    type: String,
  },
  ragUsed: {
    type: Boolean,
    default: false,
  },
  sources: [{
    type: String,
  }],
  duration: {
    type: Number,
    min: 0,
  },
}, { _id: false })

// 消息子模式
const messageSchema = new Schema({
  id: {
    type: String,
    required: true,
    unique: true,
  },
  role: {
    type: String,
    enum: ['user', 'assistant', 'system'],
    required: [true, '消息角色是必填项'],
  },
  content: {
    type: String,
    required: [true, '消息内容是必填项'],
    maxlength: [10000, '消息内容最多10000个字符'],
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true,
  },
  metadata: {
    type: messageMetadataSchema,
    default: {},
  },
}, { _id: false })

// 对话模式定义
const conversationSchema = new Schema<ConversationDocument>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, '用户ID是必填项'],
      index: true,
    },
    characterId: {
      type: Schema.Types.ObjectId,
      ref: 'Character',
      required: [true, '数字人ID是必填项'],
      index: true,
    },
    title: {
      type: String,
      maxlength: [100, '对话标题最多100个字符'],
      default: '',
    },
    messages: [messageSchema],
    status: {
      type: String,
      enum: ['active', 'archived', 'deleted'],
      default: 'active',
      index: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: (doc, ret) => {
        ret.id = ret._id
        delete ret._id
        return ret
      },
    },
    toObject: {
      transform: (doc, ret) => {
        ret.id = ret._id
        delete ret._id
        return ret
      },
    },
  }
)

// 索引定义
conversationSchema.index({ userId: 1, createdAt: -1 })
conversationSchema.index({ characterId: 1 })
conversationSchema.index({ status: 1 })
conversationSchema.index({ updatedAt: -1 })
conversationSchema.index({ 'messages.timestamp': -1 })

// 实例方法
conversationSchema.methods.addMessage = function(message: Omit<IMessage, 'id' | 'timestamp'>) {
  const newMessage: IMessage = {
    id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    timestamp: new Date(),
    ...message,
  }
  
  this.messages.push(newMessage)
  
  // 自动生成标题（如果没有标题且是第一条用户消息）
  if (!this.title && message.role === 'user' && this.messages.length <= 2) {
    this.title = message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '')
  }
  
  return this.save()
}

conversationSchema.methods.getLastMessage = function() {
  return this.messages[this.messages.length - 1]
}

conversationSchema.methods.getMessageCount = function() {
  return this.messages.length
}

conversationSchema.methods.getUserMessageCount = function() {
  return this.messages.filter(msg => msg.role === 'user').length
}

conversationSchema.methods.getAssistantMessageCount = function() {
  return this.messages.filter(msg => msg.role === 'assistant').length
}

conversationSchema.methods.getTotalTokens = function() {
  return this.messages.reduce((total, msg) => {
    return total + (msg.metadata?.tokens || 0)
  }, 0)
}

conversationSchema.methods.archive = function() {
  this.status = 'archived'
  return this.save()
}

conversationSchema.methods.restore = function() {
  this.status = 'active'
  return this.save()
}

conversationSchema.methods.softDelete = function() {
  this.status = 'deleted'
  return this.save()
}

// 静态方法
conversationSchema.statics.findByUserId = function(userId: string, status = 'active') {
  return this.find({ userId, status }).sort({ updatedAt: -1 })
}

conversationSchema.statics.findByCharacterId = function(characterId: string, status = 'active') {
  return this.find({ characterId, status }).sort({ updatedAt: -1 })
}

conversationSchema.statics.findActiveConversations = function(userId?: string) {
  const query: any = { status: 'active' }
  if (userId) {
    query.userId = userId
  }
  return this.find(query).sort({ updatedAt: -1 })
}

conversationSchema.statics.getConversationStats = function(userId?: string) {
  const matchStage: any = {}
  if (userId) {
    matchStage.userId = new mongoose.Types.ObjectId(userId)
  }
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalConversations: { $sum: 1 },
        activeConversations: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        totalMessages: { $sum: { $size: '$messages' } },
        averageMessagesPerConversation: { $avg: { $size: '$messages' } },
      }
    }
  ])
}

// 中间件
conversationSchema.pre('save', function(next) {
  // 保存前的处理
  if (this.isModified('messages')) {
    // 更新updatedAt时间
    this.updatedAt = new Date()
  }
  next()
})

conversationSchema.pre('findOneAndUpdate', function(next) {
  // 更新前的处理
  this.set({ updatedAt: new Date() })
  next()
})

// 虚拟字段
conversationSchema.virtual('messageCount').get(function() {
  return this.messages.length
})

conversationSchema.virtual('lastMessageAt').get(function() {
  if (this.messages.length === 0) return this.createdAt
  return this.messages[this.messages.length - 1].timestamp
})

conversationSchema.virtual('isActive').get(function() {
  return this.status === 'active'
})

conversationSchema.virtual('totalTokensUsed').get(function() {
  return this.messages.reduce((total, msg) => {
    return total + (msg.metadata?.tokens || 0)
  }, 0)
})

// 创建模型
export const Conversation = mongoose.model<ConversationDocument>('Conversation', conversationSchema)

// 导出类型
export type { ConversationDocument }
