import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import mongoSanitize from 'express-mongo-sanitize'
import hpp from 'hpp'
import { createServer } from 'http'
import { Server as SocketIOServer } from 'socket.io'
import dotenv from 'dotenv'

import { connectDatabase } from '@config/database'
import { connectRedis } from '@config/redis'
import { logger } from '@utils/logger'
import { errorHandler } from '@middleware/errorHandler'
import { requestLogger } from '@middleware/requestLogger'
import { authRoutes } from '@routes/authRoutes'
import { characterRoutes } from '@routes/characterRoutes'
import { conversationRoutes } from '@routes/conversationRoutes'
import { knowledgeRoutes } from '@routes/knowledgeRoutes'
import { healthRoutes } from '@routes/healthRoutes'
import { setupSocketIO } from '@config/socket'

// 加载环境变量
dotenv.config()

const app = express()
const server = createServer(app)
const io = new SocketIOServer(server, {
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST'],
    credentials: true,
  },
})

const PORT = process.env.PORT || 3001

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}))

// CORS配置
app.use(cors({
  origin: (origin, callback) => {
    const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000']
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true)
    } else {
      callback(new Error('不允许的CORS来源'))
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}))

// 基础中间件
app.use(compression())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 安全防护
app.use(mongoSanitize())
app.use(hpp())

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    error: '请求过于频繁，请稍后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
})

app.use('/api', limiter)

// 请求日志
app.use(requestLogger)

// 健康检查路由
app.use('/', healthRoutes)

// API路由
app.use('/api/auth', authRoutes)
app.use('/api/characters', characterRoutes)
app.use('/api/conversations', conversationRoutes)
app.use('/api/knowledge', knowledgeRoutes)

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: '请求的资源不存在',
    },
    timestamp: new Date().toISOString(),
  })
})

// 错误处理中间件
app.use(errorHandler)

// 设置Socket.IO
setupSocketIO(io)

// 启动服务器
async function startServer() {
  try {
    // 连接数据库
    await connectDatabase()
    logger.info('数据库连接成功')

    // 连接Redis
    await connectRedis()
    logger.info('Redis连接成功')

    // 启动服务器
    server.listen(PORT, () => {
      logger.info(`服务器运行在端口 ${PORT}`)
      logger.info(`环境: ${process.env.NODE_ENV || 'development'}`)
      logger.info(`健康检查: http://localhost:${PORT}/health`)
    })
  } catch (error) {
    logger.error('服务器启动失败:', error)
    process.exit(1)
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...')
  server.close(() => {
    logger.info('HTTP服务器已关闭')
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，开始优雅关闭...')
  server.close(() => {
    logger.info('HTTP服务器已关闭')
    process.exit(0)
  })
})

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', reason)
  logger.error('Promise:', promise)
  process.exit(1)
})

// 启动服务器
startServer()

export { app, server, io }
