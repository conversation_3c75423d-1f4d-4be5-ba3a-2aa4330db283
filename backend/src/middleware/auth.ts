import { Request, Response, NextFunction } from 'express'
import { CryptoUtils } from '@/utils/crypto'
import { User } from '@/models/User'
import { AppError } from './errorHandler'
import { JWTPayload } from '@/types'

// 扩展Request接口以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user?: any
    }
  }
}

/**
 * JWT认证中间件
 */
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

    if (!token) {
      throw new AppError('访问令牌缺失', 401, 'MISSING_TOKEN')
    }

    // 验证JWT令牌
    const decoded = CryptoUtils.verifyToken(token) as JWTPayload

    // 从数据库获取用户信息
    const user = await User.findById(decoded.userId).select('-passwordHash')
    if (!user) {
      throw new AppError('用户不存在', 401, 'USER_NOT_FOUND')
    }

    // 检查用户是否被禁用
    if (!user.isEmailVerified) {
      throw new AppError('邮箱未验证', 401, 'EMAIL_NOT_VERIFIED')
    }

    // 将用户信息添加到请求对象
    req.user = user
    next()
  } catch (error) {
    if (error instanceof AppError) {
      next(error)
    } else {
      next(new AppError('无效的访问令牌', 401, 'INVALID_TOKEN'))
    }
  }
}

/**
 * 可选认证中间件（不强制要求登录）
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1]

    if (token) {
      const decoded = CryptoUtils.verifyToken(token) as JWTPayload
      const user = await User.findById(decoded.userId).select('-passwordHash')
      if (user && user.isEmailVerified) {
        req.user = user
      }
    }
    
    next()
  } catch (error) {
    // 可选认证失败时不抛出错误，继续执行
    next()
  }
}

/**
 * 角色权限检查中间件
 */
export const requireRole = (roles: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AppError('需要登录', 401, 'AUTHENTICATION_REQUIRED')
    }

    const userRole = req.user.role
    const allowedRoles = Array.isArray(roles) ? roles : [roles]

    if (!allowedRoles.includes(userRole)) {
      throw new AppError('权限不足', 403, 'INSUFFICIENT_PERMISSIONS')
    }

    next()
  }
}

/**
 * 管理员权限检查中间件
 */
export const requireAdmin = requireRole('admin')

/**
 * 资源所有者检查中间件
 */
export const requireOwnership = (resourceUserIdField: string = 'userId') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AppError('需要登录', 401, 'AUTHENTICATION_REQUIRED')
    }

    // 管理员可以访问所有资源
    if (req.user.role === 'admin') {
      return next()
    }

    // 检查资源所有权
    const resourceUserId = req.body[resourceUserIdField] || req.params[resourceUserIdField]
    if (resourceUserId && resourceUserId !== req.user._id.toString()) {
      throw new AppError('无权访问此资源', 403, 'ACCESS_DENIED')
    }

    next()
  }
}

/**
 * API密钥认证中间件
 */
export const authenticateApiKey = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const apiKey = req.headers['x-api-key'] as string

    if (!apiKey) {
      throw new AppError('API密钥缺失', 401, 'MISSING_API_KEY')
    }

    // TODO: 实现API密钥验证逻辑
    // 这里应该从数据库中验证API密钥的有效性
    
    next()
  } catch (error) {
    next(error)
  }
}

/**
 * 订阅计划检查中间件
 */
export const requireSubscription = (requiredPlan: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AppError('需要登录', 401, 'AUTHENTICATION_REQUIRED')
    }

    const userPlan = req.user.subscription.plan
    const allowedPlans = Array.isArray(requiredPlan) ? requiredPlan : [requiredPlan]

    // 检查订阅是否有效
    if (req.user.subscription.expiresAt < new Date()) {
      throw new AppError('订阅已过期', 403, 'SUBSCRIPTION_EXPIRED')
    }

    // 检查计划权限（假设计划有层级：free < pro < enterprise）
    const planHierarchy = ['free', 'pro', 'enterprise']
    const userPlanIndex = planHierarchy.indexOf(userPlan)
    const requiredPlanIndex = Math.min(...allowedPlans.map(plan => planHierarchy.indexOf(plan)))

    if (userPlanIndex < requiredPlanIndex) {
      throw new AppError('需要升级订阅计划', 403, 'SUBSCRIPTION_UPGRADE_REQUIRED')
    }

    next()
  }
}

/**
 * Token使用量检查中间件
 */
export const checkTokenUsage = (estimatedTokens: number = 100) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AppError('需要登录', 401, 'AUTHENTICATION_REQUIRED')
    }

    const { tokensUsed, tokensLimit } = req.user.subscription
    
    if (tokensUsed + estimatedTokens > tokensLimit) {
      throw new AppError('Token使用量已达上限', 403, 'TOKEN_LIMIT_EXCEEDED')
    }

    next()
  }
}

/**
 * 更新用户最后登录时间
 */
export const updateLastLogin = async (userId: string): Promise<void> => {
  try {
    await User.findByIdAndUpdate(userId, { lastLoginAt: new Date() })
  } catch (error) {
    // 记录错误但不影响主流程
    console.error('更新最后登录时间失败:', error)
  }
}
