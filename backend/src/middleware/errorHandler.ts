import { Request, Response, NextFunction } from 'express'
import { logger } from '@utils/logger'

/**
 * 应用错误类
 */
export class AppError extends Error {
  public statusCode: number
  public isOperational: boolean
  public code?: string

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = true
    this.code = code

    Error.captureStackTrace(this, this.constructor)
  }
}

/**
 * 错误处理中间件
 */
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = 500
  let message = '服务器内部错误'
  let code = 'INTERNAL_SERVER_ERROR'

  // 处理不同类型的错误
  if (error instanceof AppError) {
    statusCode = error.statusCode
    message = error.message
    code = error.code || 'APP_ERROR'
  } else if (error.name === 'ValidationError') {
    statusCode = 400
    message = '数据验证失败'
    code = 'VALIDATION_ERROR'
  } else if (error.name === 'CastError') {
    statusCode = 400
    message = '无效的数据格式'
    code = 'CAST_ERROR'
  } else if (error.name === 'MongoServerError' && (error as any).code === 11000) {
    statusCode = 409
    message = '数据已存在'
    code = 'DUPLICATE_ERROR'
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401
    message = '无效的访问令牌'
    code = 'INVALID_TOKEN'
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401
    message = '访问令牌已过期'
    code = 'TOKEN_EXPIRED'
  } else if (error.name === 'MulterError') {
    statusCode = 400
    message = '文件上传错误'
    code = 'FILE_UPLOAD_ERROR'
  }

  // 记录错误日志
  if (statusCode >= 500) {
    logger.error('服务器错误:', {
      error: error.message,
      stack: error.stack,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    })
  } else {
    logger.warn('客户端错误:', {
      error: error.message,
      url: req.url,
      method: req.method,
      ip: req.ip,
    })
  }

  // 返回错误响应
  res.status(statusCode).json({
    success: false,
    error: {
      code,
      message,
      ...(process.env.NODE_ENV === 'development' && { stack: error.stack }),
    },
    timestamp: new Date().toISOString(),
  })
}

/**
 * 404错误处理
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: '请求的资源不存在',
    },
    timestamp: new Date().toISOString(),
  })
}

/**
 * 异步错误捕获装饰器
 */
export const catchAsync = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}
