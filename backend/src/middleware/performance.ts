import { Request, Response, NextFunction } from 'express'
import Redis from 'ioredis'
import { promisify } from 'util'

/**
 * 性能优化中间件
 * 提供缓存、响应优化等功能
 */

// Redis缓存客户端
let redisClient: Redis | null = null

// 初始化Redis连接
export const initRedis = () => {
  try {
    redisClient = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    })

    redisClient.on('connect', () => {
      console.log('✅ Redis连接成功')
    })

    redisClient.on('error', (err) => {
      console.error('❌ Redis连接错误:', err)
    })

    return redisClient
  } catch (error) {
    console.error('❌ Redis初始化失败:', error)
    return null
  }
}

// 获取Redis客户端
export const getRedisClient = () => redisClient

// 缓存中间件
export const cacheMiddleware = (duration: number = 300) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // 只缓存GET请求
    if (req.method !== 'GET') {
      return next()
    }

    // 跳过需要认证的请求
    if (req.headers.authorization) {
      return next()
    }

    if (!redisClient) {
      return next()
    }

    try {
      const cacheKey = `cache:${req.originalUrl}`
      const cachedData = await redisClient.get(cacheKey)

      if (cachedData) {
        console.log(`🎯 缓存命中: ${cacheKey}`)
        res.setHeader('X-Cache', 'HIT')
        return res.json(JSON.parse(cachedData))
      }

      // 拦截响应
      const originalSend = res.json
      res.json = function(data: any) {
        // 只缓存成功的响应
        if (res.statusCode === 200) {
          redisClient?.setex(cacheKey, duration, JSON.stringify(data))
          console.log(`💾 数据已缓存: ${cacheKey}`)
        }
        res.setHeader('X-Cache', 'MISS')
        return originalSend.call(this, data)
      }

      next()
    } catch (error) {
      console.error('缓存中间件错误:', error)
      next()
    }
  }
}

// 清除缓存
export const clearCache = async (pattern: string = '*') => {
  if (!redisClient) return

  try {
    const keys = await redisClient.keys(`cache:${pattern}`)
    if (keys.length > 0) {
      await redisClient.del(...keys)
      console.log(`🧹 已清除${keys.length}个缓存项`)
    }
  } catch (error) {
    console.error('清除缓存失败:', error)
  }
}

// 响应时间监控中间件
export const responseTimeMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = process.hrtime.bigint()

  res.on('finish', () => {
    const end = process.hrtime.bigint()
    const duration = Number(end - start) / 1000000 // 转换为毫秒

    // 设置响应时间头
    res.setHeader('X-Response-Time', `${duration.toFixed(2)}ms`)

    // 记录慢请求
    if (duration > 1000) {
      console.warn(`🐌 慢请求警告: ${req.method} ${req.path} - ${duration.toFixed(2)}ms`)
    }

    // 记录性能指标
    if (process.env.NODE_ENV === 'development') {
      console.log(`⏱️  ${req.method} ${req.path} - ${duration.toFixed(2)}ms`)
    }
  })

  next()
}

// 内存使用监控中间件
export const memoryMonitorMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const memUsage = process.memoryUsage()
  const memUsageMB = {
    rss: Math.round(memUsage.rss / 1024 / 1024),
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
    external: Math.round(memUsage.external / 1024 / 1024),
  }

  // 内存使用过高警告
  if (memUsageMB.heapUsed > 500) {
    console.warn(`🚨 内存使用过高: ${memUsageMB.heapUsed}MB`)
  }

  // 添加内存使用信息到响应头（仅开发环境）
  if (process.env.NODE_ENV === 'development') {
    res.setHeader('X-Memory-Usage', JSON.stringify(memUsageMB))
  }

  next()
}

// 数据库查询优化中间件
export const dbOptimizationMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // 记录数据库查询开始时间
  req.dbQueryStart = Date.now()
  
  // 拦截响应以记录查询时间
  const originalSend = res.json
  res.json = function(data: any) {
    if (req.dbQueryStart) {
      const queryTime = Date.now() - req.dbQueryStart
      
      // 记录慢查询
      if (queryTime > 500) {
        console.warn(`🐌 慢数据库查询: ${req.method} ${req.path} - ${queryTime}ms`)
      }
      
      // 添加查询时间到响应头
      res.setHeader('X-DB-Query-Time', `${queryTime}ms`)
    }
    
    return originalSend.call(this, data)
  }
  
  next()
}

// 请求大小限制中间件
export const requestSizeLimitMiddleware = (maxSize: number = 10 * 1024 * 1024) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.headers['content-length'] || '0')
    
    if (contentLength > maxSize) {
      return res.status(413).json({
        success: false,
        message: `请求体过大，最大允许${Math.round(maxSize / 1024 / 1024)}MB`
      })
    }
    
    next()
  }
}

// 并发请求限制中间件
export const concurrencyLimitMiddleware = (maxConcurrent: number = 100) => {
  let currentRequests = 0
  
  return (req: Request, res: Response, next: NextFunction) => {
    if (currentRequests >= maxConcurrent) {
      return res.status(503).json({
        success: false,
        message: '服务器繁忙，请稍后再试'
      })
    }
    
    currentRequests++
    
    res.on('finish', () => {
      currentRequests--
    })
    
    res.on('close', () => {
      currentRequests--
    })
    
    next()
  }
}

// 健康检查中间件
export const healthCheckMiddleware = (req: Request, res: Response, next: NextFunction) => {
  if (req.path === '/health') {
    const memUsage = process.memoryUsage()
    const uptime = process.uptime()
    
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Math.round(uptime),
      memory: {
        used: Math.round(memUsage.heapUsed / 1024 / 1024),
        total: Math.round(memUsage.heapTotal / 1024 / 1024),
      },
      redis: redisClient?.status === 'ready' ? 'connected' : 'disconnected',
      version: process.env.npm_package_version || '1.0.0',
    }
    
    return res.json(healthData)
  }
  
  next()
}

// 静态资源优化中间件
export const staticOptimizationMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // 设置静态资源缓存头
  if (req.path.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
    res.setHeader('Cache-Control', 'public, max-age=31536000') // 1年
    res.setHeader('Expires', new Date(Date.now() + 31536000000).toUTCString())
  }
  
  // 设置API响应缓存头
  if (req.path.startsWith('/api/')) {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate')
    res.setHeader('Pragma', 'no-cache')
    res.setHeader('Expires', '0')
  }
  
  next()
}

// 性能指标收集
export const performanceMetrics = {
  requests: 0,
  errors: 0,
  totalResponseTime: 0,
  averageResponseTime: 0,
  
  // 记录请求
  recordRequest: (responseTime: number, isError: boolean = false) => {
    performanceMetrics.requests++
    performanceMetrics.totalResponseTime += responseTime
    performanceMetrics.averageResponseTime = performanceMetrics.totalResponseTime / performanceMetrics.requests
    
    if (isError) {
      performanceMetrics.errors++
    }
  },
  
  // 获取指标
  getMetrics: () => ({
    totalRequests: performanceMetrics.requests,
    totalErrors: performanceMetrics.errors,
    errorRate: performanceMetrics.requests > 0 ? (performanceMetrics.errors / performanceMetrics.requests * 100).toFixed(2) + '%' : '0%',
    averageResponseTime: performanceMetrics.averageResponseTime.toFixed(2) + 'ms',
    uptime: process.uptime(),
    memoryUsage: process.memoryUsage(),
  }),
  
  // 重置指标
  reset: () => {
    performanceMetrics.requests = 0
    performanceMetrics.errors = 0
    performanceMetrics.totalResponseTime = 0
    performanceMetrics.averageResponseTime = 0
  }
}

// 性能指标中间件
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now()
  
  res.on('finish', () => {
    const responseTime = Date.now() - start
    const isError = res.statusCode >= 400
    performanceMetrics.recordRequest(responseTime, isError)
  })
  
  next()
}

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      dbQueryStart?: number
    }
  }
}
