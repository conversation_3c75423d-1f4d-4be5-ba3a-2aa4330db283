import { Request, Response, NextFunction } from 'express'
import rateLimit from 'express-rate-limit'
import helmet from 'helmet'
import cors from 'cors'
import compression from 'compression'
import { body, validationResult } from 'express-validator'

/**
 * 安全中间件配置
 * 提供全面的安全防护措施
 */

// CORS配置
export const corsOptions = {
  origin: function (origin: string | undefined, callback: Function) {
    // 允许的域名列表
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5173',
      'https://ai-digital-robots.com',
      process.env.FRONTEND_URL
    ].filter(Boolean)

    // 开发环境允许所有来源
    if (process.env.NODE_ENV === 'development') {
      return callback(null, true)
    }

    // 生产环境检查来源
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true)
    } else {
      callback(new Error('不允许的CORS来源'))
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  maxAge: 86400 // 24小时
}

// Helmet安全头配置
export const helmetOptions = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.openai.com", "https://api.anthropic.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}

// 通用限流配置
export const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP最多1000次请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // 跳过健康检查和静态资源
    return req.path === '/health' || req.path.startsWith('/static/')
  }
})

// 认证接口限流（更严格）
export const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 10, // 每个IP最多10次认证请求
  message: {
    success: false,
    message: '认证请求过于频繁，请15分钟后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true // 成功的请求不计入限制
})

// API接口限流
export const apiLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 100, // 每个IP每分钟最多100次API请求
  message: {
    success: false,
    message: 'API请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
})

// 文件上传限流
export const uploadLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 50, // 每个IP每小时最多50次上传
  message: {
    success: false,
    message: '文件上传过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
})

// 压缩中间件配置
export const compressionOptions = {
  filter: (req: Request, res: Response) => {
    // 不压缩已经压缩的内容
    if (req.headers['x-no-compression']) {
      return false
    }
    return compression.filter(req, res)
  },
  level: 6, // 压缩级别 (1-9)
  threshold: 1024, // 只压缩大于1KB的响应
}

// 输入验证中间件
export const validateInput = (validations: any[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // 运行所有验证
    await Promise.all(validations.map(validation => validation.run(req)))

    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      })
    }

    next()
  }
}

// 常用验证规则
export const validationRules = {
  // 邮箱验证
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址'),

  // 密码验证
  password: body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('密码长度必须在8-128位之间')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含大小写字母和数字'),

  // 用户名验证
  username: body('username')
    .isLength({ min: 3, max: 30 })
    .withMessage('用户名长度必须在3-30位之间')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('用户名只能包含字母、数字、下划线和连字符'),

  // MongoDB ObjectId验证
  objectId: (field: string) => body(field)
    .isMongoId()
    .withMessage(`${field}必须是有效的ID格式`),

  // 字符串长度验证
  stringLength: (field: string, min: number, max: number) => body(field)
    .isLength({ min, max })
    .withMessage(`${field}长度必须在${min}-${max}位之间`)
    .trim()
    .escape(),

  // 数组验证
  array: (field: string, maxLength: number = 10) => body(field)
    .isArray({ max: maxLength })
    .withMessage(`${field}必须是数组且长度不超过${maxLength}`),

  // 布尔值验证
  boolean: (field: string) => body(field)
    .isBoolean()
    .withMessage(`${field}必须是布尔值`),

  // 数字验证
  number: (field: string, min?: number, max?: number) => {
    let validator = body(field).isNumeric().withMessage(`${field}必须是数字`)
    if (min !== undefined) {
      validator = validator.isFloat({ min }).withMessage(`${field}不能小于${min}`)
    }
    if (max !== undefined) {
      validator = validator.isFloat({ max }).withMessage(`${field}不能大于${max}`)
    }
    return validator
  }
}

// 请求日志中间件
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now()
  
  // 记录请求信息
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.path} - IP: ${req.ip}`)
  
  // 监听响应完成
  res.on('finish', () => {
    const duration = Date.now() - start
    const statusColor = res.statusCode >= 400 ? '\x1b[31m' : '\x1b[32m' // 红色表示错误，绿色表示成功
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.path} - ${statusColor}${res.statusCode}\x1b[0m - ${duration}ms`)
  })
  
  next()
}

// 错误处理中间件
export const errorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  console.error('错误详情:', err)

  // 默认错误信息
  let status = 500
  let message = '服务器内部错误'

  // 根据错误类型设置响应
  if (err.name === 'ValidationError') {
    status = 400
    message = '数据验证失败'
  } else if (err.name === 'UnauthorizedError') {
    status = 401
    message = '未授权访问'
  } else if (err.name === 'CastError') {
    status = 400
    message = '无效的ID格式'
  } else if (err.code === 11000) {
    status = 400
    message = '数据已存在'
  } else if (err.message) {
    message = err.message
  }

  // 生产环境不暴露详细错误信息
  if (process.env.NODE_ENV === 'production' && status === 500) {
    message = '服务器内部错误'
  }

  res.status(status).json({
    success: false,
    message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  })
}

// 404处理中间件
export const notFoundHandler = (req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    message: '请求的资源不存在'
  })
}

// 安全头设置中间件
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // 移除敏感的服务器信息
  res.removeHeader('X-Powered-By')
  
  // 设置安全头
  res.setHeader('X-Content-Type-Options', 'nosniff')
  res.setHeader('X-Frame-Options', 'DENY')
  res.setHeader('X-XSS-Protection', '1; mode=block')
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  next()
}
