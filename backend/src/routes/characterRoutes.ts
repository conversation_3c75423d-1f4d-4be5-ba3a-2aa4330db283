import { Router } from 'express'
import { CharacterController } from '@/controllers/characterController'
import { authenticateToken, optionalAuth, requireSubscription } from '@/middleware/auth'
import { createValidationMiddleware, characterValidation } from '@/utils/validation'

const router = Router()

// 公开路由（可选认证）
router.get('/public',
  optionalAuth,
  CharacterController.getPublicCharacters
)

router.get('/models',
  CharacterController.getAvailableModels
)

router.get('/voices',
  CharacterController.getAvailableVoices
)

// 需要认证的路由
router.use(authenticateToken)

// 获取用户的数字人列表
router.get('/',
  CharacterController.getUserCharacters
)

// 创建数字人
router.post('/',
  createValidationMiddleware(characterValidation.create),
  requireSubscription(['pro', 'enterprise']), // 专业版以上才能创建数字人
  CharacterController.createCharacter
)

// 测试数字人配置
router.post('/test-config',
  CharacterController.testCharacterConfig
)

// 获取数字人详情
router.get('/:id',
  CharacterController.getCharacter
)

// 更新数字人
router.put('/:id',
  createValidationMiddleware(characterValidation.update),
  CharacterController.updateCharacter
)

// 克隆数字人
router.post('/:id/clone',
  CharacterController.cloneCharacter
)

// 获取数字人统计
router.get('/:id/stats',
  CharacterController.getCharacterStats
)

// 绑定知识库
router.post('/:id/knowledge',
  CharacterController.bindKnowledgeBase
)

// 解绑知识库
router.delete('/:id/knowledge',
  CharacterController.unbindKnowledgeBase
)

// 删除数字人
router.delete('/:id',
  CharacterController.deleteCharacter
)

export { router as characterRoutes }
