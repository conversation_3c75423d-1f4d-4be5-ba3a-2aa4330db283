import { Router } from 'express'
import { AuthController } from '@/controllers/authController'
import { authenticateToken } from '@/middleware/auth'
import { createValidationMiddleware, userValidation } from '@/utils/validation'

const router = Router()

// 公开路由（无需认证）
router.post('/register',
  createValidationMiddleware(userValidation.register),
  AuthController.register
)

router.post('/login',
  createValidationMiddleware(userValidation.login),
  AuthController.login
)

router.post('/verify-email',
  AuthController.verifyEmail
)

router.post('/forgot-password',
  AuthController.sendPasswordReset
)

router.post('/reset-password',
  AuthController.resetPassword
)

router.get('/check-email',
  AuthController.checkEmailExists
)

router.get('/check-username',
  AuthController.checkUsernameExists
)

// 需要认证的路由
router.use(authenticateToken)

router.post('/logout',
  AuthController.logout
)

router.post('/refresh',
  AuthController.refreshToken
)

router.get('/profile',
  AuthController.getProfile
)

router.put('/profile',
  createValidationMiddleware(userValidation.updateProfile),
  AuthController.updateProfile
)

router.post('/change-password',
  createValidationMiddleware(userValidation.changePassword),
  AuthController.changePassword
)

router.post('/send-verification',
  AuthController.sendEmailVerification
)

router.get('/stats',
  AuthController.getUserStats
)

router.delete('/account',
  AuthController.deleteAccount
)

export { router as authRoutes }
