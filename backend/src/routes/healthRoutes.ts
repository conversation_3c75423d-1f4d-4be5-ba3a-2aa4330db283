import { Router } from 'express'
import mongoose from 'mongoose'
import Redis from 'ioredis'
import { logger } from '@/utils/logger'

const router = Router()

// Redis客户端实例
let redisClient: Redis | null = null

try {
  redisClient = new Redis(process.env.REDIS_URL || 'redis://localhost:6379')
} catch (error) {
  logger.error('Redis连接失败:', error)
}

/**
 * 基础健康检查
 */
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
  })
})

/**
 * 详细健康检查
 */
router.get('/health/detailed', async (req, res) => {
  const healthChecks = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
    checks: {
      database: await checkDatabase(),
      redis: await checkRedis(),
      memory: checkMemory(),
      disk: await checkDisk(),
      externalServices: await checkExternalServices(),
    },
  }

  // 检查是否有任何服务不健康
  const hasUnhealthyService = Object.values(healthChecks.checks).some(
    (check: any) => check.status !== 'healthy'
  )

  const statusCode = hasUnhealthyService ? 503 : 200
  if (hasUnhealthyService) {
    healthChecks.status = 'unhealthy'
  }

  res.status(statusCode).json(healthChecks)
})

/**
 * 就绪检查
 */
router.get('/ready', async (req, res) => {
  try {
    // 检查关键服务
    const dbCheck = await checkDatabase()
    const redisCheck = await checkRedis()

    if (dbCheck.status === 'healthy' && redisCheck.status === 'healthy') {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
        checks: {
          database: dbCheck,
          redis: redisCheck,
        },
      })
    } else {
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        checks: {
          database: dbCheck,
          redis: redisCheck,
        },
      })
    }
  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error),
    })
  }
})

/**
 * 存活检查
 */
router.get('/live', (req, res) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  })
})

/**
 * 检查数据库连接
 */
async function checkDatabase() {
  try {
    const start = Date.now()
    await mongoose.connection.db.admin().ping()
    const responseTime = Date.now() - start

    return {
      status: 'healthy',
      responseTime: `${responseTime}ms`,
      connection: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : String(error),
      connection: 'failed',
    }
  }
}

/**
 * 检查Redis连接
 */
async function checkRedis() {
  if (!redisClient) {
    return {
      status: 'unhealthy',
      error: 'Redis客户端未初始化',
    }
  }

  try {
    const start = Date.now()
    await redisClient.ping()
    const responseTime = Date.now() - start

    return {
      status: 'healthy',
      responseTime: `${responseTime}ms`,
      connection: 'connected',
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : String(error),
      connection: 'failed',
    }
  }
}

/**
 * 检查内存使用情况
 */
function checkMemory() {
  const memUsage = process.memoryUsage()
  const totalMemory = memUsage.heapTotal
  const usedMemory = memUsage.heapUsed
  const memoryUsagePercent = (usedMemory / totalMemory) * 100

  return {
    status: memoryUsagePercent > 90 ? 'warning' : 'healthy',
    heapUsed: `${Math.round(usedMemory / 1024 / 1024)}MB`,
    heapTotal: `${Math.round(totalMemory / 1024 / 1024)}MB`,
    usagePercent: `${memoryUsagePercent.toFixed(2)}%`,
    external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
    rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
  }
}

/**
 * 检查磁盘空间
 */
async function checkDisk() {
  try {
    const fs = await import('fs/promises')
    const stats = await fs.statfs('.')
    
    const total = stats.bavail * stats.bsize
    const free = stats.bfree * stats.bsize
    const used = total - free
    const usagePercent = (used / total) * 100

    return {
      status: usagePercent > 90 ? 'warning' : 'healthy',
      total: `${Math.round(total / 1024 / 1024 / 1024)}GB`,
      used: `${Math.round(used / 1024 / 1024 / 1024)}GB`,
      free: `${Math.round(free / 1024 / 1024 / 1024)}GB`,
      usagePercent: `${usagePercent.toFixed(2)}%`,
    }
  } catch (error) {
    return {
      status: 'unknown',
      error: error instanceof Error ? error.message : String(error),
    }
  }
}

/**
 * 检查外部服务
 */
async function checkExternalServices() {
  const services = []

  // 检查OpenAI API
  if (process.env.OPENAI_API_KEY) {
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        },
        signal: AbortSignal.timeout(5000),
      })
      
      services.push({
        name: 'OpenAI API',
        status: response.ok ? 'healthy' : 'unhealthy',
        responseCode: response.status,
      })
    } catch (error) {
      services.push({
        name: 'OpenAI API',
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
      })
    }
  }

  // 检查Anthropic API
  if (process.env.ANTHROPIC_API_KEY) {
    try {
      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'x-api-key': process.env.ANTHROPIC_API_KEY,
          'anthropic-version': '2023-06-01',
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'test' }],
        }),
        signal: AbortSignal.timeout(5000),
      })
      
      services.push({
        name: 'Anthropic API',
        status: response.status < 500 ? 'healthy' : 'unhealthy',
        responseCode: response.status,
      })
    } catch (error) {
      services.push({
        name: 'Anthropic API',
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
      })
    }
  }

  return services
}

export { router as healthRoutes }
