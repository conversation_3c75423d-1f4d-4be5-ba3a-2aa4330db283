import { Router } from 'express'
import { ConversationController } from '@/controllers/conversationController'
import { authenticateToken, checkTokenUsage } from '@/middleware/auth'
import { createValidationMiddleware, conversationValidation } from '@/utils/validation'

const router = Router()

// 所有路由都需要认证
router.use(authenticateToken)

// 获取对话列表
router.get('/',
  ConversationController.getConversations
)

// 创建对话
router.post('/',
  createValidationMiddleware(conversationValidation.create),
  ConversationController.createConversation
)

// 获取对话统计
router.get('/stats',
  ConversationController.getConversationStats
)

// 获取对话详情
router.get('/:id',
  ConversationController.getConversation
)

// 发送消息
router.post('/:id/messages',
  createValidationMiddleware(conversationValidation.sendMessage),
  checkTokenUsage(100), // 预估100个token
  ConversationController.sendMessage
)

// 更新对话标题
router.put('/:id/title',
  ConversationController.updateConversationTitle
)

// 归档对话
router.post('/:id/archive',
  ConversationController.archiveConversation
)

// 恢复对话
router.post('/:id/restore',
  ConversationController.restoreConversation
)

// 删除对话
router.delete('/:id',
  ConversationController.deleteConversation
)

export { router as conversationRoutes }
