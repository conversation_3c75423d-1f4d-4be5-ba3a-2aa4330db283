import { Router } from 'express'
import { KnowledgeController } from '@/controllers/knowledgeController'
import { authenticateToken, requireSubscription } from '@/middleware/auth'
import { createValidationMiddleware, knowledgeValidation } from '@/utils/validation'

const router = Router()

// 公开的健康检查端点（不需要认证）
router.get('/health',
  KnowledgeController.healthCheck
)

// 获取服务状态（不需要认证）
router.get('/status',
  KnowledgeController.getServiceStatus
)

// 所有其他路由都需要认证
router.use(authenticateToken)

// 获取支持的文档类型
router.get('/document-types',
  KnowledgeController.getSupportedDocumentTypes
)

// 获取知识库列表
router.get('/',
  KnowledgeController.getKnowledgeList
)

// 创建知识库
router.post('/',
  createValidationMiddleware(knowledgeValidation.create),
  requireSubscription(['pro', 'enterprise']), // 专业版以上才能创建知识库
  KnowledgeController.createKnowledge
)

// 获取知识库详情
router.get('/:id',
  KnowledgeController.getKnowledge
)

// 更新知识库
router.put('/:id',
  createValidationMiddleware(knowledgeValidation.update),
  KnowledgeController.updateKnowledge
)

// 获取知识库统计
router.get('/:id/stats',
  KnowledgeController.getKnowledgeStats
)

// 获取文档处理进度
router.get('/:id/progress',
  KnowledgeController.getDocumentProgress
)

// 搜索知识库
router.post('/:id/search',
  KnowledgeController.searchKnowledge
)

// 上传文档
router.post('/:id/documents',
  KnowledgeController.uploadDocument
)

// 批量删除文档
router.delete('/:id/documents/batch',
  KnowledgeController.batchDeleteDocuments
)

// 删除单个文档
router.delete('/:id/documents/:documentId',
  KnowledgeController.deleteDocument
)

// 重新处理文档
router.post('/:id/documents/:documentId/reprocess',
  KnowledgeController.reprocessDocument
)

// 删除知识库
router.delete('/:id',
  KnowledgeController.deleteKnowledge
)

// 管理员路由 - 重新初始化服务
router.post('/admin/reinitialize',
  // TODO: 添加管理员权限验证中间件
  KnowledgeController.reinitializeService
)

// 管理员路由 - 重置降级统计
router.post('/admin/reset-fallback-stats',
  // TODO: 添加管理员权限验证中间件
  KnowledgeController.resetFallbackStats
)

export { router as knowledgeRoutes }
