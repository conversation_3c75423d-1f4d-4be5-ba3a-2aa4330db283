import { Request, Response } from 'express'
import { ConversationService } from '@/services/conversationService'
import { catchAsync } from '@/middleware/errorHandler'
import { successResponse, createdResponse, paginatedResponse } from '@/utils/response'

/**
 * 对话控制器
 */
export class ConversationController {
  /**
   * 创建对话
   */
  static createConversation = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { characterId, title } = req.body

    const conversation = await ConversationService.createConversation(
      userId,
      characterId,
      title
    )

    return createdResponse(res, conversation, '对话创建成功')
  })

  /**
   * 获取对话列表
   */
  static getConversations = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { page, limit, status, characterId } = req.query

    const result = await ConversationService.getConversations(userId, {
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      status: status as string,
      characterId: characterId as string,
    })

    return paginatedResponse(
      res,
      result.conversations,
      result.total,
      result.page,
      result.limit,
      '获取对话列表成功'
    )
  })

  /**
   * 获取对话详情
   */
  static getConversation = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params

    const conversation = await ConversationService.getConversation(id, userId)

    return successResponse(res, conversation, '获取对话详情成功')
  })

  /**
   * 发送消息
   */
  static sendMessage = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params
    const { content, useRAG = false } = req.body

    const result = await ConversationService.sendMessage(id, userId, content, {
      stream: false,
      useRAG,
    })

    return successResponse(res, result, '消息发送成功')
  })

  /**
   * 删除对话
   */
  static deleteConversation = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params

    await ConversationService.deleteConversation(id, userId)

    return successResponse(res, null, '对话删除成功')
  })

  /**
   * 归档对话
   */
  static archiveConversation = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params

    const conversation = await ConversationService.getConversation(id, userId)
    await conversation.archive()

    return successResponse(res, conversation, '对话归档成功')
  })

  /**
   * 恢复对话
   */
  static restoreConversation = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params

    const conversation = await ConversationService.getConversation(id, userId)
    await conversation.restore()

    return successResponse(res, conversation, '对话恢复成功')
  })

  /**
   * 更新对话标题
   */
  static updateConversationTitle = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params
    const { title } = req.body

    const conversation = await ConversationService.getConversation(id, userId)
    conversation.title = title
    await conversation.save()

    return successResponse(res, conversation, '对话标题更新成功')
  })

  /**
   * 获取对话统计
   */
  static getConversationStats = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()

    // TODO: 实现对话统计逻辑
    const stats = {
      totalConversations: 0,
      activeConversations: 0,
      totalMessages: 0,
      averageMessagesPerConversation: 0,
      mostUsedCharacter: null,
      recentActivity: [],
    }

    return successResponse(res, stats, '获取对话统计成功')
  })
}
