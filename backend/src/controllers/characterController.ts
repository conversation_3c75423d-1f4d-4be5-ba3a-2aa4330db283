import { Request, Response } from 'express'
import { CharacterService } from '@/services/characterService'
import { catchAsync } from '@/middleware/errorHandler'
import { successResponse, createdResponse, paginatedResponse } from '@/utils/response'
import { CreateCharacterRequest } from '@/types'

/**
 * 数字人控制器
 */
export class CharacterController {
  /**
   * 创建数字人
   */
  static createCharacter = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const characterData: CreateCharacterRequest = req.body

    const character = await CharacterService.createCharacter(userId, characterData)

    return createdResponse(res, character, '数字人创建成功')
  })

  /**
   * 获取用户的数字人列表
   */
  static getUserCharacters = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { page, limit, sortBy, sortOrder, search } = req.query

    const result = await CharacterService.getUserCharacters(userId, {
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'asc' | 'desc',
      search: search as string,
    })

    return paginatedResponse(
      res,
      result.characters,
      result.total,
      result.page,
      result.limit,
      '获取数字人列表成功'
    )
  })

  /**
   * 获取公开数字人列表
   */
  static getPublicCharacters = catchAsync(async (req: Request, res: Response) => {
    const { page, limit, sortBy, sortOrder, search, tags, language } = req.query

    const filters: any = {}
    if (tags) {
      filters.tags = Array.isArray(tags) ? tags : [tags]
    }
    if (language) {
      filters.language = language
    }

    const result = await CharacterService.getPublicCharacters({
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'asc' | 'desc',
      search: search as string,
      filters,
    })

    return paginatedResponse(
      res,
      result.characters,
      result.total,
      result.page,
      result.limit,
      '获取公开数字人列表成功'
    )
  })

  /**
   * 获取数字人详情
   */
  static getCharacter = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params
    const userId = req.user?._id?.toString()

    const character = await CharacterService.getCharacter(id, userId)

    return successResponse(res, character, '获取数字人详情成功')
  })

  /**
   * 更新数字人
   */
  static updateCharacter = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params
    const updates = req.body

    const character = await CharacterService.updateCharacter(id, userId, updates)

    return successResponse(res, character, '数字人更新成功')
  })

  /**
   * 删除数字人
   */
  static deleteCharacter = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params

    await CharacterService.deleteCharacter(id, userId)

    return successResponse(res, null, '数字人删除成功')
  })

  /**
   * 克隆数字人
   */
  static cloneCharacter = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params
    const { name } = req.body

    const character = await CharacterService.cloneCharacter(id, userId, name)

    return createdResponse(res, character, '数字人克隆成功')
  })

  /**
   * 绑定知识库
   */
  static bindKnowledgeBase = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params
    const { knowledgeId } = req.body

    const character = await CharacterService.bindKnowledgeBase(id, userId, knowledgeId)

    return successResponse(res, character, '知识库绑定成功')
  })

  /**
   * 解绑知识库
   */
  static unbindKnowledgeBase = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params

    const character = await CharacterService.unbindKnowledgeBase(id, userId)

    return successResponse(res, character, '知识库解绑成功')
  })

  /**
   * 获取数字人统计
   */
  static getCharacterStats = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params

    const stats = await CharacterService.getCharacterStats(id, userId)

    return successResponse(res, stats, '获取数字人统计成功')
  })

  /**
   * 获取可用的AI模型
   */
  static getAvailableModels = catchAsync(async (req: Request, res: Response) => {
    const { provider } = req.query

    // 从AI服务获取支持的模型
    const models = {
      openai: [
        { id: 'gpt-4', name: 'GPT-4', description: '最强大的模型，适合复杂任务' },
        { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo', description: '更快的GPT-4版本' },
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: '快速且经济的选择' },
      ],
      anthropic: [
        { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: '最强大的Claude模型' },
        { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', description: '平衡性能和速度' },
        { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', description: '快速响应' },
      ],
    }

    const result = provider ? { [provider as string]: models[provider as keyof typeof models] } : models

    return successResponse(res, result, '获取可用模型成功')
  })

  /**
   * 获取可用的语音选项
   */
  static getAvailableVoices = catchAsync(async (req: Request, res: Response) => {
    const { provider } = req.query

    // 模拟语音选项数据
    const voices = {
      azure: [
        { id: 'zh-CN-XiaoxiaoNeural', name: '晓晓', gender: 'female', language: 'zh-CN' },
        { id: 'zh-CN-YunxiNeural', name: '云希', gender: 'male', language: 'zh-CN' },
        { id: 'zh-CN-YunyangNeural', name: '云扬', gender: 'male', language: 'zh-CN' },
      ],
      aliyun: [
        { id: 'xiaoyun', name: '小云', gender: 'female', language: 'zh-CN' },
        { id: 'xiaogang', name: '小刚', gender: 'male', language: 'zh-CN' },
      ],
    }

    const result = provider ? { [provider as string]: voices[provider as keyof typeof voices] } : voices

    return successResponse(res, result, '获取可用语音成功')
  })

  /**
   * 测试数字人配置
   */
  static testCharacterConfig = catchAsync(async (req: Request, res: Response) => {
    const { aiModel, testMessage = '你好，请介绍一下自己。' } = req.body

    // TODO: 实现配置测试逻辑
    // 这里可以发送一个测试消息来验证配置是否正确

    const testResult = {
      success: true,
      response: '配置测试成功！AI模型响应正常。',
      latency: 1200, // 毫秒
      tokensUsed: 25,
    }

    return successResponse(res, testResult, '配置测试完成')
  })
}
