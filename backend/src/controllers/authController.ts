import { Request, Response } from 'express'
import { AuthService } from '@/services/authService'
import { User } from '@/models/User'
import { catchAsync } from '@/middleware/errorHandler'
import { successResponse, createdResponse, errorResponse } from '@/utils/response'
import { LoginRequest, RegisterRequest } from '@/types'

/**
 * 认证控制器
 */
export class AuthController {
  /**
   * 用户注册
   */
  static register = catchAsync(async (req: Request, res: Response) => {
    const userData: RegisterRequest = req.body

    // 验证确认密码
    if (userData.password !== userData.confirmPassword) {
      return errorResponse(res, '确认密码不匹配', 400, 'PASSWORD_MISMATCH')
    }

    const { user, token } = await AuthService.register(userData)

    return createdResponse(res, {
      user: user.toSafeObject(),
      token,
    }, '注册成功')
  })

  /**
   * 用户登录
   */
  static login = catchAsync(async (req: Request, res: Response) => {
    const loginData: LoginRequest = req.body

    const { user, token } = await AuthService.login(loginData)

    return successResponse(res, {
      user: user.toSafeObject(),
      token,
    }, '登录成功')
  })

  /**
   * 用户登出
   */
  static logout = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()

    await AuthService.logout(userId)

    return successResponse(res, null, '登出成功')
  })

  /**
   * 刷新令牌
   */
  static refreshToken = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()

    const { user, token } = await AuthService.refreshToken(userId)

    return successResponse(res, {
      user: user.toSafeObject(),
      token,
    }, '令牌刷新成功')
  })

  /**
   * 获取当前用户信息
   */
  static getProfile = catchAsync(async (req: Request, res: Response) => {
    const user = req.user

    return successResponse(res, user.toSafeObject(), '获取用户信息成功')
  })

  /**
   * 更新用户信息
   */
  static updateProfile = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id
    const updates = req.body

    // 不允许更新的字段
    const restrictedFields = ['email', 'passwordHash', 'role', 'subscription', 'isEmailVerified']
    restrictedFields.forEach(field => delete updates[field])

    const user = await User.findByIdAndUpdate(
      userId,
      updates,
      { new: true, runValidators: true }
    )

    if (!user) {
      return errorResponse(res, '用户不存在', 404, 'USER_NOT_FOUND')
    }

    return successResponse(res, user.toSafeObject(), '用户信息更新成功')
  })

  /**
   * 修改密码
   */
  static changePassword = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { currentPassword, newPassword } = req.body

    await AuthService.changePassword(userId, currentPassword, newPassword)

    return successResponse(res, null, '密码修改成功')
  })

  /**
   * 发送邮箱验证
   */
  static sendEmailVerification = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()

    await AuthService.sendEmailVerification(userId)

    return successResponse(res, null, '验证邮件已发送')
  })

  /**
   * 验证邮箱
   */
  static verifyEmail = catchAsync(async (req: Request, res: Response) => {
    const { token } = req.body

    await AuthService.verifyEmail(token)

    return successResponse(res, null, '邮箱验证成功')
  })

  /**
   * 发送密码重置邮件
   */
  static sendPasswordReset = catchAsync(async (req: Request, res: Response) => {
    const { email } = req.body

    await AuthService.sendPasswordReset(email)

    return successResponse(res, null, '密码重置邮件已发送')
  })

  /**
   * 重置密码
   */
  static resetPassword = catchAsync(async (req: Request, res: Response) => {
    const { token, newPassword } = req.body

    await AuthService.resetPassword(token, newPassword)

    return successResponse(res, null, '密码重置成功')
  })

  /**
   * 检查邮箱是否存在
   */
  static checkEmailExists = catchAsync(async (req: Request, res: Response) => {
    const { email } = req.query

    const user = await User.findOne({ email: (email as string).toLowerCase() })

    return successResponse(res, {
      exists: !!user
    }, '检查完成')
  })

  /**
   * 检查用户名是否存在
   */
  static checkUsernameExists = catchAsync(async (req: Request, res: Response) => {
    const { username } = req.query

    const user = await User.findOne({ username: username as string })

    return successResponse(res, {
      exists: !!user
    }, '检查完成')
  })

  /**
   * 获取用户统计信息
   */
  static getUserStats = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id

    // TODO: 实现用户统计信息获取
    const stats = {
      charactersCount: 0,
      conversationsCount: 0,
      messagesCount: 0,
      tokensUsed: req.user.subscription.tokensUsed,
      tokensLimit: req.user.subscription.tokensLimit,
      subscriptionPlan: req.user.subscription.plan,
      subscriptionExpiresAt: req.user.subscription.expiresAt,
    }

    return successResponse(res, stats, '获取统计信息成功')
  })

  /**
   * 删除账户
   */
  static deleteAccount = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id
    const { password } = req.body

    // 验证密码
    const user = await User.findById(userId).select('+passwordHash')
    if (!user) {
      return errorResponse(res, '用户不存在', 404, 'USER_NOT_FOUND')
    }

    const isPasswordValid = await user.comparePassword(password)
    if (!isPasswordValid) {
      return errorResponse(res, '密码错误', 400, 'INVALID_PASSWORD')
    }

    // TODO: 清理用户相关数据（数字人、对话、知识库等）

    // 删除用户
    await User.findByIdAndDelete(userId)

    // 清理缓存
    await AuthService.logout(userId)

    return successResponse(res, null, '账户删除成功')
  })
}
