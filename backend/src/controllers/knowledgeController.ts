import { Request, Response } from 'express'
import multer from 'multer'
import path from 'path'
import { KnowledgeService } from '@/services/knowledgeService'
import { enhancedKnowledgeService } from '@/services/enhancedKnowledgeService'
import { catchAsync } from '@/middleware/errorHandler'
import { successResponse, createdResponse, paginatedResponse, errorResponse } from '@/utils/response'
import { FileUploadInfo } from '@/types'

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, process.env.UPLOAD_DIR || 'uploads/')
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
  }
})

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.pdf', '.docx', '.txt', '.md', '.rtf']
    const fileExtension = path.extname(file.originalname).toLowerCase()
    
    if (allowedTypes.includes(fileExtension)) {
      cb(null, true)
    } else {
      cb(new Error(`不支持的文件类型: ${fileExtension}`))
    }
  }
})

/**
 * 知识库控制器
 */
export class KnowledgeController {
  /**
   * 创建知识库
   */
  static createKnowledge = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const knowledgeData = req.body

    const knowledge = await KnowledgeService.createKnowledge(userId, knowledgeData)

    return createdResponse(res, knowledge, '知识库创建成功')
  })

  /**
   * 获取知识库列表
   */
  static getKnowledgeList = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { page, limit, sortBy, sortOrder, search } = req.query

    const result = await KnowledgeService.getKnowledgeList(userId, {
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'asc' | 'desc',
      search: search as string,
    })

    return paginatedResponse(
      res,
      result.knowledgeList,
      result.total,
      result.page,
      result.limit,
      '获取知识库列表成功'
    )
  })

  /**
   * 获取知识库详情
   */
  static getKnowledge = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params

    const knowledge = await KnowledgeService.getKnowledge(id, userId)

    return successResponse(res, knowledge, '获取知识库详情成功')
  })

  /**
   * 更新知识库
   */
  static updateKnowledge = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params
    const updates = req.body

    const knowledge = await KnowledgeService.updateKnowledge(id, userId, updates)

    return successResponse(res, knowledge, '知识库更新成功')
  })

  /**
   * 删除知识库
   */
  static deleteKnowledge = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params

    await KnowledgeService.deleteKnowledge(id, userId)

    return successResponse(res, null, '知识库删除成功')
  })

  /**
   * 上传文档
   */
  static uploadDocument = [
    upload.single('document'),
    catchAsync(async (req: Request, res: Response) => {
      const userId = req.user._id.toString()
      const { id } = req.params

      if (!req.file) {
        return errorResponse(res, '请选择要上传的文件', 400, 'NO_FILE_UPLOADED')
      }

      const fileInfo: FileUploadInfo = {
        originalName: req.file.originalname,
        path: req.file.path,
        size: req.file.size,
        mimetype: req.file.mimetype,
      }

      const document = await KnowledgeService.uploadDocument(id, userId, fileInfo)

      return createdResponse(res, document, '文档上传成功')
    })
  ]

  /**
   * 删除文档
   */
  static deleteDocument = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id, documentId } = req.params

    await KnowledgeService.deleteDocument(id, documentId, userId)

    return successResponse(res, null, '文档删除成功')
  })

  /**
   * 搜索知识库
   */
  static searchKnowledge = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params
    const { query, topK, scoreThreshold } = req.body

    if (!query || query.trim().length === 0) {
      return errorResponse(res, '搜索查询不能为空', 400, 'EMPTY_QUERY')
    }

    const result = await KnowledgeService.searchKnowledge(id, userId, query, {
      topK: topK ? parseInt(topK) : undefined,
      scoreThreshold: scoreThreshold ? parseFloat(scoreThreshold) : undefined,
    })

    return successResponse(res, result, '知识库搜索完成')
  })

  /**
   * 获取知识库统计
   */
  static getKnowledgeStats = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params

    const stats = await KnowledgeService.getKnowledgeStats(id, userId)

    return successResponse(res, stats, '获取知识库统计成功')
  })

  /**
   * 获取支持的文档类型
   */
  static getSupportedDocumentTypes = catchAsync(async (req: Request, res: Response) => {
    // 从RAG服务获取支持的文档类型
    const supportedTypes = [
      { type: 'pdf', name: 'PDF文档', maxSize: '50MB' },
      { type: 'docx', name: 'Word文档', maxSize: '50MB' },
      { type: 'txt', name: '文本文件', maxSize: '50MB' },
      { type: 'md', name: 'Markdown文件', maxSize: '50MB' },
      { type: 'rtf', name: 'RTF文档', maxSize: '50MB' },
    ]

    return successResponse(res, supportedTypes, '获取支持的文档类型成功')
  })

  /**
   * 批量删除文档
   */
  static batchDeleteDocuments = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params
    const { documentIds } = req.body

    if (!Array.isArray(documentIds) || documentIds.length === 0) {
      return errorResponse(res, '请选择要删除的文档', 400, 'NO_DOCUMENTS_SELECTED')
    }

    const results = []
    for (const documentId of documentIds) {
      try {
        await KnowledgeService.deleteDocument(id, documentId, userId)
        results.push({ documentId, success: true })
      } catch (error) {
        results.push({
          documentId,
          success: false,
          error: error instanceof Error ? error.message : String(error),
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length

    return successResponse(res, {
      results,
      summary: {
        total: documentIds.length,
        success: successCount,
        failed: failCount,
      },
    }, `批量删除完成: 成功${successCount}个，失败${failCount}个`)
  })

  /**
   * 重新处理文档
   */
  static reprocessDocument = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id, documentId } = req.params

    // TODO: 实现文档重新处理逻辑
    // 1. 获取文档信息
    // 2. 删除现有向量
    // 3. 重新处理文档
    // 4. 更新状态

    return successResponse(res, null, '文档重新处理已开始')
  })

  /**
   * 获取文档处理进度
   */
  static getDocumentProgress = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user._id.toString()
    const { id } = req.params

    const knowledge = await KnowledgeService.getKnowledge(id, userId)

    const progress = {
      total: knowledge.documents.length,
      completed: knowledge.documents.filter(doc => doc.status === 'completed').length,
      processing: knowledge.documents.filter(doc => doc.status === 'processing').length,
      failed: knowledge.documents.filter(doc => doc.status === 'failed').length,
      percentage: knowledge.documents.length > 0 
        ? Math.round((knowledge.documents.filter(doc => doc.status === 'completed').length / knowledge.documents.length) * 100)
        : 0,
    }

    return successResponse(res, progress, '获取处理进度成功')
  })

  /**
   * 获取知识库服务状态
   */
  static getServiceStatus = catchAsync(async (req: Request, res: Response) => {
    const status = enhancedKnowledgeService.getServiceStatus()
    return successResponse(res, status, '获取服务状态成功')
  })

  /**
   * 知识库服务健康检查
   */
  static healthCheck = catchAsync(async (req: Request, res: Response) => {
    const health = await enhancedKnowledgeService.healthCheck()

    // 根据健康状态设置 HTTP 状态码
    const statusCode = health.status === 'healthy' ? 200 :
                      health.status === 'degraded' ? 200 : 503

    res.status(statusCode)
    return successResponse(res, health, '健康检查完成')
  })

  /**
   * 重新初始化知识库服务
   */
  static reinitializeService = catchAsync(async (req: Request, res: Response) => {
    await enhancedKnowledgeService.reinitialize()
    return successResponse(res, null, '服务重新初始化成功')
  })

  /**
   * 重置降级统计
   */
  static resetFallbackStats = catchAsync(async (req: Request, res: Response) => {
    enhancedKnowledgeService.resetFallbackStats()
    return successResponse(res, null, '降级统计已重置')
  })
}
