import { KnowledgeApiConfig, FallbackConfig, AIModelConfig } from '@/types'
import { logger } from '@/utils/logger'

/**
 * 知识库 API 配置管理器
 */
class KnowledgeApiConfigManager {
  private apiConfig: KnowledgeApiConfig
  private fallbackConfig: FallbackConfig

  constructor() {
    this.apiConfig = this.loadApiConfig()
    this.fallbackConfig = this.loadFallbackConfig()
    this.validateConfigs()
  }

  /**
   * 加载 API 配置
   */
  private loadApiConfig(): KnowledgeApiConfig {
    return {
      enabled: process.env.KNOWLEDGE_API_ENABLED === 'true',
      baseUrl: process.env.KNOWLEDGE_API_BASE_URL || '',
      apiKey: process.env.KNOWLEDGE_API_KEY || '',
      apiSecret: process.env.KNOWLEDGE_API_SECRET || '',
      timeout: parseInt(process.env.KNOWLEDGE_API_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.KNOWLEDGE_API_RETRY_ATTEMPTS || '3'),
      retryDelay: parseInt(process.env.KNOWLEDGE_API_RETRY_DELAY || '1000'),
    }
  }

  /**
   * 加载降级配置
   */
  private loadFallbackConfig(): FallbackConfig {
    const strategy = (process.env.KNOWLEDGE_FALLBACK_STRATEGY || 'ai_model') as 'ai_model' | 'cached_response' | 'empty_response'
    
    const aiModelConfig: AIModelConfig = {
      provider: (process.env.FALLBACK_AI_PROVIDER || 'openai') as 'openai' | 'anthropic' | 'local',
      model: process.env.FALLBACK_AI_MODEL || 'gpt-3.5-turbo',
      temperature: parseFloat(process.env.FALLBACK_AI_TEMPERATURE || '0.7'),
      maxTokens: parseInt(process.env.FALLBACK_AI_MAX_TOKENS || '1000'),
      systemPrompt: process.env.FALLBACK_AI_SYSTEM_PROMPT || '',
    }

    return {
      enabled: process.env.KNOWLEDGE_FALLBACK_ENABLED !== 'false', // 默认启用
      strategy,
      aiModelConfig: strategy === 'ai_model' ? aiModelConfig : undefined,
      cacheTimeout: parseInt(process.env.KNOWLEDGE_FALLBACK_CACHE_TIMEOUT || '86400'), // 24小时
    }
  }

  /**
   * 验证配置
   */
  private validateConfigs(): void {
    const errors: string[] = []

    // 验证 API 配置
    if (this.apiConfig.enabled) {
      if (!this.apiConfig.baseUrl) {
        errors.push('KNOWLEDGE_API_BASE_URL 是必需的')
      }
      if (!this.apiConfig.apiKey) {
        errors.push('KNOWLEDGE_API_KEY 是必需的')
      }
      if (this.apiConfig.timeout <= 0) {
        errors.push('KNOWLEDGE_API_TIMEOUT 必须大于 0')
      }
      if (this.apiConfig.retryAttempts < 0) {
        errors.push('KNOWLEDGE_API_RETRY_ATTEMPTS 不能为负数')
      }
    }

    // 验证降级配置
    if (this.fallbackConfig.enabled) {
      if (this.fallbackConfig.strategy === 'ai_model') {
        if (!this.fallbackConfig.aiModelConfig?.provider) {
          errors.push('降级策略为 ai_model 时，FALLBACK_AI_PROVIDER 是必需的')
        }
        if (!this.fallbackConfig.aiModelConfig?.model) {
          errors.push('降级策略为 ai_model 时，FALLBACK_AI_MODEL 是必需的')
        }
      }
    }

    if (errors.length > 0) {
      logger.error('知识库 API 配置验证失败', { errors })
      throw new Error(`配置验证失败: ${errors.join(', ')}`)
    }

    logger.info('知识库 API 配置验证成功', {
      apiEnabled: this.apiConfig.enabled,
      fallbackEnabled: this.fallbackConfig.enabled,
      fallbackStrategy: this.fallbackConfig.strategy,
    })
  }

  /**
   * 获取 API 配置
   */
  getApiConfig(): KnowledgeApiConfig {
    return { ...this.apiConfig }
  }

  /**
   * 获取降级配置
   */
  getFallbackConfig(): FallbackConfig {
    return { ...this.fallbackConfig }
  }

  /**
   * 更新 API 配置
   */
  updateApiConfig(updates: Partial<KnowledgeApiConfig>): void {
    this.apiConfig = { ...this.apiConfig, ...updates }
    this.validateConfigs()
    
    logger.info('知识库 API 配置已更新', { updates })
  }

  /**
   * 更新降级配置
   */
  updateFallbackConfig(updates: Partial<FallbackConfig>): void {
    this.fallbackConfig = { ...this.fallbackConfig, ...updates }
    this.validateConfigs()
    
    logger.info('知识库降级配置已更新', { updates })
  }

  /**
   * 检查 API 是否启用
   */
  isApiEnabled(): boolean {
    return this.apiConfig.enabled
  }

  /**
   * 检查降级是否启用
   */
  isFallbackEnabled(): boolean {
    return this.fallbackConfig.enabled
  }

  /**
   * 获取配置摘要
   */
  getConfigSummary(): {
    api: {
      enabled: boolean
      baseUrl: string
      hasApiKey: boolean
      timeout: number
    }
    fallback: {
      enabled: boolean
      strategy: string
      hasAiConfig: boolean
    }
  } {
    return {
      api: {
        enabled: this.apiConfig.enabled,
        baseUrl: this.apiConfig.baseUrl,
        hasApiKey: !!this.apiConfig.apiKey,
        timeout: this.apiConfig.timeout,
      },
      fallback: {
        enabled: this.fallbackConfig.enabled,
        strategy: this.fallbackConfig.strategy,
        hasAiConfig: !!this.fallbackConfig.aiModelConfig,
      },
    }
  }

  /**
   * 重新加载配置
   */
  reloadConfig(): void {
    this.apiConfig = this.loadApiConfig()
    this.fallbackConfig = this.loadFallbackConfig()
    this.validateConfigs()
    
    logger.info('知识库配置已重新加载')
  }
}

// 创建全局配置管理器实例
export const knowledgeApiConfig = new KnowledgeApiConfigManager()

// 导出类型
export type { KnowledgeApiConfigManager }
