import mongoose from 'mongoose'
import { logger } from '@utils/logger'

/**
 * 连接MongoDB数据库
 */
export async function connectDatabase(): Promise<void> {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/ai-digital-robots'
    
    // MongoDB连接选项
    const options = {
      maxPoolSize: 10, // 连接池最大连接数
      serverSelectionTimeoutMS: 5000, // 服务器选择超时时间
      socketTimeoutMS: 45000, // Socket超时时间
      bufferMaxEntries: 0, // 禁用mongoose缓冲
      bufferCommands: false, // 禁用mongoose缓冲命令
    }

    // 连接数据库
    await mongoose.connect(mongoUri, options)

    // 连接事件监听
    mongoose.connection.on('connected', () => {
      logger.info('MongoDB连接成功')
    })

    mongoose.connection.on('error', (error) => {
      logger.error('MongoDB连接错误:', error)
    })

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB连接断开')
    })

    // 应用终止时关闭数据库连接
    process.on('SIGINT', async () => {
      await mongoose.connection.close()
      logger.info('MongoDB连接已关闭')
      process.exit(0)
    })

  } catch (error) {
    logger.error('数据库连接失败:', error)
    throw error
  }
}

/**
 * 断开数据库连接
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    await mongoose.connection.close()
    logger.info('数据库连接已断开')
  } catch (error) {
    logger.error('断开数据库连接失败:', error)
    throw error
  }
}

/**
 * 检查数据库连接状态
 */
export function isDatabaseConnected(): boolean {
  return mongoose.connection.readyState === 1
}

/**
 * 获取数据库连接状态
 */
export function getDatabaseConnectionState(): string {
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting',
  }
  return states[mongoose.connection.readyState as keyof typeof states] || 'unknown'
}
