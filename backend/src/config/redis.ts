import { createClient, RedisClientType } from 'redis'
import { logger } from '@utils/logger'

let redisClient: RedisClientType

/**
 * 连接Redis
 */
export async function connectRedis(): Promise<void> {
  try {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379'
    
    redisClient = createClient({
      url: redisUrl,
      socket: {
        connectTimeout: 5000,
        lazyConnect: true,
      },
      retryDelayOnFailover: 100,
      retryDelayOnClusterDown: 300,
      maxRetriesPerRequest: 3,
    })

    // 错误处理
    redisClient.on('error', (error) => {
      logger.error('Redis连接错误:', error)
    })

    redisClient.on('connect', () => {
      logger.info('Redis连接成功')
    })

    redisClient.on('ready', () => {
      logger.info('Redis准备就绪')
    })

    redisClient.on('end', () => {
      logger.warn('Redis连接断开')
    })

    // 连接Redis
    await redisClient.connect()

    // 应用终止时关闭Redis连接
    process.on('SIGINT', async () => {
      await redisClient.quit()
      logger.info('Redis连接已关闭')
    })

  } catch (error) {
    logger.error('Redis连接失败:', error)
    throw error
  }
}

/**
 * 获取Redis客户端实例
 */
export function getRedisClient(): RedisClientType {
  if (!redisClient) {
    throw new Error('Redis客户端未初始化')
  }
  return redisClient
}

/**
 * 断开Redis连接
 */
export async function disconnectRedis(): Promise<void> {
  try {
    if (redisClient) {
      await redisClient.quit()
      logger.info('Redis连接已断开')
    }
  } catch (error) {
    logger.error('断开Redis连接失败:', error)
    throw error
  }
}

/**
 * 检查Redis连接状态
 */
export function isRedisConnected(): boolean {
  return redisClient?.isReady || false
}

/**
 * Redis缓存服务类
 */
export class CacheService {
  private client: RedisClientType

  constructor() {
    this.client = getRedisClient()
  }

  /**
   * 获取缓存
   */
  async get(key: string): Promise<string | null> {
    try {
      return await this.client.get(key)
    } catch (error) {
      logger.error('获取缓存失败:', error)
      return null
    }
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: string, ttl?: number): Promise<boolean> {
    try {
      if (ttl) {
        await this.client.setEx(key, ttl, value)
      } else {
        await this.client.set(key, value)
      }
      return true
    } catch (error) {
      logger.error('设置缓存失败:', error)
      return false
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<boolean> {
    try {
      await this.client.del(key)
      return true
    } catch (error) {
      logger.error('删除缓存失败:', error)
      return false
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key)
      return result === 1
    } catch (error) {
      logger.error('检查缓存存在性失败:', error)
      return false
    }
  }

  /**
   * 设置缓存过期时间
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    try {
      await this.client.expire(key, ttl)
      return true
    } catch (error) {
      logger.error('设置缓存过期时间失败:', error)
      return false
    }
  }

  /**
   * 获取缓存剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    try {
      return await this.client.ttl(key)
    } catch (error) {
      logger.error('获取缓存过期时间失败:', error)
      return -1
    }
  }

  /**
   * 批量删除缓存
   */
  async delPattern(pattern: string): Promise<boolean> {
    try {
      const keys = await this.client.keys(pattern)
      if (keys.length > 0) {
        await this.client.del(keys)
      }
      return true
    } catch (error) {
      logger.error('批量删除缓存失败:', error)
      return false
    }
  }

  /**
   * 增加计数器
   */
  async incr(key: string): Promise<number> {
    try {
      return await this.client.incr(key)
    } catch (error) {
      logger.error('增加计数器失败:', error)
      return 0
    }
  }

  /**
   * 减少计数器
   */
  async decr(key: string): Promise<number> {
    try {
      return await this.client.decr(key)
    } catch (error) {
      logger.error('减少计数器失败:', error)
      return 0
    }
  }
}

// 创建缓存服务实例
export const cacheService = new CacheService()
