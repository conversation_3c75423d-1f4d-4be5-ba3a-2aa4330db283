import { Server as SocketIOServer, Socket } from 'socket.io'
import { CryptoUtils } from '@/utils/crypto'
import { User } from '@/models/User'
import { ConversationService } from '@/services/conversationService'
import { logger } from '@/utils/logger'

// 扩展Socket接口
interface AuthenticatedSocket extends Socket {
  userId?: string
  user?: any
}

/**
 * Socket.IO认证中间件
 */
const authenticateSocket = async (socket: AuthenticatedSocket, next: any) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1]

    if (!token) {
      return next(new Error('认证令牌缺失'))
    }

    const decoded = CryptoUtils.verifyToken(token)
    const user = await User.findById(decoded.userId).select('-passwordHash')

    if (!user) {
      return next(new Error('用户不存在'))
    }

    socket.userId = user._id.toString()
    socket.user = user
    next()
  } catch (error) {
    next(new Error('认证失败'))
  }
}

/**
 * 设置Socket.IO服务器
 */
export function setupSocketIO(io: SocketIOServer): void {
  // 添加认证中间件
  io.use(authenticateSocket)

  // 连接事件
  io.on('connection', (socket: AuthenticatedSocket) => {
    logger.info(`用户连接: ${socket.id}, 用户ID: ${socket.userId}`)

    // 加入对话房间
    socket.on('join_conversation', async (data) => {
      try {
        const { conversationId } = data

        // 验证用户是否有权限访问该对话
        const conversation = await ConversationService.getConversation(conversationId, socket.userId!)

        socket.join(`conversation_${conversationId}`)
        socket.emit('joined_conversation', { conversationId })

        logger.info(`用户 ${socket.userId} 加入对话房间: ${conversationId}`)
      } catch (error) {
        socket.emit('error', {
          message: '加入对话失败',
          code: 'JOIN_CONVERSATION_ERROR',
          details: error instanceof Error ? error.message : String(error),
        })
      }
    })

    // 离开对话房间
    socket.on('leave_conversation', (data) => {
      const { conversationId } = data
      socket.leave(`conversation_${conversationId}`)
      socket.emit('left_conversation', { conversationId })
      logger.info(`用户 ${socket.userId} 离开对话房间: ${conversationId}`)
    })

    // 发送消息
    socket.on('send_message', async (data) => {
      try {
        const { conversationId, content, useRAG = false } = data

        if (!content || content.trim().length === 0) {
          socket.emit('error', {
            message: '消息内容不能为空',
            code: 'EMPTY_MESSAGE',
          })
          return
        }

        // 发送打字状态
        socket.to(`conversation_${conversationId}`).emit('typing_start', {
          userId: socket.userId,
        })

        // 处理流式消息
        for await (const chunk of ConversationService.sendStreamMessage(
          conversationId,
          socket.userId!,
          content,
          { useRAG }
        )) {
          if (chunk.type === 'user') {
            // 广播用户消息
            io.to(`conversation_${conversationId}`).emit('message_received', chunk.data)
          } else if (chunk.type === 'assistant') {
            // 发送AI响应流
            socket.emit('message_stream', {
              conversationId,
              content: chunk.data.content,
              delta: chunk.data.delta,
              isComplete: chunk.data.isComplete,
            })
          } else if (chunk.type === 'complete') {
            // 广播完整的AI消息
            io.to(`conversation_${conversationId}`).emit('message_received', chunk.data)

            // 停止打字状态
            socket.to(`conversation_${conversationId}`).emit('typing_stop', {
              userId: socket.userId,
            })
          }
        }

      } catch (error) {
        logger.error('处理消息失败:', error)

        socket.emit('error', {
          message: '消息发送失败',
          code: 'MESSAGE_SEND_ERROR',
          details: error instanceof Error ? error.message : String(error),
        })

        // 停止打字状态
        socket.to(`conversation_${data.conversationId}`).emit('typing_stop', {
          userId: socket.userId,
        })
      }
    })

    // 打字状态
    socket.on('typing_start', (data) => {
      const { conversationId } = data
      socket.to(`conversation_${conversationId}`).emit('typing_start', {
        userId: socket.userId,
      })
    })

    socket.on('typing_stop', (data) => {
      const { conversationId } = data
      socket.to(`conversation_${conversationId}`).emit('typing_stop', {
        userId: socket.userId,
      })
    })

    // 断开连接
    socket.on('disconnect', (reason) => {
      logger.info(`用户断开连接: ${socket.id}, 用户ID: ${socket.userId}, 原因: ${reason}`)
    })

    // 错误处理
    socket.on('error', (error) => {
      logger.error(`Socket错误: ${socket.id}`, error)
    })
  })

  // 服务器错误处理
  io.on('error', (error) => {
    logger.error('Socket.IO服务器错误:', error)
  })

  logger.info('Socket.IO服务器已启动')
}
