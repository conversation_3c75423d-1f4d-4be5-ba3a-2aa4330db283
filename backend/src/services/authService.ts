import { User, UserDocument } from '@/models/User'
import { CryptoUtils } from '@/utils/crypto'
import { AppError } from '@/middleware/errorHandler'
import { cacheService } from '@/config/redis'
import { logger } from '@/utils/logger'
import { LoginRequest, RegisterRequest, JWTPayload } from '@/types'

/**
 * 认证服务类
 */
export class AuthService {
  /**
   * 用户注册
   */
  static async register(userData: RegisterRequest): Promise<{ user: UserDocument; token: string }> {
    const { username, email, password } = userData

    // 检查邮箱是否已存在
    const existingUserByEmail = await User.findOne({ email: email.toLowerCase() })
    if (existingUserByEmail) {
      throw new AppError('邮箱已被注册', 409, 'EMAIL_ALREADY_EXISTS')
    }

    // 检查用户名是否已存在
    const existingUserByUsername = await User.findOne({ username })
    if (existingUserByUsername) {
      throw new AppError('用户名已被使用', 409, 'USERNAME_ALREADY_EXISTS')
    }

    // 加密密码
    const passwordHash = await CryptoUtils.hashPassword(password)

    // 创建用户
    const user = new User({
      username,
      email: email.toLowerCase(),
      passwordHash,
      isEmailVerified: process.env.NODE_ENV === 'development', // 开发环境自动验证
    })

    await user.save()

    // 生成JWT令牌
    const token = CryptoUtils.generateToken({
      userId: user._id.toString(),
      email: user.email,
      role: user.role,
    })

    // 缓存用户信息
    await this.cacheUserInfo(user._id.toString(), user)

    logger.info(`新用户注册: ${user.email}`)

    return { user, token }
  }

  /**
   * 用户登录
   */
  static async login(loginData: LoginRequest): Promise<{ user: UserDocument; token: string }> {
    const { email, password } = loginData

    // 查找用户（包含密码哈希）
    const user = await User.findOne({ email: email.toLowerCase() }).select('+passwordHash')
    if (!user) {
      throw new AppError('邮箱或密码错误', 401, 'INVALID_CREDENTIALS')
    }

    // 验证密码
    const isPasswordValid = await CryptoUtils.comparePassword(password, user.passwordHash)
    if (!isPasswordValid) {
      throw new AppError('邮箱或密码错误', 401, 'INVALID_CREDENTIALS')
    }

    // 检查邮箱是否已验证
    if (!user.isEmailVerified) {
      throw new AppError('请先验证邮箱', 401, 'EMAIL_NOT_VERIFIED')
    }

    // 更新最后登录时间
    user.lastLoginAt = new Date()
    await user.save()

    // 生成JWT令牌
    const token = CryptoUtils.generateToken({
      userId: user._id.toString(),
      email: user.email,
      role: user.role,
    })

    // 缓存用户信息
    await this.cacheUserInfo(user._id.toString(), user)

    // 移除密码哈希
    user.passwordHash = undefined as any

    logger.info(`用户登录: ${user.email}`)

    return { user, token }
  }

  /**
   * 刷新令牌
   */
  static async refreshToken(userId: string): Promise<{ user: UserDocument; token: string }> {
    // 从缓存或数据库获取用户信息
    let user = await this.getCachedUserInfo(userId)
    if (!user) {
      user = await User.findById(userId)
      if (!user) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND')
      }
      await this.cacheUserInfo(userId, user)
    }

    // 检查用户状态
    if (!user.isEmailVerified) {
      throw new AppError('邮箱未验证', 401, 'EMAIL_NOT_VERIFIED')
    }

    // 生成新的JWT令牌
    const token = CryptoUtils.generateToken({
      userId: user._id.toString(),
      email: user.email,
      role: user.role,
    })

    return { user, token }
  }

  /**
   * 用户登出
   */
  static async logout(userId: string): Promise<void> {
    // 清除用户缓存
    await this.clearUserCache(userId)
    
    // TODO: 将令牌加入黑名单（如果需要）
    
    logger.info(`用户登出: ${userId}`)
  }

  /**
   * 修改密码
   */
  static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    // 获取用户信息（包含密码哈希）
    const user = await User.findById(userId).select('+passwordHash')
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND')
    }

    // 验证当前密码
    const isCurrentPasswordValid = await CryptoUtils.comparePassword(currentPassword, user.passwordHash)
    if (!isCurrentPasswordValid) {
      throw new AppError('当前密码错误', 400, 'INVALID_CURRENT_PASSWORD')
    }

    // 加密新密码
    const newPasswordHash = await CryptoUtils.hashPassword(newPassword)

    // 更新密码
    user.passwordHash = newPasswordHash
    await user.save()

    // 清除用户缓存
    await this.clearUserCache(userId)

    logger.info(`用户修改密码: ${user.email}`)
  }

  /**
   * 发送邮箱验证
   */
  static async sendEmailVerification(userId: string): Promise<void> {
    const user = await User.findById(userId)
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND')
    }

    if (user.isEmailVerified) {
      throw new AppError('邮箱已验证', 400, 'EMAIL_ALREADY_VERIFIED')
    }

    // 生成验证令牌
    const verificationToken = CryptoUtils.generateEmailVerificationToken()

    // 缓存验证令牌（有效期1小时）
    await cacheService.set(
      `email_verification:${verificationToken}`,
      userId,
      3600
    )

    // TODO: 发送验证邮件
    logger.info(`发送邮箱验证: ${user.email}`)
  }

  /**
   * 验证邮箱
   */
  static async verifyEmail(token: string): Promise<void> {
    // 从缓存获取用户ID
    const userId = await cacheService.get(`email_verification:${token}`)
    if (!userId) {
      throw new AppError('验证令牌无效或已过期', 400, 'INVALID_VERIFICATION_TOKEN')
    }

    // 更新用户邮箱验证状态
    const user = await User.findByIdAndUpdate(
      userId,
      { isEmailVerified: true },
      { new: true }
    )

    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND')
    }

    // 删除验证令牌
    await cacheService.del(`email_verification:${token}`)

    // 清除用户缓存
    await this.clearUserCache(userId)

    logger.info(`邮箱验证成功: ${user.email}`)
  }

  /**
   * 发送密码重置邮件
   */
  static async sendPasswordReset(email: string): Promise<void> {
    const user = await User.findOne({ email: email.toLowerCase() })
    if (!user) {
      // 为了安全，不透露用户是否存在
      return
    }

    // 生成重置令牌
    const resetToken = CryptoUtils.generatePasswordResetToken()

    // 缓存重置令牌（有效期1小时）
    await cacheService.set(
      `password_reset:${resetToken}`,
      user._id.toString(),
      3600
    )

    // TODO: 发送重置邮件
    logger.info(`发送密码重置邮件: ${user.email}`)
  }

  /**
   * 重置密码
   */
  static async resetPassword(token: string, newPassword: string): Promise<void> {
    // 从缓存获取用户ID
    const userId = await cacheService.get(`password_reset:${token}`)
    if (!userId) {
      throw new AppError('重置令牌无效或已过期', 400, 'INVALID_RESET_TOKEN')
    }

    // 加密新密码
    const passwordHash = await CryptoUtils.hashPassword(newPassword)

    // 更新用户密码
    const user = await User.findByIdAndUpdate(
      userId,
      { passwordHash },
      { new: true }
    )

    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND')
    }

    // 删除重置令牌
    await cacheService.del(`password_reset:${token}`)

    // 清除用户缓存
    await this.clearUserCache(userId)

    logger.info(`密码重置成功: ${user.email}`)
  }

  /**
   * 缓存用户信息
   */
  private static async cacheUserInfo(userId: string, user: UserDocument): Promise<void> {
    try {
      const userInfo = user.toSafeObject()
      await cacheService.set(`user:${userId}`, JSON.stringify(userInfo), 3600) // 1小时
    } catch (error) {
      logger.error('缓存用户信息失败:', error)
    }
  }

  /**
   * 获取缓存的用户信息
   */
  private static async getCachedUserInfo(userId: string): Promise<UserDocument | null> {
    try {
      const cachedUser = await cacheService.get(`user:${userId}`)
      return cachedUser ? JSON.parse(cachedUser) : null
    } catch (error) {
      logger.error('获取缓存用户信息失败:', error)
      return null
    }
  }

  /**
   * 清除用户缓存
   */
  private static async clearUserCache(userId: string): Promise<void> {
    try {
      await cacheService.del(`user:${userId}`)
    } catch (error) {
      logger.error('清除用户缓存失败:', error)
    }
  }
}
