import { logger } from '@/utils/logger'
import { AppError } from '@/middleware/errorHandler'
import { KnowledgeApiClient } from './knowledgeApiClient'
import { KnowledgeFallbackService } from './knowledgeFallbackService'
import { knowledgeApiConfig } from '@/config/knowledgeApi'
import {
  KnowledgeApiRequest,
  KnowledgeApiResponse,
  FallbackResponse,
  KnowledgeApiError,
} from '@/types'

/**
 * 增强的知识库服务
 * 集成 API 调用和降级策略
 */
export class EnhancedKnowledgeService {
  private apiClient: KnowledgeApiClient | null = null
  private fallbackService: KnowledgeFallbackService
  private isInitialized: boolean = false

  constructor() {
    this.fallbackService = new KnowledgeFallbackService(knowledgeApiConfig.getFallbackConfig())
    this.initialize()
  }

  /**
   * 初始化服务
   */
  private initialize(): void {
    try {
      const apiConfig = knowledgeApiConfig.getApiConfig()
      
      if (apiConfig.enabled && this.isApiConfigValid(apiConfig)) {
        this.apiClient = new KnowledgeApiClient(apiConfig)
        logger.info('知识库 API 客户端初始化成功')
      } else {
        logger.warn('知识库 API 未启用或配置无效，将使用降级策略')
      }

      this.isInitialized = true
    } catch (error) {
      logger.error('知识库服务初始化失败', {
        error: error instanceof Error ? error.message : String(error),
      })
      this.isInitialized = false
    }
  }

  /**
   * 搜索知识库
   */
  async searchKnowledge(
    query: string,
    options: {
      knowledgeBaseId?: string
      topK?: number
      scoreThreshold?: number
      filters?: Record<string, any>
      userId?: string
    } = {}
  ): Promise<{
    content: string
    source: 'knowledge_api' | 'ai_model' | 'cache' | 'empty'
    confidence: number
    results?: any[]
    sources?: any[]
    metadata?: Record<string, any>
  }> {
    if (!this.isInitialized) {
      throw new AppError('知识库服务未初始化', 500, 'SERVICE_NOT_INITIALIZED')
    }

    const request: KnowledgeApiRequest = {
      query,
      knowledgeBaseId: options.knowledgeBaseId,
      topK: options.topK || 5,
      scoreThreshold: options.scoreThreshold || 0.7,
      filters: options.filters,
      metadata: {
        userId: options.userId,
        timestamp: new Date().toISOString(),
      },
    }

    try {
      // 尝试使用知识库 API
      if (this.apiClient) {
        const response = await this.searchWithApi(request)
        
        // 缓存成功的响应用于降级
        await this.fallbackService.cacheSuccessfulResponse(request, response)
        
        return {
          content: response.data.context,
          source: 'knowledge_api',
          confidence: this.calculateConfidence(response.data.results),
          results: response.data.results,
          sources: response.data.sources,
          metadata: {
            totalResults: response.data.totalResults,
            processingTime: response.data.processingTime,
            timestamp: response.timestamp,
          },
        }
      } else {
        // API 客户端不可用，直接使用降级策略
        const fallbackResponse = await this.fallbackService.executeFallback(
          request,
          new Error('知识库 API 客户端未初始化')
        )
        
        return fallbackResponse
      }
    } catch (error) {
      logger.error('知识库搜索失败', {
        query,
        error: error instanceof Error ? error.message : String(error),
      })

      // 检查是否应该执行降级
      if (this.fallbackService.shouldFallback(error as Error)) {
        try {
          const fallbackResponse = await this.fallbackService.executeFallback(request, error as Error)
          return fallbackResponse
        } catch (fallbackError) {
          logger.error('降级策略执行失败', {
            fallbackError: fallbackError instanceof Error ? fallbackError.message : String(fallbackError),
          })
          throw new AppError('知识库服务不可用', 503, 'KNOWLEDGE_SERVICE_UNAVAILABLE')
        }
      } else {
        throw error
      }
    }
  }

  /**
   * 使用 API 搜索
   */
  private async searchWithApi(request: KnowledgeApiRequest): Promise<KnowledgeApiResponse> {
    if (!this.apiClient) {
      throw new Error('API 客户端未初始化')
    }

    // 检查 API 健康状态
    const isHealthy = await this.apiClient.checkHealth()
    if (!isHealthy) {
      throw new KnowledgeApiError('知识库 API 服务不健康')
    }

    return await this.apiClient.searchKnowledge(request)
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(results: any[]): number {
    if (!results || results.length === 0) {
      return 0
    }

    // 基于搜索结果的平均分数计算置信度
    const avgScore = results.reduce((sum, result) => sum + (result.score || 0), 0) / results.length
    return Math.min(avgScore, 1.0)
  }

  /**
   * 获取服务状态
   */
  getServiceStatus(): {
    initialized: boolean
    apiEnabled: boolean
    apiHealthy: boolean
    fallbackEnabled: boolean
    fallbackStats: any
    config: any
  } {
    return {
      initialized: this.isInitialized,
      apiEnabled: !!this.apiClient,
      apiHealthy: this.apiClient ? this.apiClient.getStatus().healthy : false,
      fallbackEnabled: knowledgeApiConfig.isFallbackEnabled(),
      fallbackStats: this.fallbackService.getFallbackStats(),
      config: knowledgeApiConfig.getConfigSummary(),
    }
  }

  /**
   * 重新初始化服务
   */
  async reinitialize(): Promise<void> {
    logger.info('重新初始化知识库服务')
    
    // 重新加载配置
    knowledgeApiConfig.reloadConfig()
    
    // 重新初始化
    this.initialize()
    
    // 更新降级服务配置
    this.fallbackService.updateConfig(knowledgeApiConfig.getFallbackConfig())
    
    // 如果有 API 客户端，更新其配置
    if (this.apiClient) {
      this.apiClient.updateConfig(knowledgeApiConfig.getApiConfig())
    }
  }

  /**
   * 验证 API 配置
   */
  private isApiConfigValid(config: any): boolean {
    return !!(
      config.baseUrl &&
      config.apiKey &&
      config.timeout > 0
    )
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    details: {
      api: { available: boolean; healthy: boolean }
      fallback: { enabled: boolean; working: boolean }
      lastError?: string
    }
  }> {
    const details = {
      api: { available: false, healthy: false },
      fallback: { enabled: false, working: false },
    }

    try {
      // 检查 API 状态
      if (this.apiClient) {
        details.api.available = true
        details.api.healthy = await this.apiClient.checkHealth()
      }

      // 检查降级策略
      details.fallback.enabled = knowledgeApiConfig.isFallbackEnabled()
      details.fallback.working = this.fallbackService.isConfigValid()

      // 确定整体状态
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'unhealthy'
      
      if (details.api.available && details.api.healthy) {
        status = 'healthy'
      } else if (details.fallback.enabled && details.fallback.working) {
        status = 'degraded'
      }

      return { status, details }
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          ...details,
          lastError: error instanceof Error ? error.message : String(error),
        },
      }
    }
  }

  /**
   * 重置降级统计
   */
  resetFallbackStats(): void {
    this.fallbackService.resetFallbackStats()
  }
}

// 创建全局服务实例
export const enhancedKnowledgeService = new EnhancedKnowledgeService()

// 为了向后兼容，创建一个函数来包装搜索功能
export async function searchKnowledgeWithApi(
  query: string,
  knowledgeId?: string,
  options: {
    topK?: number
    scoreThreshold?: number
    userId?: string
  } = {}
): Promise<{
  content: string
  source: string
  confidence: number
  results?: any[]
  sources?: any[]
}> {
  return enhancedKnowledgeService.searchKnowledge(query, {
    knowledgeBaseId: knowledgeId,
    ...options,
  })
}
