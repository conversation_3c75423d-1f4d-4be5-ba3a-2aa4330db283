import { logger } from '@/utils/logger'
import { cacheService } from '@/config/redis'
import { aiService } from './ai/aiService'
import {
  FallbackConfig,
  FallbackResponse,
  AIModelConfig,
  AIMessage,
  KnowledgeApiRequest,
  KnowledgeApiResponse,
} from '@/types'

/**
 * 知识库降级策略服务
 * 当知识库 API 不可用时提供备用方案
 */
export class KnowledgeFallbackService {
  private config: FallbackConfig
  private fallbackCount: number = 0
  private lastFallbackTime: number = 0

  constructor(config: FallbackConfig) {
    this.config = config
  }

  /**
   * 执行降级策略
   */
  async executeFallback(
    request: KnowledgeApiRequest,
    originalError: Error
  ): Promise<FallbackResponse> {
    this.fallbackCount++
    this.lastFallbackTime = Date.now()

    logger.warn('执行知识库降级策略', {
      strategy: this.config.strategy,
      query: request.query,
      error: originalError.message,
      fallbackCount: this.fallbackCount,
    })

    try {
      switch (this.config.strategy) {
        case 'ai_model':
          return await this.fallbackToAiModel(request)
        case 'cached_response':
          return await this.fallbackToCachedResponse(request)
        case 'empty_response':
          return this.fallbackToEmptyResponse(request)
        default:
          throw new Error(`未知的降级策略: ${this.config.strategy}`)
      }
    } catch (error) {
      logger.error('降级策略执行失败', {
        strategy: this.config.strategy,
        error: error instanceof Error ? error.message : String(error),
      })
      
      // 如果降级策略失败，返回空响应
      return this.fallbackToEmptyResponse(request)
    }
  }

  /**
   * 降级到 AI 模型
   */
  private async fallbackToAiModel(request: KnowledgeApiRequest): Promise<FallbackResponse> {
    if (!this.config.aiModelConfig) {
      throw new Error('AI 模型配置未设置')
    }

    // 构建提示词
    const systemPrompt = this.buildSystemPrompt(request)
    const messages: AIMessage[] = [
      {
        role: 'system',
        content: systemPrompt,
      },
      {
        role: 'user',
        content: request.query,
      },
    ]

    try {
      const response = await aiService.generateResponse(
        messages,
        this.config.aiModelConfig,
        {
          stream: false,
        }
      )

      logger.info('AI 模型降级响应生成成功', {
        query: request.query,
        model: this.config.aiModelConfig.model,
        tokens: response.usage.totalTokens,
      })

      return {
        content: response.content,
        source: 'ai_model',
        confidence: 0.7, // AI 模型降级的置信度设为 0.7
        metadata: {
          model: this.config.aiModelConfig.model,
          tokens: response.usage.totalTokens,
          fallbackReason: 'knowledge_api_unavailable',
        },
      }
    } catch (error) {
      logger.error('AI 模型降级失败', {
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * 降级到缓存响应
   */
  private async fallbackToCachedResponse(request: KnowledgeApiRequest): Promise<FallbackResponse> {
    const cacheKey = this.generateFallbackCacheKey(request)
    
    try {
      const cached = await cacheService.get(cacheKey)
      if (cached) {
        const cachedResponse = JSON.parse(cached)
        
        logger.info('使用缓存响应作为降级方案', {
          query: request.query,
          cacheKey,
        })

        return {
          content: cachedResponse.content,
          source: 'cache',
          confidence: 0.6, // 缓存响应的置信度设为 0.6
          metadata: {
            cacheKey,
            cachedAt: cachedResponse.timestamp,
            fallbackReason: 'knowledge_api_unavailable',
          },
        }
      } else {
        throw new Error('未找到相关的缓存响应')
      }
    } catch (error) {
      logger.error('缓存降级失败', {
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * 降级到空响应
   */
  private fallbackToEmptyResponse(request: KnowledgeApiRequest): FallbackResponse {
    logger.info('使用空响应作为降级方案', {
      query: request.query,
    })

    return {
      content: '抱歉，知识库服务暂时不可用，无法为您提供相关信息。请稍后再试或联系管理员。',
      source: 'empty',
      confidence: 0.1, // 空响应的置信度最低
      metadata: {
        fallbackReason: 'knowledge_api_unavailable',
        timestamp: new Date().toISOString(),
      },
    }
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(request: KnowledgeApiRequest): string {
    return `你是一个智能助手。由于知识库服务暂时不可用，请基于你的通用知识回答用户的问题。

请注意：
1. 如果问题涉及特定的文档或专业知识，请说明你无法访问相关的知识库
2. 尽量提供有用的通用信息
3. 保持回答的准确性和有用性
4. 如果不确定，请诚实说明

用户问题：${request.query}`
  }

  /**
   * 生成降级缓存键
   */
  private generateFallbackCacheKey(request: KnowledgeApiRequest): string {
    const key = JSON.stringify({
      query: request.query,
      knowledgeBaseId: request.knowledgeBaseId,
    })
    
    const crypto = require('crypto')
    return `fallback_cache:${crypto.createHash('md5').update(key).digest('hex')}`
  }

  /**
   * 缓存成功的响应用于降级
   */
  async cacheSuccessfulResponse(
    request: KnowledgeApiRequest,
    response: KnowledgeApiResponse
  ): Promise<void> {
    if (!this.config.enabled || this.config.strategy !== 'cached_response') {
      return
    }

    try {
      const cacheKey = this.generateFallbackCacheKey(request)
      const cacheData = {
        content: response.data.context,
        timestamp: response.timestamp,
        sources: response.data.sources,
      }

      const ttl = this.config.cacheTimeout || 86400 // 默认缓存24小时
      await cacheService.set(cacheKey, JSON.stringify(cacheData), ttl)

      logger.debug('缓存成功响应用于降级', {
        cacheKey,
        ttl,
      })
    } catch (error) {
      logger.error('缓存降级响应失败', {
        error: error instanceof Error ? error.message : String(error),
      })
    }
  }

  /**
   * 获取降级统计信息
   */
  getFallbackStats(): {
    count: number
    lastTime: number
    strategy: string
    enabled: boolean
  } {
    return {
      count: this.fallbackCount,
      lastTime: this.lastFallbackTime,
      strategy: this.config.strategy,
      enabled: this.config.enabled,
    }
  }

  /**
   * 重置降级统计
   */
  resetFallbackStats(): void {
    this.fallbackCount = 0
    this.lastFallbackTime = 0
    
    logger.info('降级统计已重置')
  }

  /**
   * 更新降级配置
   */
  updateConfig(newConfig: Partial<FallbackConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    logger.info('降级策略配置已更新', {
      config: this.config,
    })
  }

  /**
   * 检查是否应该执行降级
   */
  shouldFallback(error: Error): boolean {
    if (!this.config.enabled) {
      return false
    }

    // 检查错误类型，决定是否需要降级
    const errorMessage = error.message.toLowerCase()
    
    // 网络错误或服务不可用时执行降级
    if (
      errorMessage.includes('network') ||
      errorMessage.includes('timeout') ||
      errorMessage.includes('unavailable') ||
      errorMessage.includes('connection') ||
      error.name === 'KnowledgeApiError'
    ) {
      return true
    }

    return false
  }

  /**
   * 检查配置是否有效
   */
  isConfigValid(): boolean {
    if (!this.config.enabled) {
      return true // 如果禁用，配置总是有效的
    }

    switch (this.config.strategy) {
      case 'ai_model':
        return !!(this.config.aiModelConfig?.provider && this.config.aiModelConfig?.model)
      case 'cached_response':
        return true // 缓存策略不需要额外配置
      case 'empty_response':
        return true // 空响应策略不需要额外配置
      default:
        return false
    }
  }
}
