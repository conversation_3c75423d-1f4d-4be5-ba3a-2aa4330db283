// AI服务相关类型定义

export interface AIMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
  name?: string
}

export interface AIModelConfig {
  provider: 'openai' | 'anthropic' | 'local'
  model: string
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  stop?: string[]
}

export interface AIResponse {
  content: string
  usage: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  model: string
  finishReason: 'stop' | 'length' | 'content_filter' | 'function_call'
  metadata?: {
    duration: number
    provider: string
    requestId?: string
  }
}

export interface AIStreamResponse {
  content: string
  delta: string
  isComplete: boolean
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  metadata?: {
    duration: number
    provider: string
    requestId?: string
  }
}

export interface AIProvider {
  name: string
  generateResponse(
    messages: AIMessage[],
    config: AIModelConfig,
    options?: AIRequestOptions
  ): Promise<AIResponse>
  
  generateStreamResponse(
    messages: AIMessage[],
    config: AIModelConfig,
    options?: AIRequestOptions
  ): AsyncGenerator<AIStreamResponse, void, unknown>
  
  isAvailable(): boolean
  getSupportedModels(): string[]
  validateConfig(config: AIModelConfig): boolean
}

export interface AIRequestOptions {
  userId?: string
  conversationId?: string
  characterId?: string
  timeout?: number
  retries?: number
  stream?: boolean
  ragContext?: string[]
}

export interface EmbeddingRequest {
  text: string
  model?: string
}

export interface EmbeddingResponse {
  embedding: number[]
  usage: {
    promptTokens: number
    totalTokens: number
  }
  model: string
}

export interface EmbeddingProvider {
  name: string
  generateEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse>
  generateEmbeddings(texts: string[], model?: string): Promise<EmbeddingResponse[]>
  isAvailable(): boolean
  getSupportedModels(): string[]
}

// 错误类型
export class AIProviderError extends Error {
  constructor(
    message: string,
    public provider: string,
    public code?: string,
    public statusCode?: number
  ) {
    super(message)
    this.name = 'AIProviderError'
  }
}

export class AIRateLimitError extends AIProviderError {
  constructor(provider: string, retryAfter?: number) {
    super(`AI服务限流: ${provider}`, provider, 'RATE_LIMIT', 429)
    this.retryAfter = retryAfter
  }
  
  retryAfter?: number
}

export class AIQuotaExceededError extends AIProviderError {
  constructor(provider: string) {
    super(`AI服务配额已用完: ${provider}`, provider, 'QUOTA_EXCEEDED', 402)
  }
}

export class AIModelNotFoundError extends AIProviderError {
  constructor(provider: string, model: string) {
    super(`AI模型不存在: ${model} (${provider})`, provider, 'MODEL_NOT_FOUND', 404)
  }
}

export class AIContentFilterError extends AIProviderError {
  constructor(provider: string) {
    super(`内容被过滤: ${provider}`, provider, 'CONTENT_FILTERED', 400)
  }
}
