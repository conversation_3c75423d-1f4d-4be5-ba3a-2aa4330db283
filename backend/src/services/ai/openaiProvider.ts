import OpenAI from 'openai'
import { logger } from '@/utils/logger'
import {
  AIProvider,
  AIMessage,
  AIModelConfig,
  AIResponse,
  AIStreamResponse,
  AIRequestOptions,
  AIProviderError,
  AIRateLimitError,
  AIQuotaExceededError,
  AIModelNotFoundError,
  AIContentFilterError,
  EmbeddingProvider,
  EmbeddingRequest,
  EmbeddingResponse,
} from './types'

/**
 * OpenAI服务提供商
 */
export class OpenAIProvider implements AIProvider, EmbeddingProvider {
  public readonly name = 'openai'
  private client: OpenAI
  private isInitialized = false

  constructor() {
    this.initialize()
  }

  private initialize(): void {
    try {
      const apiKey = process.env.OPENAI_API_KEY
      if (!apiKey) {
        logger.warn('OpenAI API密钥未配置')
        return
      }

      this.client = new OpenAI({
        apiKey,
        organization: process.env.OPENAI_ORG_ID,
        timeout: 60000, // 60秒超时
        maxRetries: 3,
      })

      this.isInitialized = true
      logger.info('OpenAI服务初始化成功')
    } catch (error) {
      logger.error('OpenAI服务初始化失败:', error)
    }
  }

  isAvailable(): boolean {
    return this.isInitialized && !!process.env.OPENAI_API_KEY
  }

  getSupportedModels(): string[] {
    return [
      'gpt-4',
      'gpt-4-turbo-preview',
      'gpt-4-1106-preview',
      'gpt-3.5-turbo',
      'gpt-3.5-turbo-16k',
    ]
  }

  validateConfig(config: AIModelConfig): boolean {
    if (config.provider !== 'openai') return false
    if (!this.getSupportedModels().includes(config.model)) return false
    if (config.temperature && (config.temperature < 0 || config.temperature > 2)) return false
    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 4000)) return false
    return true
  }

  async generateResponse(
    messages: AIMessage[],
    config: AIModelConfig,
    options: AIRequestOptions = {}
  ): Promise<AIResponse> {
    if (!this.isAvailable()) {
      throw new AIProviderError('OpenAI服务不可用', this.name, 'SERVICE_UNAVAILABLE')
    }

    if (!this.validateConfig(config)) {
      throw new AIProviderError('无效的模型配置', this.name, 'INVALID_CONFIG')
    }

    const startTime = Date.now()

    try {
      const response = await this.client.chat.completions.create({
        model: config.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          name: msg.name,
        })),
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 2000,
        top_p: config.topP || 1,
        frequency_penalty: config.frequencyPenalty || 0,
        presence_penalty: config.presencePenalty || 0,
        stop: config.stop,
        user: options.userId,
      })

      const choice = response.choices[0]
      if (!choice || !choice.message) {
        throw new AIProviderError('无效的响应格式', this.name, 'INVALID_RESPONSE')
      }

      const duration = Date.now() - startTime

      return {
        content: choice.message.content || '',
        usage: {
          promptTokens: response.usage?.prompt_tokens || 0,
          completionTokens: response.usage?.completion_tokens || 0,
          totalTokens: response.usage?.total_tokens || 0,
        },
        model: response.model,
        finishReason: this.mapFinishReason(choice.finish_reason),
        metadata: {
          duration,
          provider: this.name,
          requestId: response.id,
        },
      }
    } catch (error: any) {
      this.handleError(error)
      throw error
    }
  }

  async *generateStreamResponse(
    messages: AIMessage[],
    config: AIModelConfig,
    options: AIRequestOptions = {}
  ): AsyncGenerator<AIStreamResponse, void, unknown> {
    if (!this.isAvailable()) {
      throw new AIProviderError('OpenAI服务不可用', this.name, 'SERVICE_UNAVAILABLE')
    }

    if (!this.validateConfig(config)) {
      throw new AIProviderError('无效的模型配置', this.name, 'INVALID_CONFIG')
    }

    const startTime = Date.now()
    let content = ''
    let totalTokens = 0

    try {
      const stream = await this.client.chat.completions.create({
        model: config.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          name: msg.name,
        })),
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 2000,
        top_p: config.topP || 1,
        frequency_penalty: config.frequencyPenalty || 0,
        presence_penalty: config.presencePenalty || 0,
        stop: config.stop,
        user: options.userId,
        stream: true,
      })

      for await (const chunk of stream) {
        const choice = chunk.choices[0]
        if (!choice) continue

        const delta = choice.delta?.content || ''
        content += delta

        const isComplete = choice.finish_reason !== null

        yield {
          content,
          delta,
          isComplete,
          usage: isComplete ? {
            promptTokens: 0, // OpenAI流式响应不提供token统计
            completionTokens: 0,
            totalTokens: 0,
          } : undefined,
          metadata: {
            duration: Date.now() - startTime,
            provider: this.name,
            requestId: chunk.id,
          },
        }

        if (isComplete) break
      }
    } catch (error: any) {
      this.handleError(error)
      throw error
    }
  }

  async generateEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    if (!this.isAvailable()) {
      throw new AIProviderError('OpenAI服务不可用', this.name, 'SERVICE_UNAVAILABLE')
    }

    try {
      const response = await this.client.embeddings.create({
        model: request.model || 'text-embedding-ada-002',
        input: request.text,
      })

      const embedding = response.data[0]
      if (!embedding) {
        throw new AIProviderError('无效的嵌入响应', this.name, 'INVALID_RESPONSE')
      }

      return {
        embedding: embedding.embedding,
        usage: {
          promptTokens: response.usage.prompt_tokens,
          totalTokens: response.usage.total_tokens,
        },
        model: response.model,
      }
    } catch (error: any) {
      this.handleError(error)
      throw error
    }
  }

  async generateEmbeddings(texts: string[], model?: string): Promise<EmbeddingResponse[]> {
    if (!this.isAvailable()) {
      throw new AIProviderError('OpenAI服务不可用', this.name, 'SERVICE_UNAVAILABLE')
    }

    try {
      const response = await this.client.embeddings.create({
        model: model || 'text-embedding-ada-002',
        input: texts,
      })

      return response.data.map((embedding, index) => ({
        embedding: embedding.embedding,
        usage: {
          promptTokens: Math.floor(response.usage.prompt_tokens / texts.length),
          totalTokens: Math.floor(response.usage.total_tokens / texts.length),
        },
        model: response.model,
      }))
    } catch (error: any) {
      this.handleError(error)
      throw error
    }
  }

  private mapFinishReason(reason: string | null): 'stop' | 'length' | 'content_filter' | 'function_call' {
    switch (reason) {
      case 'stop':
        return 'stop'
      case 'length':
        return 'length'
      case 'content_filter':
        return 'content_filter'
      case 'function_call':
        return 'function_call'
      default:
        return 'stop'
    }
  }

  private handleError(error: any): void {
    const errorMessage = error.message || '未知错误'
    const errorCode = error.code || 'UNKNOWN_ERROR'

    logger.error(`OpenAI API错误: ${errorMessage}`, {
      code: errorCode,
      status: error.status,
      type: error.type,
    })

    // 根据错误类型抛出相应的错误
    if (error.status === 429) {
      const retryAfter = error.headers?.['retry-after']
      throw new AIRateLimitError(this.name, retryAfter ? parseInt(retryAfter) : undefined)
    }

    if (error.status === 402 || error.code === 'insufficient_quota') {
      throw new AIQuotaExceededError(this.name)
    }

    if (error.status === 404 || error.code === 'model_not_found') {
      throw new AIModelNotFoundError(this.name, error.model || 'unknown')
    }

    if (error.code === 'content_filter') {
      throw new AIContentFilterError(this.name)
    }

    throw new AIProviderError(errorMessage, this.name, errorCode, error.status)
  }
}
