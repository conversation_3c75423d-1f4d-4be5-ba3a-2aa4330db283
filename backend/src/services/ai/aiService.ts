import { logger } from '@/utils/logger'
import { cacheService } from '@/config/redis'
import { OpenAIProvider } from './openaiProvider'
import { AnthropicProvider } from './anthropicProvider'
import {
  AIProvider,
  AIMessage,
  AIModelConfig,
  AIResponse,
  AIStreamResponse,
  AIRequestOptions,
  AIProviderError,
  EmbeddingProvider,
  EmbeddingRequest,
  EmbeddingResponse,
} from './types'

/**
 * AI服务管理器
 */
export class AIService {
  private providers: Map<string, AIProvider> = new Map()
  private embeddingProviders: Map<string, EmbeddingProvider> = new Map()
  private defaultProvider: string = 'openai'
  private defaultEmbeddingProvider: string = 'openai'

  constructor() {
    this.initializeProviders()
  }

  private initializeProviders(): void {
    // 初始化AI提供商
    const openaiProvider = new OpenAIProvider()
    const anthropicProvider = new AnthropicProvider()

    this.providers.set('openai', openaiProvider)
    this.providers.set('anthropic', anthropicProvider)

    // 初始化嵌入提供商
    this.embeddingProviders.set('openai', openaiProvider)

    // 设置默认提供商
    if (openaiProvider.isAvailable()) {
      this.defaultProvider = 'openai'
      this.defaultEmbeddingProvider = 'openai'
    } else if (anthropicProvider.isAvailable()) {
      this.defaultProvider = 'anthropic'
    }

    logger.info('AI服务提供商初始化完成', {
      providers: Array.from(this.providers.keys()),
      embeddingProviders: Array.from(this.embeddingProviders.keys()),
      defaultProvider: this.defaultProvider,
    })
  }

  /**
   * 生成AI响应
   */
  async generateResponse(
    messages: AIMessage[],
    config: AIModelConfig,
    options: AIRequestOptions = {}
  ): Promise<AIResponse> {
    const provider = this.getProvider(config.provider)
    
    // 添加缓存键
    const cacheKey = this.generateCacheKey(messages, config, options)
    
    // 尝试从缓存获取响应
    if (!options.stream) {
      const cachedResponse = await this.getCachedResponse(cacheKey)
      if (cachedResponse) {
        logger.info('返回缓存的AI响应', { cacheKey })
        return cachedResponse
      }
    }

    try {
      const response = await provider.generateResponse(messages, config, options)
      
      // 缓存响应（非流式）
      if (!options.stream && response.finishReason === 'stop') {
        await this.cacheResponse(cacheKey, response)
      }

      // 记录使用统计
      await this.recordUsage(options.userId, response.usage.totalTokens, config.provider)

      return response
    } catch (error) {
      logger.error('AI响应生成失败', {
        provider: config.provider,
        model: config.model,
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * 生成流式AI响应
   */
  async *generateStreamResponse(
    messages: AIMessage[],
    config: AIModelConfig,
    options: AIRequestOptions = {}
  ): AsyncGenerator<AIStreamResponse, void, unknown> {
    const provider = this.getProvider(config.provider)

    try {
      let totalTokens = 0
      
      for await (const chunk of provider.generateStreamResponse(messages, config, options)) {
        if (chunk.usage) {
          totalTokens = chunk.usage.totalTokens
        }
        yield chunk
      }

      // 记录使用统计
      if (totalTokens > 0) {
        await this.recordUsage(options.userId, totalTokens, config.provider)
      }
    } catch (error) {
      logger.error('AI流式响应生成失败', {
        provider: config.provider,
        model: config.model,
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * 生成文本嵌入
   */
  async generateEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const provider = this.getEmbeddingProvider(this.defaultEmbeddingProvider)
    
    const cacheKey = `embedding:${this.hashText(request.text)}:${request.model || 'default'}`
    
    // 尝试从缓存获取嵌入
    const cachedEmbedding = await this.getCachedEmbedding(cacheKey)
    if (cachedEmbedding) {
      logger.info('返回缓存的文本嵌入', { cacheKey })
      return cachedEmbedding
    }

    try {
      const response = await provider.generateEmbedding(request)
      
      // 缓存嵌入结果
      await this.cacheEmbedding(cacheKey, response)

      return response
    } catch (error) {
      logger.error('文本嵌入生成失败', {
        provider: this.defaultEmbeddingProvider,
        model: request.model,
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * 批量生成文本嵌入
   */
  async generateEmbeddings(texts: string[], model?: string): Promise<EmbeddingResponse[]> {
    const provider = this.getEmbeddingProvider(this.defaultEmbeddingProvider)

    try {
      return await provider.generateEmbeddings(texts, model)
    } catch (error) {
      logger.error('批量文本嵌入生成失败', {
        provider: this.defaultEmbeddingProvider,
        model,
        textsCount: texts.length,
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * 获取可用的AI提供商
   */
  getAvailableProviders(): string[] {
    return Array.from(this.providers.entries())
      .filter(([_, provider]) => provider.isAvailable())
      .map(([name, _]) => name)
  }

  /**
   * 获取支持的模型列表
   */
  getSupportedModels(providerName?: string): Record<string, string[]> {
    const result: Record<string, string[]> = {}
    
    if (providerName) {
      const provider = this.providers.get(providerName)
      if (provider && provider.isAvailable()) {
        result[providerName] = provider.getSupportedModels()
      }
    } else {
      for (const [name, provider] of this.providers.entries()) {
        if (provider.isAvailable()) {
          result[name] = provider.getSupportedModels()
        }
      }
    }
    
    return result
  }

  /**
   * 验证模型配置
   */
  validateConfig(config: AIModelConfig): boolean {
    const provider = this.providers.get(config.provider)
    return provider ? provider.validateConfig(config) : false
  }

  private getProvider(providerName: string): AIProvider {
    const provider = this.providers.get(providerName)
    if (!provider) {
      throw new AIProviderError(`未知的AI提供商: ${providerName}`, providerName, 'UNKNOWN_PROVIDER')
    }
    
    if (!provider.isAvailable()) {
      throw new AIProviderError(`AI提供商不可用: ${providerName}`, providerName, 'PROVIDER_UNAVAILABLE')
    }
    
    return provider
  }

  private getEmbeddingProvider(providerName: string): EmbeddingProvider {
    const provider = this.embeddingProviders.get(providerName)
    if (!provider) {
      throw new AIProviderError(`未知的嵌入提供商: ${providerName}`, providerName, 'UNKNOWN_PROVIDER')
    }
    
    if (!provider.isAvailable()) {
      throw new AIProviderError(`嵌入提供商不可用: ${providerName}`, providerName, 'PROVIDER_UNAVAILABLE')
    }
    
    return provider
  }

  private generateCacheKey(
    messages: AIMessage[],
    config: AIModelConfig,
    options: AIRequestOptions
  ): string {
    const content = JSON.stringify({
      messages: messages.map(m => ({ role: m.role, content: m.content })),
      config: {
        provider: config.provider,
        model: config.model,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
      },
      ragContext: options.ragContext,
    })
    
    return `ai_response:${this.hashText(content)}`
  }

  private hashText(text: string): string {
    const crypto = require('crypto')
    return crypto.createHash('md5').update(text).digest('hex')
  }

  private async getCachedResponse(cacheKey: string): Promise<AIResponse | null> {
    try {
      const cached = await cacheService.get(cacheKey)
      return cached ? JSON.parse(cached) : null
    } catch (error) {
      logger.error('获取缓存响应失败:', error)
      return null
    }
  }

  private async cacheResponse(cacheKey: string, response: AIResponse): Promise<void> {
    try {
      // 缓存1小时
      await cacheService.set(cacheKey, JSON.stringify(response), 3600)
    } catch (error) {
      logger.error('缓存响应失败:', error)
    }
  }

  private async getCachedEmbedding(cacheKey: string): Promise<EmbeddingResponse | null> {
    try {
      const cached = await cacheService.get(cacheKey)
      return cached ? JSON.parse(cached) : null
    } catch (error) {
      logger.error('获取缓存嵌入失败:', error)
      return null
    }
  }

  private async cacheEmbedding(cacheKey: string, embedding: EmbeddingResponse): Promise<void> {
    try {
      // 缓存24小时
      await cacheService.set(cacheKey, JSON.stringify(embedding), 86400)
    } catch (error) {
      logger.error('缓存嵌入失败:', error)
    }
  }

  private async recordUsage(userId?: string, tokens?: number, provider?: string): Promise<void> {
    if (!userId || !tokens) return

    try {
      // TODO: 更新用户token使用量
      logger.info('记录AI使用量', { userId, tokens, provider })
    } catch (error) {
      logger.error('记录使用量失败:', error)
    }
  }
}

// 创建全局AI服务实例
export const aiService = new AIService()
