import Anthropic from '@anthropic-ai/sdk'
import { logger } from '@/utils/logger'
import {
  AIProvider,
  AIMessage,
  AIModelConfig,
  AIResponse,
  AIStreamResponse,
  AIRequestOptions,
  AIProviderError,
  AIRateLimitError,
  AIQuotaExceededError,
  AIModelNotFoundError,
  AIContentFilterError,
} from './types'

/**
 * Anthropic Claude服务提供商
 */
export class AnthropicProvider implements AIProvider {
  public readonly name = 'anthropic'
  private client: Anthropic
  private isInitialized = false

  constructor() {
    this.initialize()
  }

  private initialize(): void {
    try {
      const apiKey = process.env.ANTHROPIC_API_KEY
      if (!apiKey) {
        logger.warn('Anthropic API密钥未配置')
        return
      }

      this.client = new Anthropic({
        apiKey,
        timeout: 60000, // 60秒超时
        maxRetries: 3,
      })

      this.isInitialized = true
      logger.info('Anthropic服务初始化成功')
    } catch (error) {
      logger.error('Anthropic服务初始化失败:', error)
    }
  }

  isAvailable(): boolean {
    return this.isInitialized && !!process.env.ANTHROPIC_API_KEY
  }

  getSupportedModels(): string[] {
    return [
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307',
      'claude-2.1',
      'claude-2.0',
      'claude-instant-1.2',
    ]
  }

  validateConfig(config: AIModelConfig): boolean {
    if (config.provider !== 'anthropic') return false
    if (!this.getSupportedModels().includes(config.model)) return false
    if (config.temperature && (config.temperature < 0 || config.temperature > 1)) return false
    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 4000)) return false
    return true
  }

  async generateResponse(
    messages: AIMessage[],
    config: AIModelConfig,
    options: AIRequestOptions = {}
  ): Promise<AIResponse> {
    if (!this.isAvailable()) {
      throw new AIProviderError('Anthropic服务不可用', this.name, 'SERVICE_UNAVAILABLE')
    }

    if (!this.validateConfig(config)) {
      throw new AIProviderError('无效的模型配置', this.name, 'INVALID_CONFIG')
    }

    const startTime = Date.now()

    try {
      // 转换消息格式
      const { system, messages: anthropicMessages } = this.convertMessages(messages)

      const response = await this.client.messages.create({
        model: config.model,
        max_tokens: config.maxTokens || 2000,
        temperature: config.temperature || 0.7,
        system,
        messages: anthropicMessages,
        top_p: config.topP || 1,
        stop_sequences: config.stop,
      })

      if (!response.content || response.content.length === 0) {
        throw new AIProviderError('无效的响应格式', this.name, 'INVALID_RESPONSE')
      }

      const content = response.content
        .filter(block => block.type === 'text')
        .map(block => (block as any).text)
        .join('')

      const duration = Date.now() - startTime

      return {
        content,
        usage: {
          promptTokens: response.usage.input_tokens,
          completionTokens: response.usage.output_tokens,
          totalTokens: response.usage.input_tokens + response.usage.output_tokens,
        },
        model: response.model,
        finishReason: this.mapStopReason(response.stop_reason),
        metadata: {
          duration,
          provider: this.name,
          requestId: response.id,
        },
      }
    } catch (error: any) {
      this.handleError(error)
      throw error
    }
  }

  async *generateStreamResponse(
    messages: AIMessage[],
    config: AIModelConfig,
    options: AIRequestOptions = {}
  ): AsyncGenerator<AIStreamResponse, void, unknown> {
    if (!this.isAvailable()) {
      throw new AIProviderError('Anthropic服务不可用', this.name, 'SERVICE_UNAVAILABLE')
    }

    if (!this.validateConfig(config)) {
      throw new AIProviderError('无效的模型配置', this.name, 'INVALID_CONFIG')
    }

    const startTime = Date.now()
    let content = ''

    try {
      // 转换消息格式
      const { system, messages: anthropicMessages } = this.convertMessages(messages)

      const stream = await this.client.messages.create({
        model: config.model,
        max_tokens: config.maxTokens || 2000,
        temperature: config.temperature || 0.7,
        system,
        messages: anthropicMessages,
        top_p: config.topP || 1,
        stop_sequences: config.stop,
        stream: true,
      })

      for await (const event of stream) {
        if (event.type === 'content_block_delta' && event.delta.type === 'text_delta') {
          const delta = event.delta.text
          content += delta

          yield {
            content,
            delta,
            isComplete: false,
            metadata: {
              duration: Date.now() - startTime,
              provider: this.name,
            },
          }
        } else if (event.type === 'message_stop') {
          yield {
            content,
            delta: '',
            isComplete: true,
            usage: {
              promptTokens: 0, // Anthropic流式响应不提供详细token统计
              completionTokens: 0,
              totalTokens: 0,
            },
            metadata: {
              duration: Date.now() - startTime,
              provider: this.name,
            },
          }
          break
        }
      }
    } catch (error: any) {
      this.handleError(error)
      throw error
    }
  }

  private convertMessages(messages: AIMessage[]): {
    system?: string
    messages: Array<{ role: 'user' | 'assistant'; content: string }>
  } {
    let system: string | undefined
    const anthropicMessages: Array<{ role: 'user' | 'assistant'; content: string }> = []

    for (const message of messages) {
      if (message.role === 'system') {
        system = message.content
      } else if (message.role === 'user' || message.role === 'assistant') {
        anthropicMessages.push({
          role: message.role,
          content: message.content,
        })
      }
    }

    return { system, messages: anthropicMessages }
  }

  private mapStopReason(reason: string | null): 'stop' | 'length' | 'content_filter' | 'function_call' {
    switch (reason) {
      case 'end_turn':
        return 'stop'
      case 'max_tokens':
        return 'length'
      case 'stop_sequence':
        return 'stop'
      default:
        return 'stop'
    }
  }

  private handleError(error: any): void {
    const errorMessage = error.message || '未知错误'
    const errorCode = error.code || 'UNKNOWN_ERROR'

    logger.error(`Anthropic API错误: ${errorMessage}`, {
      code: errorCode,
      status: error.status,
      type: error.type,
    })

    // 根据错误类型抛出相应的错误
    if (error.status === 429) {
      const retryAfter = error.headers?.['retry-after']
      throw new AIRateLimitError(this.name, retryAfter ? parseInt(retryAfter) : undefined)
    }

    if (error.status === 402 || error.code === 'insufficient_quota') {
      throw new AIQuotaExceededError(this.name)
    }

    if (error.status === 404 || error.code === 'model_not_found') {
      throw new AIModelNotFoundError(this.name, error.model || 'unknown')
    }

    if (error.code === 'content_filter') {
      throw new AIContentFilterError(this.name)
    }

    throw new AIProviderError(errorMessage, this.name, errorCode, error.status)
  }
}
