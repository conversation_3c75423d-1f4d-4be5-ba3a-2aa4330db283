import { Conversation, ConversationDocument } from '@/models/Conversation'
import { Character, CharacterDocument } from '@/models/Character'
import { User, UserDocument } from '@/models/User'
import { aiService } from '@/services/ai/aiService'
import { ragService } from '@/services/rag/ragService'
import { logger } from '@/utils/logger'
import { AppError } from '@/middleware/errorHandler'
import { AIMessage, AIModelConfig, AIRequestOptions } from '@/services/ai/types'
import { RAGConfig, RAGSearchOptions } from '@/services/rag/types'
import { IMessage } from '@/types'

/**
 * 对话服务类
 */
export class ConversationService {
  /**
   * 创建新对话
   */
  static async createConversation(
    userId: string,
    characterId: string,
    title?: string
  ): Promise<ConversationDocument> {
    try {
      // 验证用户和数字人是否存在
      const [user, character] = await Promise.all([
        User.findById(userId),
        Character.findById(characterId),
      ])

      if (!user) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND')
      }

      if (!character) {
        throw new AppError('数字人不存在', 404, 'CHARACTER_NOT_FOUND')
      }

      // 检查用户是否有权限访问该数字人
      if (!character.isPublic && character.userId.toString() !== userId) {
        throw new AppError('无权访问该数字人', 403, 'ACCESS_DENIED')
      }

      // 创建对话
      const conversation = new Conversation({
        userId,
        characterId,
        title: title || `与${character.name}的对话`,
        messages: [],
        status: 'active',
      })

      await conversation.save()

      logger.info('创建对话成功', {
        conversationId: conversation._id,
        userId,
        characterId,
        characterName: character.name,
      })

      return conversation
    } catch (error) {
      logger.error('创建对话失败:', error)
      throw error
    }
  }

  /**
   * 发送消息并获取AI响应
   */
  static async sendMessage(
    conversationId: string,
    userId: string,
    content: string,
    options: {
      stream?: boolean
      useRAG?: boolean
    } = {}
  ): Promise<{ userMessage: IMessage; assistantMessage?: IMessage }> {
    try {
      // 获取对话信息
      const conversation = await Conversation.findById(conversationId)
        .populate('characterId')
        .populate('userId')

      if (!conversation) {
        throw new AppError('对话不存在', 404, 'CONVERSATION_NOT_FOUND')
      }

      // 验证用户权限
      if (conversation.userId._id.toString() !== userId) {
        throw new AppError('无权访问该对话', 403, 'ACCESS_DENIED')
      }

      const character = conversation.characterId as CharacterDocument
      const user = conversation.userId as UserDocument

      // 检查用户token使用量
      if (user.subscription.tokensUsed >= user.subscription.tokensLimit) {
        throw new AppError('Token使用量已达上限', 403, 'TOKEN_LIMIT_EXCEEDED')
      }

      // 添加用户消息
      const userMessage = await conversation.addMessage({
        role: 'user',
        content,
      })

      // 构建AI消息历史
      const messages = await this.buildMessageHistory(conversation, character)

      // 如果启用RAG，搜索相关文档
      let ragContext = ''
      if (options.useRAG && character.knowledgeBase.enabled) {
        ragContext = await this.searchKnowledgeBase(content, character)
      }

      // 生成AI响应
      const aiConfig: AIModelConfig = {
        provider: character.config.aiModel.provider,
        model: character.config.aiModel.model,
        temperature: character.config.aiModel.temperature,
        maxTokens: character.config.aiModel.maxTokens,
      }

      const aiOptions: AIRequestOptions = {
        userId,
        conversationId,
        characterId: character._id.toString(),
        stream: options.stream,
        ragContext: ragContext ? [ragContext] : undefined,
      }

      if (options.stream) {
        // 流式响应处理在Socket.IO中进行
        return { userMessage }
      } else {
        // 非流式响应
        const aiResponse = await aiService.generateResponse(messages, aiConfig, aiOptions)

        // 添加AI响应消息
        const assistantMessage = await conversation.addMessage({
          role: 'assistant',
          content: aiResponse.content,
          metadata: {
            tokens: aiResponse.usage.totalTokens,
            model: aiResponse.model,
            ragUsed: !!ragContext,
            sources: ragContext ? ['knowledge_base'] : undefined,
            duration: aiResponse.metadata?.duration,
          },
        })

        // 更新用户token使用量
        await this.updateTokenUsage(userId, aiResponse.usage.totalTokens)

        // 更新数字人统计
        await this.updateCharacterStats(character._id.toString(), 0, 1)

        logger.info('消息发送成功', {
          conversationId,
          userId,
          tokensUsed: aiResponse.usage.totalTokens,
          ragUsed: !!ragContext,
        })

        return { userMessage, assistantMessage }
      }
    } catch (error) {
      logger.error('发送消息失败:', error)
      throw error
    }
  }

  /**
   * 流式发送消息
   */
  static async *sendStreamMessage(
    conversationId: string,
    userId: string,
    content: string,
    options: {
      useRAG?: boolean
    } = {}
  ): AsyncGenerator<{ type: 'user' | 'assistant' | 'complete'; data: any }, void, unknown> {
    try {
      // 发送用户消息
      const { userMessage } = await this.sendMessage(conversationId, userId, content, {
        stream: false,
        useRAG: options.useRAG,
      })

      yield { type: 'user', data: userMessage }

      // 获取对话和数字人信息
      const conversation = await Conversation.findById(conversationId)
        .populate('characterId')

      if (!conversation) {
        throw new AppError('对话不存在', 404, 'CONVERSATION_NOT_FOUND')
      }

      const character = conversation.characterId as CharacterDocument

      // 构建消息历史
      const messages = await this.buildMessageHistory(conversation, character)

      // RAG搜索
      let ragContext = ''
      if (options.useRAG && character.knowledgeBase.enabled) {
        ragContext = await this.searchKnowledgeBase(content, character)
      }

      // 生成流式AI响应
      const aiConfig: AIModelConfig = {
        provider: character.config.aiModel.provider,
        model: character.config.aiModel.model,
        temperature: character.config.aiModel.temperature,
        maxTokens: character.config.aiModel.maxTokens,
      }

      const aiOptions: AIRequestOptions = {
        userId,
        conversationId,
        characterId: character._id.toString(),
        ragContext: ragContext ? [ragContext] : undefined,
      }

      let fullContent = ''
      let totalTokens = 0

      for await (const chunk of aiService.generateStreamResponse(messages, aiConfig, aiOptions)) {
        fullContent = chunk.content
        if (chunk.usage) {
          totalTokens = chunk.usage.totalTokens
        }

        yield {
          type: 'assistant',
          data: {
            content: chunk.content,
            delta: chunk.delta,
            isComplete: chunk.isComplete,
          },
        }

        if (chunk.isComplete) {
          break
        }
      }

      // 保存完整的AI响应
      const assistantMessage = await conversation.addMessage({
        role: 'assistant',
        content: fullContent,
        metadata: {
          tokens: totalTokens,
          model: aiConfig.model,
          ragUsed: !!ragContext,
          sources: ragContext ? ['knowledge_base'] : undefined,
        },
      })

      // 更新统计
      await Promise.all([
        this.updateTokenUsage(userId, totalTokens),
        this.updateCharacterStats(character._id.toString(), 0, 1),
      ])

      yield { type: 'complete', data: assistantMessage }

      logger.info('流式消息发送完成', {
        conversationId,
        userId,
        tokensUsed: totalTokens,
        ragUsed: !!ragContext,
      })
    } catch (error) {
      logger.error('流式消息发送失败:', error)
      throw error
    }
  }

  /**
   * 获取对话列表
   */
  static async getConversations(
    userId: string,
    options: {
      page?: number
      limit?: number
      status?: string
      characterId?: string
    } = {}
  ) {
    const { page = 1, limit = 20, status = 'active', characterId } = options

    const query: any = { userId, status }
    if (characterId) {
      query.characterId = characterId
    }

    const [conversations, total] = await Promise.all([
      Conversation.find(query)
        .populate('characterId', 'name avatar')
        .sort({ updatedAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit),
      Conversation.countDocuments(query),
    ])

    return {
      conversations,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    }
  }

  /**
   * 获取对话详情
   */
  static async getConversation(conversationId: string, userId: string) {
    const conversation = await Conversation.findById(conversationId)
      .populate('characterId', 'name avatar config')
      .populate('userId', 'username avatar')

    if (!conversation) {
      throw new AppError('对话不存在', 404, 'CONVERSATION_NOT_FOUND')
    }

    if (conversation.userId._id.toString() !== userId) {
      throw new AppError('无权访问该对话', 403, 'ACCESS_DENIED')
    }

    return conversation
  }

  /**
   * 删除对话
   */
  static async deleteConversation(conversationId: string, userId: string) {
    const conversation = await Conversation.findById(conversationId)

    if (!conversation) {
      throw new AppError('对话不存在', 404, 'CONVERSATION_NOT_FOUND')
    }

    if (conversation.userId.toString() !== userId) {
      throw new AppError('无权删除该对话', 403, 'ACCESS_DENIED')
    }

    await conversation.softDelete()

    logger.info('对话删除成功', { conversationId, userId })
  }

  /**
   * 构建消息历史
   */
  private static async buildMessageHistory(
    conversation: ConversationDocument,
    character: CharacterDocument
  ): Promise<AIMessage[]> {
    const messages: AIMessage[] = []

    // 添加系统提示词
    if (character.config.aiModel.systemPrompt) {
      messages.push({
        role: 'system',
        content: character.config.aiModel.systemPrompt,
      })
    }

    // 添加对话历史（最近20条消息）
    const recentMessages = conversation.messages.slice(-20)
    for (const message of recentMessages) {
      messages.push({
        role: message.role as 'user' | 'assistant' | 'system',
        content: message.content,
      })
    }

    return messages
  }

  /**
   * 搜索知识库
   */
  private static async searchKnowledgeBase(
    query: string,
    character: CharacterDocument
  ): Promise<string> {
    if (!character.knowledgeBase.enabled || !character.knowledgeBase.vectorStoreId) {
      return ''
    }

    try {
      const ragConfig: RAGConfig = {
        chunkSize: 1000,
        chunkOverlap: 200,
        embeddingModel: 'text-embedding-ada-002',
        vectorStore: {
          provider: 'pinecone',
          indexName: character.knowledgeBase.vectorStoreId,
          dimension: 1536,
        },
        retrieval: {
          topK: 5,
          scoreThreshold: 0.7,
          rerankEnabled: false,
        },
      }

      const searchOptions: RAGSearchOptions = {
        topK: 3,
        scoreThreshold: 0.7,
      }

      const result = await ragService.searchDocuments(query, ragConfig, searchOptions)
      return result.context
    } catch (error) {
      logger.error('知识库搜索失败:', error)
      return ''
    }
  }

  /**
   * 更新用户token使用量
   */
  private static async updateTokenUsage(userId: string, tokens: number): Promise<void> {
    try {
      await User.findByIdAndUpdate(userId, {
        $inc: { 'subscription.tokensUsed': tokens },
      })
    } catch (error) {
      logger.error('更新token使用量失败:', error)
    }
  }

  /**
   * 更新数字人统计
   */
  private static async updateCharacterStats(
    characterId: string,
    conversationIncrement: number,
    messageIncrement: number
  ): Promise<void> {
    try {
      await Character.findByIdAndUpdate(characterId, {
        $inc: {
          'stats.conversationCount': conversationIncrement,
          'stats.messageCount': messageIncrement,
        },
        $set: {
          'stats.lastUsedAt': new Date(),
        },
      })
    } catch (error) {
      logger.error('更新数字人统计失败:', error)
    }
  }
}
