import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { logger } from '@/utils/logger'
import { cacheService } from '@/config/redis'
import {
  KnowledgeApiConfig,
  KnowledgeApiRequest,
  KnowledgeApiResponse,
  KnowledgeApiError,
  KnowledgeSearchResult,
} from '@/types'

/**
 * 知识库 API 客户端
 * 负责与外部知识库服务进行通信
 */
export class KnowledgeApiClient {
  private client: AxiosInstance
  private config: KnowledgeApiConfig
  private isHealthy: boolean = true
  private lastHealthCheck: number = 0
  private healthCheckInterval: number = 60000 // 1分钟

  constructor(config: KnowledgeApiConfig) {
    this.config = config
    this.client = this.createAxiosInstance()
    this.setupInterceptors()
  }

  /**
   * 创建 Axios 实例
   */
  private createAxiosInstance(): AxiosInstance {
    return axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        'X-API-Secret': this.config.apiSecret || '',
        'User-Agent': 'AI-Digital-Robots/1.0.0',
      },
    })
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('知识库 API 请求', {
          url: config.url,
          method: config.method,
          headers: config.headers,
        })
        return config
      },
      (error) => {
        logger.error('知识库 API 请求错误', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        logger.debug('知识库 API 响应', {
          status: response.status,
          data: response.data,
        })
        return response
      },
      (error) => {
        logger.error('知识库 API 响应错误', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        })
        return Promise.reject(this.handleApiError(error))
      }
    )
  }

  /**
   * 处理 API 错误
   */
  private handleApiError(error: any): KnowledgeApiError {
    const apiError = new Error(error.message) as KnowledgeApiError
    apiError.name = 'KnowledgeApiError'
    apiError.code = error.response?.data?.error?.code || 'UNKNOWN_ERROR'
    apiError.statusCode = error.response?.status
    apiError.details = error.response?.data
    
    // 判断是否可重试
    apiError.isRetryable = this.isRetryableError(error)
    
    // 更新健康状态
    if (error.response?.status >= 500 || error.code === 'ECONNREFUSED') {
      this.isHealthy = false
    }
    
    return apiError
  }

  /**
   * 判断错误是否可重试
   */
  private isRetryableError(error: any): boolean {
    // 网络错误或服务器错误可重试
    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      return true
    }
    
    // 5xx 错误可重试
    if (error.response?.status >= 500) {
      return true
    }
    
    // 429 限流错误可重试
    if (error.response?.status === 429) {
      return true
    }
    
    return false
  }

  /**
   * 搜索知识库
   */
  async searchKnowledge(request: KnowledgeApiRequest): Promise<KnowledgeApiResponse> {
    const cacheKey = this.generateCacheKey(request)
    
    try {
      // 尝试从缓存获取结果
      const cachedResult = await this.getCachedResult(cacheKey)
      if (cachedResult) {
        logger.info('返回缓存的知识库搜索结果', { cacheKey })
        return cachedResult
      }

      // 健康检查
      await this.checkHealth()
      
      if (!this.isHealthy) {
        throw new Error('知识库服务不可用')
      }

      // 发送请求
      const response = await this.makeRequestWithRetry('/search', {
        method: 'POST',
        data: request,
      })

      const result: KnowledgeApiResponse = response.data
      
      // 缓存结果
      await this.cacheResult(cacheKey, result)
      
      logger.info('知识库搜索成功', {
        query: request.query,
        resultsCount: result.data.results.length,
        processingTime: result.data.processingTime,
      })

      return result
    } catch (error) {
      logger.error('知识库搜索失败', {
        query: request.query,
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * 带重试的请求
   */
  private async makeRequestWithRetry(
    url: string,
    config: AxiosRequestConfig,
    attempt: number = 1
  ): Promise<AxiosResponse> {
    try {
      return await this.client.request({ ...config, url })
    } catch (error) {
      const apiError = error as KnowledgeApiError
      
      if (apiError.isRetryable && attempt < this.config.retryAttempts) {
        const delay = this.config.retryDelay * Math.pow(2, attempt - 1) // 指数退避
        
        logger.warn(`知识库 API 请求失败，${delay}ms 后重试 (${attempt}/${this.config.retryAttempts})`, {
          url,
          error: apiError.message,
        })
        
        await this.sleep(delay)
        return this.makeRequestWithRetry(url, config, attempt + 1)
      }
      
      throw error
    }
  }

  /**
   * 健康检查
   */
  async checkHealth(): Promise<boolean> {
    const now = Date.now()
    
    // 如果距离上次检查时间不足间隔，直接返回缓存状态
    if (now - this.lastHealthCheck < this.healthCheckInterval) {
      return this.isHealthy
    }
    
    try {
      const response = await this.client.get('/health', { timeout: 5000 })
      this.isHealthy = response.status === 200
      this.lastHealthCheck = now
      
      logger.debug('知识库服务健康检查', { healthy: this.isHealthy })
      
      return this.isHealthy
    } catch (error) {
      this.isHealthy = false
      this.lastHealthCheck = now
      
      logger.warn('知识库服务健康检查失败', {
        error: error instanceof Error ? error.message : String(error),
      })
      
      return false
    }
  }

  /**
   * 获取服务状态
   */
  getStatus(): { healthy: boolean; lastCheck: number } {
    return {
      healthy: this.isHealthy,
      lastCheck: this.lastHealthCheck,
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(request: KnowledgeApiRequest): string {
    const key = JSON.stringify({
      query: request.query,
      knowledgeBaseId: request.knowledgeBaseId,
      topK: request.topK,
      scoreThreshold: request.scoreThreshold,
      filters: request.filters,
    })
    
    const crypto = require('crypto')
    return `knowledge_api:${crypto.createHash('md5').update(key).digest('hex')}`
  }

  /**
   * 获取缓存结果
   */
  private async getCachedResult(cacheKey: string): Promise<KnowledgeApiResponse | null> {
    try {
      const cached = await cacheService.get(cacheKey)
      return cached ? JSON.parse(cached) : null
    } catch (error) {
      logger.error('获取知识库缓存失败', error)
      return null
    }
  }

  /**
   * 缓存结果
   */
  private async cacheResult(cacheKey: string, result: KnowledgeApiResponse): Promise<void> {
    try {
      // 缓存30分钟
      await cacheService.set(cacheKey, JSON.stringify(result), 1800)
    } catch (error) {
      logger.error('缓存知识库结果失败', error)
    }
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<KnowledgeApiConfig>): void {
    this.config = { ...this.config, ...newConfig }
    this.client = this.createAxiosInstance()
    this.setupInterceptors()
    
    logger.info('知识库 API 配置已更新', { config: this.config })
  }

  /**
   * 检查配置是否有效
   */
  isConfigValid(): boolean {
    return !!(
      this.config.enabled &&
      this.config.baseUrl &&
      this.config.apiKey &&
      this.config.timeout > 0
    )
  }
}
