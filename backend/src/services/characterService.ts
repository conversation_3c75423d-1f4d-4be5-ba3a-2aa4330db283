import { Character, CharacterDocument } from '@/models/Character'
import { User } from '@/models/User'
import { Conversation } from '@/models/Conversation'
import { Knowledge } from '@/models/Knowledge'
import { aiService } from '@/services/ai/aiService'
import { logger } from '@/utils/logger'
import { AppError } from '@/middleware/errorHandler'
import { CreateCharacterRequest, PaginationParams } from '@/types'

/**
 * 数字人服务类
 */
export class CharacterService {
  /**
   * 创建数字人
   */
  static async createCharacter(
    userId: string,
    characterData: CreateCharacterRequest
  ): Promise<CharacterDocument> {
    try {
      // 验证用户是否存在
      const user = await User.findById(userId)
      if (!user) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND')
      }

      // 验证AI模型配置
      if (!aiService.validateConfig(characterData.aiModel)) {
        throw new AppError('无效的AI模型配置', 400, 'INVALID_AI_CONFIG')
      }

      // 创建数字人
      const character = new Character({
        name: characterData.name,
        description: characterData.description,
        avatar: characterData.avatar,
        userId,
        config: {
          personality: characterData.personality,
          expertise: characterData.expertise,
          language: characterData.language,
          voice: characterData.voice,
          aiModel: characterData.aiModel,
        },
        knowledgeBase: {
          enabled: false,
          documents: [],
        },
        isPublic: characterData.isPublic,
        tags: characterData.tags,
      })

      await character.save()

      logger.info('数字人创建成功', {
        characterId: character._id,
        userId,
        name: character.name,
      })

      return character
    } catch (error) {
      logger.error('数字人创建失败:', error)
      throw error
    }
  }

  /**
   * 更新数字人
   */
  static async updateCharacter(
    characterId: string,
    userId: string,
    updates: Partial<CreateCharacterRequest>
  ): Promise<CharacterDocument> {
    try {
      const character = await Character.findById(characterId)
      if (!character) {
        throw new AppError('数字人不存在', 404, 'CHARACTER_NOT_FOUND')
      }

      // 验证权限
      if (character.userId.toString() !== userId) {
        throw new AppError('无权修改该数字人', 403, 'ACCESS_DENIED')
      }

      // 验证AI模型配置（如果有更新）
      if (updates.aiModel && !aiService.validateConfig(updates.aiModel)) {
        throw new AppError('无效的AI模型配置', 400, 'INVALID_AI_CONFIG')
      }

      // 更新字段
      if (updates.name) character.name = updates.name
      if (updates.description !== undefined) character.description = updates.description
      if (updates.avatar !== undefined) character.avatar = updates.avatar
      if (updates.personality !== undefined) character.config.personality = updates.personality
      if (updates.expertise) character.config.expertise = updates.expertise
      if (updates.language) character.config.language = updates.language
      if (updates.voice) character.config.voice = updates.voice
      if (updates.aiModel) character.config.aiModel = updates.aiModel
      if (updates.tags) character.tags = updates.tags
      if (updates.isPublic !== undefined) character.isPublic = updates.isPublic

      await character.save()

      logger.info('数字人更新成功', {
        characterId,
        userId,
        name: character.name,
      })

      return character
    } catch (error) {
      logger.error('数字人更新失败:', error)
      throw error
    }
  }

  /**
   * 删除数字人
   */
  static async deleteCharacter(characterId: string, userId: string): Promise<void> {
    try {
      const character = await Character.findById(characterId)
      if (!character) {
        throw new AppError('数字人不存在', 404, 'CHARACTER_NOT_FOUND')
      }

      // 验证权限
      if (character.userId.toString() !== userId) {
        throw new AppError('无权删除该数字人', 403, 'ACCESS_DENIED')
      }

      // 检查是否有关联的对话
      const conversationCount = await Conversation.countDocuments({
        characterId,
        status: { $ne: 'deleted' },
      })

      if (conversationCount > 0) {
        throw new AppError(
          '该数字人还有关联的对话，请先删除相关对话',
          400,
          'HAS_CONVERSATIONS'
        )
      }

      // 删除数字人
      await Character.findByIdAndDelete(characterId)

      // TODO: 清理知识库数据

      logger.info('数字人删除成功', {
        characterId,
        userId,
        name: character.name,
      })
    } catch (error) {
      logger.error('数字人删除失败:', error)
      throw error
    }
  }

  /**
   * 获取数字人详情
   */
  static async getCharacter(characterId: string, userId?: string): Promise<CharacterDocument> {
    try {
      const character = await Character.findById(characterId)
      if (!character) {
        throw new AppError('数字人不存在', 404, 'CHARACTER_NOT_FOUND')
      }

      // 检查访问权限
      if (!character.isPublic && (!userId || character.userId.toString() !== userId)) {
        throw new AppError('无权访问该数字人', 403, 'ACCESS_DENIED')
      }

      return character
    } catch (error) {
      logger.error('获取数字人详情失败:', error)
      throw error
    }
  }

  /**
   * 获取用户的数字人列表
   */
  static async getUserCharacters(
    userId: string,
    params: PaginationParams = {}
  ) {
    const {
      page = 1,
      limit = 20,
      sortBy = 'stats.lastUsedAt',
      sortOrder = 'desc',
      search,
    } = params

    const query: any = { userId }

    // 搜索功能
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ]
    }

    const sortOptions: any = {}
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1

    const [characters, total] = await Promise.all([
      Character.find(query)
        .sort(sortOptions)
        .skip((page - 1) * limit)
        .limit(limit),
      Character.countDocuments(query),
    ])

    return {
      characters,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    }
  }

  /**
   * 获取公开数字人列表
   */
  static async getPublicCharacters(params: PaginationParams = {}) {
    const {
      page = 1,
      limit = 20,
      sortBy = 'stats.conversationCount',
      sortOrder = 'desc',
      search,
      filters = {},
    } = params

    const query: any = { isPublic: true }

    // 搜索功能
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ]
    }

    // 过滤器
    if (filters.tags && filters.tags.length > 0) {
      query.tags = { $in: filters.tags }
    }

    if (filters.language) {
      query['config.language'] = filters.language
    }

    const sortOptions: any = {}
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1

    const [characters, total] = await Promise.all([
      Character.find(query)
        .populate('userId', 'username avatar')
        .sort(sortOptions)
        .skip((page - 1) * limit)
        .limit(limit),
      Character.countDocuments(query),
    ])

    return {
      characters,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    }
  }

  /**
   * 克隆数字人
   */
  static async cloneCharacter(
    characterId: string,
    userId: string,
    newName?: string
  ): Promise<CharacterDocument> {
    try {
      const originalCharacter = await Character.findById(characterId)
      if (!originalCharacter) {
        throw new AppError('数字人不存在', 404, 'CHARACTER_NOT_FOUND')
      }

      // 检查是否可以克隆
      if (!originalCharacter.isPublic && originalCharacter.userId.toString() !== userId) {
        throw new AppError('无权克隆该数字人', 403, 'ACCESS_DENIED')
      }

      // 创建克隆
      const clonedCharacter = new Character({
        name: newName || `${originalCharacter.name} (副本)`,
        description: originalCharacter.description,
        avatar: originalCharacter.avatar,
        userId,
        config: originalCharacter.config,
        knowledgeBase: {
          enabled: false, // 克隆时不复制知识库
          documents: [],
        },
        isPublic: false, // 克隆的数字人默认为私有
        tags: originalCharacter.tags,
      })

      await clonedCharacter.save()

      logger.info('数字人克隆成功', {
        originalCharacterId: characterId,
        clonedCharacterId: clonedCharacter._id,
        userId,
      })

      return clonedCharacter
    } catch (error) {
      logger.error('数字人克隆失败:', error)
      throw error
    }
  }

  /**
   * 绑定知识库
   */
  static async bindKnowledgeBase(
    characterId: string,
    userId: string,
    knowledgeId: string
  ): Promise<CharacterDocument> {
    try {
      const [character, knowledge] = await Promise.all([
        Character.findById(characterId),
        Knowledge.findById(knowledgeId),
      ])

      if (!character) {
        throw new AppError('数字人不存在', 404, 'CHARACTER_NOT_FOUND')
      }

      if (!knowledge) {
        throw new AppError('知识库不存在', 404, 'KNOWLEDGE_NOT_FOUND')
      }

      // 验证权限
      if (character.userId.toString() !== userId || knowledge.userId.toString() !== userId) {
        throw new AppError('无权操作', 403, 'ACCESS_DENIED')
      }

      // 绑定知识库
      character.knowledgeBase.enabled = true
      character.knowledgeBase.vectorStoreId = knowledge.vectorStore?.indexId || ''
      
      await character.save()

      logger.info('知识库绑定成功', {
        characterId,
        knowledgeId,
        userId,
      })

      return character
    } catch (error) {
      logger.error('知识库绑定失败:', error)
      throw error
    }
  }

  /**
   * 解绑知识库
   */
  static async unbindKnowledgeBase(
    characterId: string,
    userId: string
  ): Promise<CharacterDocument> {
    try {
      const character = await Character.findById(characterId)
      if (!character) {
        throw new AppError('数字人不存在', 404, 'CHARACTER_NOT_FOUND')
      }

      // 验证权限
      if (character.userId.toString() !== userId) {
        throw new AppError('无权操作', 403, 'ACCESS_DENIED')
      }

      // 解绑知识库
      character.knowledgeBase.enabled = false
      character.knowledgeBase.vectorStoreId = ''
      
      await character.save()

      logger.info('知识库解绑成功', {
        characterId,
        userId,
      })

      return character
    } catch (error) {
      logger.error('知识库解绑失败:', error)
      throw error
    }
  }

  /**
   * 获取数字人统计信息
   */
  static async getCharacterStats(characterId: string, userId: string) {
    try {
      const character = await Character.findById(characterId)
      if (!character) {
        throw new AppError('数字人不存在', 404, 'CHARACTER_NOT_FOUND')
      }

      // 验证权限
      if (character.userId.toString() !== userId) {
        throw new AppError('无权访问', 403, 'ACCESS_DENIED')
      }

      // 获取详细统计
      const [conversationStats, recentConversations] = await Promise.all([
        Conversation.aggregate([
          { $match: { characterId: character._id, status: 'active' } },
          {
            $group: {
              _id: null,
              totalConversations: { $sum: 1 },
              totalMessages: { $sum: { $size: '$messages' } },
              averageMessagesPerConversation: { $avg: { $size: '$messages' } },
            },
          },
        ]),
        Conversation.find({ characterId, status: 'active' })
          .sort({ updatedAt: -1 })
          .limit(5)
          .select('title updatedAt'),
      ])

      const stats = conversationStats[0] || {
        totalConversations: 0,
        totalMessages: 0,
        averageMessagesPerConversation: 0,
      }

      return {
        ...character.stats,
        ...stats,
        recentConversations,
      }
    } catch (error) {
      logger.error('获取数字人统计失败:', error)
      throw error
    }
  }
}
