import fs from 'fs/promises'
import path from 'path'
import pdfParse from 'pdf-parse'
import mammoth from 'mammoth'
import { logger } from '@/utils/logger'
import {
  DocumentProcessor,
  DocumentChunk,
  DocumentProcessingError,
} from './types'

/**
 * 文档处理器
 */
export class DefaultDocumentProcessor implements DocumentProcessor {
  public readonly name = 'default'
  public readonly supportedTypes = ['pdf', 'docx', 'txt', 'md', 'rtf']

  /**
   * 提取文档文本
   */
  async extractText(filePath: string, fileType: string): Promise<string> {
    try {
      switch (fileType.toLowerCase()) {
        case 'pdf':
          return await this.extractPdfText(filePath)
        case 'docx':
          return await this.extractDocxText(filePath)
        case 'txt':
        case 'md':
        case 'rtf':
          return await this.extractPlainText(filePath)
        default:
          throw new DocumentProcessingError(
            `不支持的文件类型: ${fileType}`,
            fileType
          )
      }
    } catch (error) {
      logger.error('文档文本提取失败:', {
        filePath,
        fileType,
        error: error instanceof Error ? error.message : String(error),
      })
      
      if (error instanceof DocumentProcessingError) {
        throw error
      }
      
      throw new DocumentProcessingError(
        `文档处理失败: ${error instanceof Error ? error.message : String(error)}`,
        fileType,
        error
      )
    }
  }

  /**
   * 文本分块
   */
  chunkText(
    text: string,
    options: {
      chunkSize: number
      chunkOverlap: number
      preserveStructure?: boolean
    }
  ): DocumentChunk[] {
    const { chunkSize, chunkOverlap, preserveStructure = false } = options
    
    // 清理文本
    const cleanText = this.cleanText(text)
    
    if (preserveStructure) {
      return this.chunkByStructure(cleanText, chunkSize, chunkOverlap)
    } else {
      return this.chunkBySize(cleanText, chunkSize, chunkOverlap)
    }
  }

  private async extractPdfText(filePath: string): Promise<string> {
    try {
      const buffer = await fs.readFile(filePath)
      const data = await pdfParse(buffer)
      return data.text
    } catch (error) {
      throw new DocumentProcessingError(
        'PDF文件解析失败',
        'pdf',
        error
      )
    }
  }

  private async extractDocxText(filePath: string): Promise<string> {
    try {
      const buffer = await fs.readFile(filePath)
      const result = await mammoth.extractRawText({ buffer })
      return result.value
    } catch (error) {
      throw new DocumentProcessingError(
        'DOCX文件解析失败',
        'docx',
        error
      )
    }
  }

  private async extractPlainText(filePath: string): Promise<string> {
    try {
      return await fs.readFile(filePath, 'utf-8')
    } catch (error) {
      throw new DocumentProcessingError(
        '文本文件读取失败',
        'txt',
        error
      )
    }
  }

  private cleanText(text: string): string {
    return text
      // 移除多余的空白字符
      .replace(/\s+/g, ' ')
      // 移除特殊字符
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // 标准化换行符
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // 移除多余的换行符
      .replace(/\n{3,}/g, '\n\n')
      .trim()
  }

  private chunkBySize(
    text: string,
    chunkSize: number,
    chunkOverlap: number
  ): DocumentChunk[] {
    const chunks: DocumentChunk[] = []
    const sentences = this.splitIntoSentences(text)
    
    let currentChunk = ''
    let currentTokens = 0
    let chunkIndex = 0
    let startIndex = 0

    for (const sentence of sentences) {
      const sentenceTokens = this.estimateTokens(sentence)
      
      // 如果当前块加上新句子会超过大小限制
      if (currentTokens + sentenceTokens > chunkSize && currentChunk.length > 0) {
        // 保存当前块
        chunks.push({
          id: `chunk_${chunkIndex}`,
          content: currentChunk.trim(),
          metadata: {
            documentId: '',
            documentName: '',
            chunkIndex,
            startIndex,
            endIndex: startIndex + currentChunk.length,
            tokens: currentTokens,
          },
        })

        // 开始新块，保留重叠部分
        const overlapText = this.getOverlapText(currentChunk, chunkOverlap)
        currentChunk = overlapText + sentence
        currentTokens = this.estimateTokens(currentChunk)
        startIndex = startIndex + currentChunk.length - overlapText.length
        chunkIndex++
      } else {
        currentChunk += (currentChunk ? ' ' : '') + sentence
        currentTokens += sentenceTokens
      }
    }

    // 添加最后一个块
    if (currentChunk.trim()) {
      chunks.push({
        id: `chunk_${chunkIndex}`,
        content: currentChunk.trim(),
        metadata: {
          documentId: '',
          documentName: '',
          chunkIndex,
          startIndex,
          endIndex: startIndex + currentChunk.length,
          tokens: currentTokens,
        },
      })
    }

    return chunks
  }

  private chunkByStructure(
    text: string,
    chunkSize: number,
    chunkOverlap: number
  ): DocumentChunk[] {
    // 按段落分割
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim())
    const chunks: DocumentChunk[] = []
    
    let currentChunk = ''
    let currentTokens = 0
    let chunkIndex = 0
    let startIndex = 0

    for (const paragraph of paragraphs) {
      const paragraphTokens = this.estimateTokens(paragraph)
      
      // 如果单个段落就超过块大小，需要进一步分割
      if (paragraphTokens > chunkSize) {
        // 先保存当前块（如果有内容）
        if (currentChunk.trim()) {
          chunks.push({
            id: `chunk_${chunkIndex}`,
            content: currentChunk.trim(),
            metadata: {
              documentId: '',
              documentName: '',
              chunkIndex,
              startIndex,
              endIndex: startIndex + currentChunk.length,
              tokens: currentTokens,
            },
          })
          chunkIndex++
        }

        // 分割大段落
        const subChunks = this.chunkBySize(paragraph, chunkSize, chunkOverlap)
        for (const subChunk of subChunks) {
          subChunk.id = `chunk_${chunkIndex}`
          subChunk.metadata.chunkIndex = chunkIndex
          chunks.push(subChunk)
          chunkIndex++
        }

        currentChunk = ''
        currentTokens = 0
        startIndex = startIndex + paragraph.length
      } else if (currentTokens + paragraphTokens > chunkSize && currentChunk.length > 0) {
        // 保存当前块
        chunks.push({
          id: `chunk_${chunkIndex}`,
          content: currentChunk.trim(),
          metadata: {
            documentId: '',
            documentName: '',
            chunkIndex,
            startIndex,
            endIndex: startIndex + currentChunk.length,
            tokens: currentTokens,
          },
        })

        currentChunk = paragraph
        currentTokens = paragraphTokens
        startIndex = startIndex + currentChunk.length
        chunkIndex++
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + paragraph
        currentTokens += paragraphTokens
      }
    }

    // 添加最后一个块
    if (currentChunk.trim()) {
      chunks.push({
        id: `chunk_${chunkIndex}`,
        content: currentChunk.trim(),
        metadata: {
          documentId: '',
          documentName: '',
          chunkIndex,
          startIndex,
          endIndex: startIndex + currentChunk.length,
          tokens: currentTokens,
        },
      })
    }

    return chunks
  }

  private splitIntoSentences(text: string): string[] {
    // 简单的句子分割（可以使用更复杂的NLP库）
    return text
      .split(/[.!?]+/)
      .map(s => s.trim())
      .filter(s => s.length > 0)
  }

  private estimateTokens(text: string): number {
    // 简单的token估算（1 token ≈ 4 characters for Chinese, 4 characters for English）
    return Math.ceil(text.length / 4)
  }

  private getOverlapText(text: string, overlapSize: number): string {
    const sentences = this.splitIntoSentences(text)
    let overlapText = ''
    let tokens = 0

    // 从末尾开始取句子，直到达到重叠大小
    for (let i = sentences.length - 1; i >= 0; i--) {
      const sentence = sentences[i]
      const sentenceTokens = this.estimateTokens(sentence)
      
      if (tokens + sentenceTokens <= overlapSize) {
        overlapText = sentence + (overlapText ? ' ' + overlapText : '')
        tokens += sentenceTokens
      } else {
        break
      }
    }

    return overlapText
  }
}
