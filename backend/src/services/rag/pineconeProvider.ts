import { Pinecone } from '@pinecone-database/pinecone'
import { logger } from '@/utils/logger'
import {
  VectorStoreProvider,
  VectorSearchResult,
  VectorStoreError,
} from './types'

/**
 * Pinecone向量存储提供商
 */
export class PineconeProvider implements VectorStoreProvider {
  public readonly name = 'pinecone'
  private client: Pinecone
  private isInitialized = false

  constructor() {
    this.initialize()
  }

  private async initialize(): Promise<void> {
    try {
      const apiKey = process.env.PINECONE_API_KEY
      if (!apiKey) {
        logger.warn('Pinecone API密钥未配置')
        return
      }

      this.client = new Pinecone({
        apiKey,
        environment: process.env.PINECONE_ENVIRONMENT || 'us-east1-gcp',
      })

      this.isInitialized = true
      logger.info('Pinecone服务初始化成功')
    } catch (error) {
      logger.error('Pinecone服务初始化失败:', error)
    }
  }

  isAvailable(): boolean {
    return this.isInitialized && !!process.env.PINECONE_API_KEY
  }

  async createIndex(indexName: string, dimension: number, metadata?: any): Promise<void> {
    if (!this.isAvailable()) {
      throw new VectorStoreError('Pinecone服务不可用', this.name)
    }

    try {
      await this.client.createIndex({
        name: indexName,
        dimension,
        metric: 'cosine',
        spec: {
          serverless: {
            cloud: 'aws',
            region: 'us-east-1',
          },
        },
        ...metadata,
      })

      logger.info(`Pinecone索引创建成功: ${indexName}`)
    } catch (error: any) {
      if (error.status === 409) {
        logger.info(`Pinecone索引已存在: ${indexName}`)
        return
      }
      
      logger.error('Pinecone索引创建失败:', error)
      throw new VectorStoreError(
        `索引创建失败: ${error.message}`,
        this.name,
        error
      )
    }
  }

  async deleteIndex(indexName: string): Promise<void> {
    if (!this.isAvailable()) {
      throw new VectorStoreError('Pinecone服务不可用', this.name)
    }

    try {
      await this.client.deleteIndex(indexName)
      logger.info(`Pinecone索引删除成功: ${indexName}`)
    } catch (error: any) {
      logger.error('Pinecone索引删除失败:', error)
      throw new VectorStoreError(
        `索引删除失败: ${error.message}`,
        this.name,
        error
      )
    }
  }

  async indexExists(indexName: string): Promise<boolean> {
    if (!this.isAvailable()) {
      return false
    }

    try {
      const indexes = await this.client.listIndexes()
      return indexes.indexes?.some(index => index.name === indexName) || false
    } catch (error) {
      logger.error('检查Pinecone索引存在性失败:', error)
      return false
    }
  }

  async upsertVectors(
    indexName: string,
    vectors: Array<{
      id: string
      values: number[]
      metadata?: any
    }>
  ): Promise<void> {
    if (!this.isAvailable()) {
      throw new VectorStoreError('Pinecone服务不可用', this.name)
    }

    try {
      const index = this.client.index(indexName)
      
      // 分批处理向量（Pinecone限制每批最多100个向量）
      const batchSize = 100
      for (let i = 0; i < vectors.length; i += batchSize) {
        const batch = vectors.slice(i, i + batchSize)
        await index.upsert(batch)
      }

      logger.info(`Pinecone向量上传成功: ${vectors.length}个向量`)
    } catch (error: any) {
      logger.error('Pinecone向量上传失败:', error)
      throw new VectorStoreError(
        `向量上传失败: ${error.message}`,
        this.name,
        error
      )
    }
  }

  async deleteVectors(indexName: string, ids: string[]): Promise<void> {
    if (!this.isAvailable()) {
      throw new VectorStoreError('Pinecone服务不可用', this.name)
    }

    try {
      const index = this.client.index(indexName)
      await index.deleteMany(ids)
      
      logger.info(`Pinecone向量删除成功: ${ids.length}个向量`)
    } catch (error: any) {
      logger.error('Pinecone向量删除失败:', error)
      throw new VectorStoreError(
        `向量删除失败: ${error.message}`,
        this.name,
        error
      )
    }
  }

  async search(
    indexName: string,
    queryVector: number[],
    topK: number,
    filter?: any
  ): Promise<VectorSearchResult[]> {
    if (!this.isAvailable()) {
      throw new VectorStoreError('Pinecone服务不可用', this.name)
    }

    try {
      const index = this.client.index(indexName)
      
      const response = await index.query({
        vector: queryVector,
        topK,
        includeMetadata: true,
        filter,
      })

      const results: VectorSearchResult[] = []
      
      if (response.matches) {
        for (const match of response.matches) {
          if (match.metadata) {
            results.push({
              id: match.id,
              content: match.metadata.content as string,
              score: match.score || 0,
              metadata: {
                documentId: match.metadata.documentId as string,
                documentName: match.metadata.documentName as string,
                chunkIndex: match.metadata.chunkIndex as number,
                ...match.metadata,
              },
            })
          }
        }
      }

      logger.info(`Pinecone搜索完成: 返回${results.length}个结果`)
      return results
    } catch (error: any) {
      logger.error('Pinecone搜索失败:', error)
      throw new VectorStoreError(
        `向量搜索失败: ${error.message}`,
        this.name,
        error
      )
    }
  }

  async getIndexStats(indexName: string): Promise<{
    totalVectors: number
    dimension: number
    indexSize: number
  }> {
    if (!this.isAvailable()) {
      throw new VectorStoreError('Pinecone服务不可用', this.name)
    }

    try {
      const index = this.client.index(indexName)
      const stats = await index.describeIndexStats()

      return {
        totalVectors: stats.totalVectorCount || 0,
        dimension: stats.dimension || 0,
        indexSize: stats.indexFullness || 0,
      }
    } catch (error: any) {
      logger.error('获取Pinecone索引统计失败:', error)
      throw new VectorStoreError(
        `获取索引统计失败: ${error.message}`,
        this.name,
        error
      )
    }
  }
}
