import path from 'path'
import { logger } from '@/utils/logger'
import { aiService } from '@/services/ai/aiService'
import { DefaultDocumentProcessor } from './documentProcessor'
import { PineconeProvider } from './pineconeProvider'
import {
  VectorStoreProvider,
  DocumentProcessor,
  RAGConfig,
  RAGSearchOptions,
  RAGSearchResult,
  DocumentUploadResult,
  DocumentChunk,
  RAGError,
  VectorStoreError,
  DocumentProcessingError,
  EmbeddingError,
} from './types'

/**
 * RAG服务管理器
 */
export class RAGService {
  private vectorStoreProviders: Map<string, VectorStoreProvider> = new Map()
  private documentProcessors: Map<string, DocumentProcessor> = new Map()
  private defaultVectorStore: string = 'pinecone'
  private defaultProcessor: string = 'default'

  constructor() {
    this.initializeProviders()
  }

  private initializeProviders(): void {
    // 初始化向量存储提供商
    const pineconeProvider = new PineconeProvider()
    this.vectorStoreProviders.set('pinecone', pineconeProvider)

    // 初始化文档处理器
    const defaultProcessor = new DefaultDocumentProcessor()
    this.documentProcessors.set('default', defaultProcessor)

    // 设置默认提供商
    if (pineconeProvider.isAvailable()) {
      this.defaultVectorStore = 'pinecone'
    }

    logger.info('RAG服务提供商初始化完成', {
      vectorStores: Array.from(this.vectorStoreProviders.keys()),
      processors: Array.from(this.documentProcessors.keys()),
      defaultVectorStore: this.defaultVectorStore,
    })
  }

  /**
   * 上传并处理文档
   */
  async uploadDocument(
    filePath: string,
    fileName: string,
    documentId: string,
    config: RAGConfig
  ): Promise<DocumentUploadResult> {
    const startTime = Date.now()

    try {
      // 获取文件类型
      const fileExtension = path.extname(fileName).slice(1).toLowerCase()
      
      // 获取文档处理器
      const processor = this.getDocumentProcessor(this.defaultProcessor)
      
      if (!processor.supportedTypes.includes(fileExtension)) {
        throw new DocumentProcessingError(
          `不支持的文件类型: ${fileExtension}`,
          fileExtension
        )
      }

      // 提取文档文本
      logger.info(`开始处理文档: ${fileName}`)
      const text = await processor.extractText(filePath, fileExtension)
      
      if (!text.trim()) {
        throw new DocumentProcessingError(
          '文档内容为空',
          fileExtension
        )
      }

      // 文本分块
      const chunks = processor.chunkText(text, {
        chunkSize: config.chunkSize,
        chunkOverlap: config.chunkOverlap,
        preserveStructure: true,
      })

      if (chunks.length === 0) {
        throw new DocumentProcessingError(
          '文档分块失败',
          fileExtension
        )
      }

      // 更新块的元数据
      chunks.forEach((chunk, index) => {
        chunk.id = `${documentId}_chunk_${index}`
        chunk.metadata.documentId = documentId
        chunk.metadata.documentName = fileName
      })

      // 生成嵌入向量
      logger.info(`生成文档嵌入向量: ${chunks.length}个块`)
      const embeddings = await this.generateEmbeddings(
        chunks.map(chunk => chunk.content),
        config.embeddingModel
      )

      // 准备向量数据
      const vectors = chunks.map((chunk, index) => ({
        id: chunk.id,
        values: embeddings[index].embedding,
        metadata: {
          content: chunk.content,
          ...chunk.metadata,
        },
      }))

      // 上传到向量存储
      const vectorStore = this.getVectorStoreProvider(config.vectorStore.provider)
      
      // 确保索引存在
      if (!(await vectorStore.indexExists(config.vectorStore.indexName))) {
        await vectorStore.createIndex(
          config.vectorStore.indexName,
          config.vectorStore.dimension
        )
      }

      await vectorStore.upsertVectors(config.vectorStore.indexName, vectors)

      const processingTime = Date.now() - startTime
      const totalTokens = chunks.reduce((sum, chunk) => sum + chunk.metadata.tokens, 0)

      logger.info(`文档处理完成: ${fileName}`, {
        chunks: chunks.length,
        tokens: totalTokens,
        processingTime,
      })

      return {
        documentId,
        chunks: chunks.length,
        tokens: totalTokens,
        processingTime,
        status: 'success',
      }
    } catch (error) {
      const processingTime = Date.now() - startTime
      
      logger.error('文档处理失败:', {
        fileName,
        documentId,
        error: error instanceof Error ? error.message : String(error),
        processingTime,
      })

      return {
        documentId,
        chunks: 0,
        tokens: 0,
        processingTime,
        status: 'failed',
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }

  /**
   * 删除文档
   */
  async deleteDocument(
    documentId: string,
    config: RAGConfig
  ): Promise<void> {
    try {
      const vectorStore = this.getVectorStoreProvider(config.vectorStore.provider)
      
      // 获取文档的所有向量ID
      const stats = await vectorStore.getIndexStats(config.vectorStore.indexName)
      
      // TODO: 实现更精确的文档向量删除
      // 目前Pinecone不支持按metadata过滤删除，需要先查询再删除
      
      logger.info(`文档删除完成: ${documentId}`)
    } catch (error) {
      logger.error('文档删除失败:', {
        documentId,
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * 搜索相关文档
   */
  async searchDocuments(
    query: string,
    config: RAGConfig,
    options: RAGSearchOptions = {}
  ): Promise<RAGSearchResult> {
    try {
      // 生成查询向量
      const queryEmbedding = await aiService.generateEmbedding({
        text: query,
        model: config.embeddingModel,
      })

      // 向量搜索
      const vectorStore = this.getVectorStoreProvider(config.vectorStore.provider)
      const searchResults = await vectorStore.search(
        config.vectorStore.indexName,
        queryEmbedding.embedding,
        options.topK || config.retrieval.topK,
        options.filter
      )

      // 过滤低分结果
      const scoreThreshold = options.scoreThreshold || config.retrieval.scoreThreshold
      const filteredResults = searchResults.filter(result => result.score >= scoreThreshold)

      // 构建上下文
      const context = this.buildContext(filteredResults)

      // 提取来源信息
      const sources = this.extractSources(filteredResults)

      logger.info('文档搜索完成', {
        query,
        totalResults: searchResults.length,
        filteredResults: filteredResults.length,
        contextLength: context.length,
      })

      return {
        chunks: filteredResults,
        context,
        sources,
        totalResults: searchResults.length,
      }
    } catch (error) {
      logger.error('文档搜索失败:', {
        query,
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * 获取索引统计信息
   */
  async getIndexStats(config: RAGConfig): Promise<{
    totalVectors: number
    dimension: number
    indexSize: number
  }> {
    try {
      const vectorStore = this.getVectorStoreProvider(config.vectorStore.provider)
      return await vectorStore.getIndexStats(config.vectorStore.indexName)
    } catch (error) {
      logger.error('获取索引统计失败:', error)
      throw error
    }
  }

  private async generateEmbeddings(texts: string[], model: string) {
    try {
      return await aiService.generateEmbeddings(texts, model)
    } catch (error) {
      throw new EmbeddingError(
        `嵌入生成失败: ${error instanceof Error ? error.message : String(error)}`,
        error
      )
    }
  }

  private buildContext(results: any[]): string {
    return results
      .map(result => result.content)
      .join('\n\n---\n\n')
  }

  private extractSources(results: any[]) {
    const sourceMap = new Map()
    
    for (const result of results) {
      const { documentId, documentName } = result.metadata
      if (!sourceMap.has(documentId)) {
        sourceMap.set(documentId, {
          documentId,
          documentName,
          relevanceScore: result.score,
        })
      } else {
        // 更新最高相关性分数
        const existing = sourceMap.get(documentId)
        if (result.score > existing.relevanceScore) {
          existing.relevanceScore = result.score
        }
      }
    }
    
    return Array.from(sourceMap.values())
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
  }

  private getVectorStoreProvider(providerName: string): VectorStoreProvider {
    const provider = this.vectorStoreProviders.get(providerName)
    if (!provider) {
      throw new VectorStoreError(`未知的向量存储提供商: ${providerName}`, providerName)
    }
    
    if (!provider.isAvailable()) {
      throw new VectorStoreError(`向量存储提供商不可用: ${providerName}`, providerName)
    }
    
    return provider
  }

  private getDocumentProcessor(processorName: string): DocumentProcessor {
    const processor = this.documentProcessors.get(processorName)
    if (!processor) {
      throw new RAGError(`未知的文档处理器: ${processorName}`, 'UNKNOWN_PROCESSOR')
    }
    
    return processor
  }

  /**
   * 获取可用的向量存储提供商
   */
  getAvailableVectorStores(): string[] {
    return Array.from(this.vectorStoreProviders.entries())
      .filter(([_, provider]) => provider.isAvailable())
      .map(([name, _]) => name)
  }

  /**
   * 获取支持的文档类型
   */
  getSupportedDocumentTypes(): string[] {
    const allTypes = new Set<string>()
    
    for (const processor of this.documentProcessors.values()) {
      processor.supportedTypes.forEach(type => allTypes.add(type))
    }
    
    return Array.from(allTypes)
  }
}

// 创建全局RAG服务实例
export const ragService = new RAGService()
