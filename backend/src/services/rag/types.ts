// RAG系统相关类型定义

export interface DocumentChunk {
  id: string
  content: string
  metadata: {
    documentId: string
    documentName: string
    chunkIndex: number
    startIndex: number
    endIndex: number
    tokens: number
    [key: string]: any
  }
}

export interface VectorSearchResult {
  id: string
  content: string
  score: number
  metadata: {
    documentId: string
    documentName: string
    chunkIndex: number
    [key: string]: any
  }
}

export interface VectorStoreProvider {
  name: string
  
  // 索引管理
  createIndex(indexName: string, dimension: number, metadata?: any): Promise<void>
  deleteIndex(indexName: string): Promise<void>
  indexExists(indexName: string): Promise<boolean>
  
  // 向量操作
  upsertVectors(
    indexName: string,
    vectors: Array<{
      id: string
      values: number[]
      metadata?: any
    }>
  ): Promise<void>
  
  deleteVectors(indexName: string, ids: string[]): Promise<void>
  
  // 搜索
  search(
    indexName: string,
    queryVector: number[],
    topK: number,
    filter?: any
  ): Promise<VectorSearchResult[]>
  
  // 统计
  getIndexStats(indexName: string): Promise<{
    totalVectors: number
    dimension: number
    indexSize: number
  }>
  
  isAvailable(): boolean
}

export interface DocumentProcessor {
  name: string
  supportedTypes: string[]
  
  extractText(filePath: string, fileType: string): Promise<string>
  chunkText(
    text: string,
    options: {
      chunkSize: number
      chunkOverlap: number
      preserveStructure?: boolean
    }
  ): DocumentChunk[]
}

export interface RAGConfig {
  chunkSize: number
  chunkOverlap: number
  embeddingModel: string
  vectorStore: {
    provider: string
    indexName: string
    dimension: number
  }
  retrieval: {
    topK: number
    scoreThreshold: number
    rerankEnabled: boolean
  }
}

export interface RAGSearchOptions {
  topK?: number
  scoreThreshold?: number
  filter?: any
  rerank?: boolean
}

export interface RAGSearchResult {
  chunks: VectorSearchResult[]
  context: string
  sources: Array<{
    documentId: string
    documentName: string
    relevanceScore: number
  }>
  totalResults: number
}

export interface DocumentUploadResult {
  documentId: string
  chunks: number
  tokens: number
  processingTime: number
  status: 'success' | 'failed'
  error?: string
}

// 错误类型
export class RAGError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message)
    this.name = 'RAGError'
  }
}

export class VectorStoreError extends RAGError {
  constructor(message: string, public provider: string, details?: any) {
    super(message, 'VECTOR_STORE_ERROR', details)
  }
}

export class DocumentProcessingError extends RAGError {
  constructor(message: string, public fileType: string, details?: any) {
    super(message, 'DOCUMENT_PROCESSING_ERROR', details)
  }
}

export class EmbeddingError extends RAGError {
  constructor(message: string, details?: any) {
    super(message, 'EMBEDDING_ERROR', details)
  }
}
