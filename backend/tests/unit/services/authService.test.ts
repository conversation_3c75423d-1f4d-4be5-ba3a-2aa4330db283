import { AuthService } from '@/services/authService'
import { User } from '@/models/User'
import { CryptoUtils } from '@/utils/crypto'

// Mock外部依赖
jest.mock('@/utils/email', () => ({
  EmailService: {
    sendVerificationEmail: jest.fn(),
    sendPasswordResetEmail: jest.fn(),
  },
}))

describe('AuthService', () => {
  describe('用户注册', () => {
    it('应该成功注册新用户', async () => {
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
      }

      const result = await AuthService.register(userData)

      expect(result.user).toBeDefined()
      expect(result.user.username).toBe('newuser')
      expect(result.user.email).toBe('<EMAIL>')
      expect(result.token).toBeDefined()
      expect(typeof result.token).toBe('string')
    })

    it('应该拒绝重复的用户名', async () => {
      await global.testUtils.createTestUser()

      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
      }

      await expect(AuthService.register(userData)).rejects.toThrow('用户名已存在')
    })

    it('应该拒绝重复的邮箱', async () => {
      await global.testUtils.createTestUser()

      const userData = {
        username: 'differentuser',
        email: '<EMAIL>',
        password: 'password123',
      }

      await expect(AuthService.register(userData)).rejects.toThrow('邮箱已被注册')
    })

    it('应该验证密码强度', async () => {
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: '123', // 弱密码
      }

      await expect(AuthService.register(userData)).rejects.toThrow('密码强度不足')
    })
  })

  describe('用户登录', () => {
    let user: any

    beforeEach(async () => {
      user = await User.create({
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: await CryptoUtils.hashPassword('password123'),
        isEmailVerified: true,
      })
    })

    it('应该使用邮箱成功登录', async () => {
      const result = await AuthService.login({
        email: '<EMAIL>',
        password: 'password123',
      })

      expect(result.user).toBeDefined()
      expect(result.user.email).toBe('<EMAIL>')
      expect(result.token).toBeDefined()
    })

    it('应该使用用户名成功登录', async () => {
      const result = await AuthService.login({
        username: 'testuser',
        password: 'password123',
      })

      expect(result.user).toBeDefined()
      expect(result.user.username).toBe('testuser')
      expect(result.token).toBeDefined()
    })

    it('应该拒绝错误的密码', async () => {
      await expect(
        AuthService.login({
          email: '<EMAIL>',
          password: 'wrongpassword',
        })
      ).rejects.toThrow('邮箱或密码错误')
    })

    it('应该拒绝不存在的用户', async () => {
      await expect(
        AuthService.login({
          email: '<EMAIL>',
          password: 'password123',
        })
      ).rejects.toThrow('邮箱或密码错误')
    })

    it('应该拒绝未验证邮箱的用户', async () => {
      user.isEmailVerified = false
      await user.save()

      await expect(
        AuthService.login({
          email: '<EMAIL>',
          password: 'password123',
        })
      ).rejects.toThrow('请先验证邮箱')
    })

    it('应该更新最后登录时间', async () => {
      const originalLastLogin = user.lastLoginAt

      await AuthService.login({
        email: '<EMAIL>',
        password: 'password123',
      })

      const updatedUser = await User.findById(user._id)
      expect(updatedUser?.lastLoginAt).not.toEqual(originalLastLogin)
    })
  })

  describe('邮箱验证', () => {
    let user: any

    beforeEach(async () => {
      user = await global.testUtils.createTestUser()
    })

    it('应该发送验证邮件', async () => {
      await AuthService.sendEmailVerification(user._id.toString())

      const updatedUser = await User.findById(user._id)
      expect(updatedUser?.emailVerificationToken).toBeDefined()
      expect(updatedUser?.emailVerificationExpires).toBeDefined()
    })

    it('应该验证邮箱令牌', async () => {
      const token = user.generateEmailVerificationToken()
      await user.save()

      await AuthService.verifyEmail(token)

      const updatedUser = await User.findById(user._id)
      expect(updatedUser?.isEmailVerified).toBe(true)
      expect(updatedUser?.emailVerificationToken).toBeUndefined()
      expect(updatedUser?.emailVerificationExpires).toBeUndefined()
    })

    it('应该拒绝无效的验证令牌', async () => {
      await expect(AuthService.verifyEmail('invalid-token')).rejects.toThrow(
        '无效或已过期的验证令牌'
      )
    })
  })

  describe('密码重置', () => {
    let user: any

    beforeEach(async () => {
      user = await User.create({
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: await CryptoUtils.hashPassword('password123'),
        isEmailVerified: true,
      })
    })

    it('应该发送密码重置邮件', async () => {
      await AuthService.sendPasswordReset('<EMAIL>')

      const updatedUser = await User.findById(user._id)
      expect(updatedUser?.passwordResetToken).toBeDefined()
      expect(updatedUser?.passwordResetExpires).toBeDefined()
    })

    it('应该重置密码', async () => {
      const token = user.generatePasswordResetToken()
      await user.save()

      await AuthService.resetPassword(token, 'newpassword123')

      const updatedUser = await User.findById(user._id)
      const isValidPassword = await updatedUser?.validatePassword('newpassword123')
      expect(isValidPassword).toBe(true)
      expect(updatedUser?.passwordResetToken).toBeUndefined()
      expect(updatedUser?.passwordResetExpires).toBeUndefined()
    })

    it('应该拒绝无效的重置令牌', async () => {
      await expect(
        AuthService.resetPassword('invalid-token', 'newpassword123')
      ).rejects.toThrow('无效或已过期的重置令牌')
    })
  })

  describe('令牌管理', () => {
    let user: any

    beforeEach(async () => {
      user = await global.testUtils.createTestUser()
    })

    it('应该验证有效的JWT令牌', async () => {
      const token = global.testUtils.generateJWT(user._id.toString())
      const decoded = await AuthService.verifyToken(token)

      expect(decoded.userId).toBe(user._id.toString())
    })

    it('应该拒绝无效的JWT令牌', async () => {
      await expect(AuthService.verifyToken('invalid-token')).rejects.toThrow()
    })

    it('应该刷新JWT令牌', async () => {
      const oldToken = global.testUtils.generateJWT(user._id.toString())
      const result = await AuthService.refreshToken(oldToken)

      expect(result.token).toBeDefined()
      expect(result.token).not.toBe(oldToken)
      expect(result.user).toBeDefined()
    })
  })
})
