import { User } from '@/models/User'
import { CryptoUtils } from '@/utils/crypto'

describe('User Model', () => {
  describe('用户创建', () => {
    it('应该成功创建用户', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: await CryptoUtils.hashPassword('password123'),
      }

      const user = new User(userData)
      await user.save()

      expect(user._id).toBeDefined()
      expect(user.username).toBe('testuser')
      expect(user.email).toBe('<EMAIL>')
      expect(user.isEmailVerified).toBe(false)
      expect(user.subscription.plan).toBe('free')
    })

    it('应该验证必填字段', async () => {
      const user = new User({})

      await expect(user.save()).rejects.toThrow()
    })

    it('应该验证邮箱格式', async () => {
      const user = new User({
        username: 'testuser',
        email: 'invalid-email',
        passwordHash: 'hashedpassword',
      })

      await expect(user.save()).rejects.toThrow()
    })

    it('应该确保用户名唯一', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: 'hashedpassword',
      }

      await User.create(userData)

      const duplicateUser = new User({
        ...userData,
        email: '<EMAIL>',
      })

      await expect(duplicateUser.save()).rejects.toThrow()
    })

    it('应该确保邮箱唯一', async () => {
      const userData = {
        username: 'testuser1',
        email: '<EMAIL>',
        passwordHash: 'hashedpassword',
      }

      await User.create(userData)

      const duplicateUser = new User({
        username: 'testuser2',
        email: '<EMAIL>',
        passwordHash: 'hashedpassword',
      })

      await expect(duplicateUser.save()).rejects.toThrow()
    })
  })

  describe('用户方法', () => {
    let user: any

    beforeEach(async () => {
      user = await global.testUtils.createTestUser()
    })

    it('应该验证密码', async () => {
      const isValid = await user.validatePassword('password123')
      expect(isValid).toBe(true)

      const isInvalid = await user.validatePassword('wrongpassword')
      expect(isInvalid).toBe(false)
    })

    it('应该生成邮箱验证令牌', () => {
      const token = user.generateEmailVerificationToken()
      expect(token).toBeDefined()
      expect(typeof token).toBe('string')
      expect(user.emailVerificationToken).toBe(token)
      expect(user.emailVerificationExpires).toBeDefined()
    })

    it('应该验证邮箱验证令牌', () => {
      const token = user.generateEmailVerificationToken()
      const isValid = user.validateEmailVerificationToken(token)
      expect(isValid).toBe(true)

      const isInvalid = user.validateEmailVerificationToken('invalid-token')
      expect(isInvalid).toBe(false)
    })

    it('应该生成密码重置令牌', () => {
      const token = user.generatePasswordResetToken()
      expect(token).toBeDefined()
      expect(typeof token).toBe('string')
      expect(user.passwordResetToken).toBeDefined()
      expect(user.passwordResetExpires).toBeDefined()
    })

    it('应该验证密码重置令牌', () => {
      const token = user.generatePasswordResetToken()
      const isValid = user.validatePasswordResetToken(token)
      expect(isValid).toBe(true)

      const isInvalid = user.validatePasswordResetToken('invalid-token')
      expect(isInvalid).toBe(false)
    })

    it('应该更新最后登录时间', async () => {
      const originalLastLogin = user.lastLoginAt
      await user.updateLastLogin()
      
      expect(user.lastLoginAt).not.toEqual(originalLastLogin)
      expect(user.lastLoginAt).toBeInstanceOf(Date)
    })

    it('应该检查订阅状态', () => {
      expect(user.isSubscriptionActive()).toBe(true)

      user.subscription.status = 'cancelled'
      expect(user.isSubscriptionActive()).toBe(false)
    })

    it('应该检查token使用限制', () => {
      user.subscription.tokensUsed = 500
      user.subscription.tokensLimit = 1000
      expect(user.canUseTokens(400)).toBe(true)
      expect(user.canUseTokens(600)).toBe(false)
    })
  })

  describe('用户查询', () => {
    beforeEach(async () => {
      await User.create([
        {
          username: 'user1',
          email: '<EMAIL>',
          passwordHash: 'hash1',
          isEmailVerified: true,
        },
        {
          username: 'user2',
          email: '<EMAIL>',
          passwordHash: 'hash2',
          isEmailVerified: false,
        },
      ])
    })

    it('应该按邮箱查找用户', async () => {
      const user = await User.findByEmail('<EMAIL>')
      expect(user).toBeDefined()
      expect(user?.username).toBe('user1')
    })

    it('应该按用户名查找用户', async () => {
      const user = await User.findByUsername('user2')
      expect(user).toBeDefined()
      expect(user?.email).toBe('<EMAIL>')
    })

    it('应该查找已验证邮箱的用户', async () => {
      const users = await User.findVerifiedUsers()
      expect(users).toHaveLength(1)
      expect(users[0].username).toBe('user1')
    })
  })
})
