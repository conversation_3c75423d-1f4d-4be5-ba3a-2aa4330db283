import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { KnowledgeApiClient } from '@/services/knowledgeApiClient'
import { KnowledgeFallbackService } from '@/services/knowledgeFallbackService'
import { EnhancedKnowledgeService } from '@/services/enhancedKnowledgeService'
import {
  KnowledgeApiConfig,
  FallbackConfig,
  KnowledgeApiRequest,
  KnowledgeApiResponse,
} from '@/types'

// 模拟配置
const mockApiConfig: KnowledgeApiConfig = {
  enabled: true,
  baseUrl: 'https://test-api.example.com',
  apiKey: 'test-api-key',
  apiSecret: 'test-api-secret',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
}

const mockFallbackConfig: FallbackConfig = {
  enabled: true,
  strategy: 'ai_model',
  aiModelConfig: {
    provider: 'openai',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 1000,
    systemPrompt: '你是一个智能助手',
  },
  cacheTimeout: 86400,
}

describe('知识库 API 客户端测试', () => {
  let apiClient: KnowledgeApiClient

  beforeEach(() => {
    apiClient = new KnowledgeApiClient(mockApiConfig)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  test('应该正确初始化 API 客户端', () => {
    expect(apiClient).toBeDefined()
    expect(apiClient.isConfigValid()).toBe(true)
  })

  test('应该正确生成缓存键', () => {
    const request: KnowledgeApiRequest = {
      query: '测试查询',
      knowledgeBaseId: 'kb_123',
      topK: 5,
      scoreThreshold: 0.7,
    }

    // 通过反射访问私有方法进行测试
    const cacheKey = (apiClient as any).generateCacheKey(request)
    expect(cacheKey).toMatch(/^knowledge_api:[a-f0-9]{32}$/)
  })

  test('应该正确判断可重试错误', () => {
    const networkError = { code: 'ECONNREFUSED' }
    const timeoutError = { code: 'ETIMEDOUT' }
    const serverError = { response: { status: 500 } }
    const clientError = { response: { status: 400 } }

    expect((apiClient as any).isRetryableError(networkError)).toBe(true)
    expect((apiClient as any).isRetryableError(timeoutError)).toBe(true)
    expect((apiClient as any).isRetryableError(serverError)).toBe(true)
    expect((apiClient as any).isRetryableError(clientError)).toBe(false)
  })

  test('应该正确验证配置', () => {
    const validConfig = { ...mockApiConfig }
    const invalidConfig = { ...mockApiConfig, apiKey: '' }

    const validClient = new KnowledgeApiClient(validConfig)
    const invalidClient = new KnowledgeApiClient(invalidConfig)

    expect(validClient.isConfigValid()).toBe(true)
    expect(invalidClient.isConfigValid()).toBe(false)
  })
})

describe('知识库降级策略服务测试', () => {
  let fallbackService: KnowledgeFallbackService

  beforeEach(() => {
    fallbackService = new KnowledgeFallbackService(mockFallbackConfig)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  test('应该正确初始化降级服务', () => {
    expect(fallbackService).toBeDefined()
    expect(fallbackService.isConfigValid()).toBe(true)
  })

  test('应该正确判断是否需要降级', () => {
    const networkError = new Error('Network error')
    const apiError = new Error('API unavailable')
    const validationError = new Error('Invalid input')

    expect(fallbackService.shouldFallback(networkError)).toBe(true)
    expect(fallbackService.shouldFallback(apiError)).toBe(true)
    expect(fallbackService.shouldFallback(validationError)).toBe(false)
  })

  test('应该正确生成降级缓存键', () => {
    const request: KnowledgeApiRequest = {
      query: '测试查询',
      knowledgeBaseId: 'kb_123',
    }

    const cacheKey = (fallbackService as any).generateFallbackCacheKey(request)
    expect(cacheKey).toMatch(/^fallback_cache:[a-f0-9]{32}$/)
  })

  test('应该正确构建系统提示词', () => {
    const request: KnowledgeApiRequest = {
      query: '什么是人工智能？',
    }

    const systemPrompt = (fallbackService as any).buildSystemPrompt(request)
    expect(systemPrompt).toContain('智能助手')
    expect(systemPrompt).toContain('什么是人工智能？')
  })

  test('应该正确返回空响应', () => {
    const request: KnowledgeApiRequest = {
      query: '测试查询',
    }

    const response = (fallbackService as any).fallbackToEmptyResponse(request)
    expect(response.source).toBe('empty')
    expect(response.confidence).toBe(0.1)
    expect(response.content).toContain('知识库服务暂时不可用')
  })

  test('应该正确获取降级统计', () => {
    const stats = fallbackService.getFallbackStats()
    expect(stats).toHaveProperty('count')
    expect(stats).toHaveProperty('lastTime')
    expect(stats).toHaveProperty('strategy')
    expect(stats).toHaveProperty('enabled')
  })

  test('应该正确重置降级统计', () => {
    // 先执行一次降级以产生统计数据
    const request: KnowledgeApiRequest = { query: '测试' }
    const error = new Error('测试错误')
    
    // 模拟降级执行
    fallbackService['fallbackCount'] = 5
    fallbackService['lastFallbackTime'] = Date.now()

    let stats = fallbackService.getFallbackStats()
    expect(stats.count).toBe(5)

    // 重置统计
    fallbackService.resetFallbackStats()
    stats = fallbackService.getFallbackStats()
    expect(stats.count).toBe(0)
    expect(stats.lastTime).toBe(0)
  })
})

describe('增强知识库服务集成测试', () => {
  let enhancedService: EnhancedKnowledgeService

  beforeEach(() => {
    // 模拟配置管理器
    jest.mock('@/config/knowledgeApi', () => ({
      knowledgeApiConfig: {
        getApiConfig: () => mockApiConfig,
        getFallbackConfig: () => mockFallbackConfig,
        isApiEnabled: () => true,
        isFallbackEnabled: () => true,
        getConfigSummary: () => ({
          api: {
            enabled: true,
            baseUrl: mockApiConfig.baseUrl,
            hasApiKey: true,
            timeout: mockApiConfig.timeout,
          },
          fallback: {
            enabled: true,
            strategy: mockFallbackConfig.strategy,
            hasAiConfig: true,
          },
        }),
        reloadConfig: jest.fn(),
      },
    }))

    enhancedService = new EnhancedKnowledgeService()
  })

  afterEach(() => {
    jest.clearAllMocks()
    jest.resetModules()
  })

  test('应该正确初始化增强服务', () => {
    expect(enhancedService).toBeDefined()
  })

  test('应该正确获取服务状态', () => {
    const status = enhancedService.getServiceStatus()
    expect(status).toHaveProperty('initialized')
    expect(status).toHaveProperty('apiEnabled')
    expect(status).toHaveProperty('apiHealthy')
    expect(status).toHaveProperty('fallbackEnabled')
    expect(status).toHaveProperty('fallbackStats')
    expect(status).toHaveProperty('config')
  })

  test('应该正确计算置信度', () => {
    const results = [
      { score: 0.9 },
      { score: 0.8 },
      { score: 0.7 },
    ]

    const confidence = (enhancedService as any).calculateConfidence(results)
    expect(confidence).toBeCloseTo(0.8, 1)
  })

  test('空结果应该返回零置信度', () => {
    const confidence = (enhancedService as any).calculateConfidence([])
    expect(confidence).toBe(0)
  })

  test('应该正确验证 API 配置', () => {
    const validConfig = {
      baseUrl: 'https://api.example.com',
      apiKey: 'test-key',
      timeout: 30000,
    }

    const invalidConfig = {
      baseUrl: '',
      apiKey: 'test-key',
      timeout: 30000,
    }

    expect((enhancedService as any).isApiConfigValid(validConfig)).toBe(true)
    expect((enhancedService as any).isApiConfigValid(invalidConfig)).toBe(false)
  })
})

describe('配置验证测试', () => {
  test('应该正确验证知识库 API 配置', () => {
    const validConfig: KnowledgeApiConfig = {
      enabled: true,
      baseUrl: 'https://api.example.com',
      apiKey: 'test-key',
      apiSecret: 'test-secret',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    }

    const invalidConfigs = [
      { ...validConfig, baseUrl: '' },
      { ...validConfig, apiKey: '' },
      { ...validConfig, timeout: 0 },
      { ...validConfig, retryAttempts: -1 },
    ]

    const client = new KnowledgeApiClient(validConfig)
    expect(client.isConfigValid()).toBe(true)

    invalidConfigs.forEach(config => {
      const invalidClient = new KnowledgeApiClient(config)
      expect(invalidClient.isConfigValid()).toBe(false)
    })
  })

  test('应该正确验证降级策略配置', () => {
    const validConfigs = [
      { ...mockFallbackConfig, strategy: 'ai_model' as const },
      { ...mockFallbackConfig, strategy: 'cached_response' as const },
      { ...mockFallbackConfig, strategy: 'empty_response' as const },
    ]

    const invalidConfig = {
      ...mockFallbackConfig,
      strategy: 'ai_model' as const,
      aiModelConfig: undefined,
    }

    validConfigs.forEach(config => {
      const service = new KnowledgeFallbackService(config)
      expect(service.isConfigValid()).toBe(true)
    })

    const invalidService = new KnowledgeFallbackService(invalidConfig)
    expect(invalidService.isConfigValid()).toBe(false)
  })
})
