import { MongoMemoryServer } from 'mongodb-memory-server'
import mongoose from 'mongoose'
import Redis from 'ioredis'

// 全局测试设置
let mongoServer: MongoMemoryServer
let redisClient: Redis

beforeAll(async () => {
  // 设置测试环境变量
  process.env.NODE_ENV = 'test'
  process.env.JWT_SECRET = 'test-jwt-secret'
  process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters'
  
  // 启动内存MongoDB
  mongoServer = await MongoMemoryServer.create()
  const mongoUri = mongoServer.getUri()
  
  await mongoose.connect(mongoUri)
  
  // 设置Redis测试客户端
  redisClient = new Redis({
    host: 'localhost',
    port: 6379,
    db: 15, // 使用测试数据库
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 1,
  })
})

afterAll(async () => {
  // 清理数据库连接
  await mongoose.disconnect()
  await mongoServer.stop()
  
  // 清理Redis连接
  await redisClient.quit()
})

beforeEach(async () => {
  // 清理数据库
  const collections = mongoose.connection.collections
  for (const key in collections) {
    const collection = collections[key]
    await collection.deleteMany({})
  }
  
  // 清理Redis
  await redisClient.flushdb()
})

// 全局测试工具
global.testUtils = {
  createTestUser: async () => {
    const { User } = require('../src/models/User')
    return await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword',
      isEmailVerified: true,
    })
  },
  
  createTestCharacter: async (userId: string) => {
    const { Character } = require('../src/models/Character')
    return await Character.create({
      name: 'Test Character',
      description: 'A test character',
      userId,
      config: {
        personality: 'friendly',
        expertise: ['general'],
        language: 'zh-CN',
        aiModel: {
          provider: 'openai',
          model: 'gpt-3.5-turbo',
          temperature: 0.7,
          maxTokens: 2000,
        },
      },
    })
  },
  
  generateJWT: (userId: string) => {
    const jwt = require('jsonwebtoken')
    return jwt.sign({ userId }, process.env.JWT_SECRET, { expiresIn: '1h' })
  },
}

// 类型声明
declare global {
  var testUtils: {
    createTestUser: () => Promise<any>
    createTestCharacter: (userId: string) => Promise<any>
    generateJWT: (userId: string) => string
  }
}
