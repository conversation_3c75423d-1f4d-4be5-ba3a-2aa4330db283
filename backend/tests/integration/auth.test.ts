import request from 'supertest'
import { app } from '@/app'
import { User } from '@/models/User'
import { CryptoUtils } from '@/utils/crypto'

describe('认证接口集成测试', () => {
  describe('POST /api/auth/register', () => {
    it('应该成功注册新用户', async () => {
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toBeDefined()
      expect(response.body.data.user.username).toBe('newuser')
      expect(response.body.data.user.email).toBe('<EMAIL>')
      expect(response.body.data.token).toBeDefined()

      // 验证用户已保存到数据库
      const savedUser = await User.findOne({ email: '<EMAIL>' })
      expect(savedUser).toBeDefined()
    })

    it('应该拒绝无效的输入数据', async () => {
      const invalidData = {
        username: 'a', // 太短
        email: 'invalid-email',
        password: '123', // 太短
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBeDefined()
    })

    it('应该拒绝重复的邮箱', async () => {
      await global.testUtils.createTestUser()

      const userData = {
        username: 'newuser',
        email: '<EMAIL>', // 已存在的邮箱
        password: 'password123',
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.message).toContain('邮箱已被注册')
    })
  })

  describe('POST /api/auth/login', () => {
    let user: any

    beforeEach(async () => {
      user = await User.create({
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: await CryptoUtils.hashPassword('password123'),
        isEmailVerified: true,
      })
    })

    it('应该使用邮箱成功登录', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      }

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toBeDefined()
      expect(response.body.data.user.email).toBe('<EMAIL>')
      expect(response.body.data.token).toBeDefined()
    })

    it('应该使用用户名成功登录', async () => {
      const loginData = {
        username: 'testuser',
        password: 'password123',
      }

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.user.username).toBe('testuser')
    })

    it('应该拒绝错误的密码', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      }

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.error.message).toContain('邮箱或密码错误')
    })

    it('应该拒绝未验证邮箱的用户', async () => {
      user.isEmailVerified = false
      await user.save()

      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      }

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.error.message).toContain('请先验证邮箱')
    })
  })

  describe('GET /api/auth/profile', () => {
    let user: any
    let token: string

    beforeEach(async () => {
      user = await global.testUtils.createTestUser()
      token = global.testUtils.generateJWT(user._id.toString())
    })

    it('应该返回用户信息', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.username).toBe('testuser')
      expect(response.body.data.email).toBe('<EMAIL>')
      expect(response.body.data.passwordHash).toBeUndefined() // 不应返回密码
    })

    it('应该拒绝无效的令牌', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401)

      expect(response.body.success).toBe(false)
    })

    it('应该拒绝缺少令牌的请求', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .expect(401)

      expect(response.body.success).toBe(false)
    })
  })

  describe('PUT /api/auth/profile', () => {
    let user: any
    let token: string

    beforeEach(async () => {
      user = await global.testUtils.createTestUser()
      token = global.testUtils.generateJWT(user._id.toString())
    })

    it('应该更新用户信息', async () => {
      const updateData = {
        username: 'updateduser',
        avatar: 'https://example.com/avatar.jpg',
      }

      const response = await request(app)
        .put('/api/auth/profile')
        .set('Authorization', `Bearer ${token}`)
        .send(updateData)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.username).toBe('updateduser')
      expect(response.body.data.avatar).toBe('https://example.com/avatar.jpg')

      // 验证数据库中的更新
      const updatedUser = await User.findById(user._id)
      expect(updatedUser?.username).toBe('updateduser')
    })

    it('应该拒绝无效的更新数据', async () => {
      const updateData = {
        email: 'invalid-email', // 无效邮箱格式
      }

      const response = await request(app)
        .put('/api/auth/profile')
        .set('Authorization', `Bearer ${token}`)
        .send(updateData)
        .expect(400)

      expect(response.body.success).toBe(false)
    })
  })

  describe('POST /api/auth/change-password', () => {
    let user: any
    let token: string

    beforeEach(async () => {
      user = await User.create({
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: await CryptoUtils.hashPassword('password123'),
        isEmailVerified: true,
      })
      token = global.testUtils.generateJWT(user._id.toString())
    })

    it('应该成功修改密码', async () => {
      const changeData = {
        currentPassword: 'password123',
        newPassword: 'newpassword123',
      }

      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send(changeData)
        .expect(200)

      expect(response.body.success).toBe(true)

      // 验证新密码
      const updatedUser = await User.findById(user._id)
      const isValidPassword = await updatedUser?.validatePassword('newpassword123')
      expect(isValidPassword).toBe(true)
    })

    it('应该拒绝错误的当前密码', async () => {
      const changeData = {
        currentPassword: 'wrongpassword',
        newPassword: 'newpassword123',
      }

      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send(changeData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.message).toContain('当前密码错误')
    })
  })

  describe('POST /api/auth/logout', () => {
    let user: any
    let token: string

    beforeEach(async () => {
      user = await global.testUtils.createTestUser()
      token = global.testUtils.generateJWT(user._id.toString())
    })

    it('应该成功登出', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${token}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toContain('登出成功')
    })
  })
})
