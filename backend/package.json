{"name": "ai-digital-robots-backend", "version": "1.0.0", "description": "AI数字人定制应用系统 - 后端API服务", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "db:seed": "ts-node src/scripts/seed.ts", "db:migrate": "ts-node src/scripts/migrate.ts"}, "keywords": ["ai", "digital-human", "chatbot", "rag", "llm", "nodejs", "express", "mongodb", "typescript"], "author": "AI Digital Robots Team", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.9.1", "@pinecone-database/pinecone": "^1.1.2", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.1", "express-slow-down": "^2.0.1", "express-validator": "^7.2.1", "helmet": "^7.2.0", "hpp": "^0.2.3", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "langchain": "^0.0.208", "mammoth": "^1.6.0", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "openai": "^4.20.1", "pdf-parse": "^1.1.1", "redis": "^4.6.10", "socket.io": "^4.7.4", "tsconfig-paths": "^4.2.0", "winston": "^3.11.0", "xss": "^1.0.14"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/hpp": "^0.2.5", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/server.ts", "!src/scripts/**"]}}