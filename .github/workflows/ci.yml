name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  lint-and-format:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 设置Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装后端依赖
        working-directory: ./backend
        run: npm ci

      - name: 安装前端依赖
        working-directory: ./frontend
        run: npm ci

      - name: 后端代码检查
        working-directory: ./backend
        run: |
          npm run lint
          npm run type-check

      - name: 前端代码检查
        working-directory: ./frontend
        run: |
          npm run lint
          npm run type-check

  # 后端测试
  backend-tests:
    name: 后端测试
    runs-on: ubuntu-latest
    needs: lint-and-format

    services:
      mongodb:
        image: mongo:7.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: password
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7.2-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 设置Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        working-directory: ./backend
        run: npm ci

      - name: 运行单元测试
        working-directory: ./backend
        env:
          NODE_ENV: test
          MONGODB_URI: **************************************************************
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-jwt-secret
          ENCRYPTION_KEY: test-encryption-key-32-characters
        run: npm run test:coverage

      - name: 上传测试覆盖率
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage/lcov.info
          flags: backend
          name: backend-coverage

  # 前端测试
  frontend-tests:
    name: 前端测试
    runs-on: ubuntu-latest
    needs: lint-and-format

    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 设置Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        working-directory: ./frontend
        run: npm ci

      - name: 运行单元测试
        working-directory: ./frontend
        run: npm run test:coverage

      - name: 上传测试覆盖率
        uses: codecov/codecov-action@v3
        with:
          file: ./frontend/coverage/lcov.info
          flags: frontend
          name: frontend-coverage

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: lint-and-format

    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 运行Trivy漏洞扫描
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 上传扫描结果到GitHub Security
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: 依赖漏洞扫描
        run: |
          cd backend && npm audit --audit-level moderate
          cd ../frontend && npm audit --audit-level moderate

  # 构建Docker镜像
  build-images:
    name: 构建Docker镜像
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, security-scan]
    if: github.event_name == 'push'

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        id: meta-backend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-

      - name: 构建并推送后端镜像
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          push: true
          tags: ${{ steps.meta-backend.outputs.tags }}
          labels: ${{ steps.meta-backend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 提取前端元数据
        id: meta-frontend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-

      - name: 构建并推送前端镜像
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          push: true
          tags: ${{ steps.meta-frontend.outputs.tags }}
          labels: ${{ steps.meta-frontend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # 部署到测试环境
  deploy-staging:
    name: 部署到测试环境
    runs-on: ubuntu-latest
    needs: build-images
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 部署到测试环境
        run: |
          echo "部署到测试环境..."
          # 这里添加实际的部署脚本

  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: build-images
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 部署到生产环境
        run: |
          echo "部署到生产环境..."
          # 这里添加实际的部署脚本

  # 性能测试
  performance-test:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/develop'

    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 安装k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: 运行性能测试
        run: |
          k6 run tests/performance/load-test.js
        env:
          BASE_URL: ${{ secrets.STAGING_URL }}

  # 通知
  notify:
    name: 通知
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()

    steps:
      - name: 发送通知
        run: |
          echo "CI/CD流水线完成"
          # 这里可以添加Slack、邮件等通知
