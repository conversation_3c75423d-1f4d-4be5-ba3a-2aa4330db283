groups:
  - name: ai-chat-alerts
    rules:
      # 应用健康检查
      - alert: ApplicationDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "应用服务下线"
          description: "{{ $labels.job }} 服务已下线超过1分钟"

      # 高错误率
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "高错误率告警"
          description: "{{ $labels.job }} 5xx错误率超过10%"

      # 响应时间过长
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "响应时间过长"
          description: "{{ $labels.job }} 95%响应时间超过1秒"

      # CPU使用率过高
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "{{ $labels.instance }} CPU使用率超过80%"

      # 内存使用率过高
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "{{ $labels.instance }} 内存使用率超过85%"

      # 磁盘空间不足
      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足"
          description: "{{ $labels.instance }} 磁盘使用率超过90%"

      # MongoDB连接数过高
      - alert: MongoDBHighConnections
        expr: mongodb_connections{state="current"} > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "MongoDB连接数过高"
          description: "MongoDB当前连接数: {{ $value }}"

      # Redis内存使用率过高
      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率: {{ $value }}%"

      # AI API调用失败率过高
      - alert: AIAPIHighFailureRate
        expr: rate(ai_api_requests_total{status="error"}[5m]) / rate(ai_api_requests_total[5m]) > 0.1
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "AI API调用失败率过高"
          description: "AI API失败率: {{ $value | humanizePercentage }}"

      # Token使用量告警
      - alert: HighTokenUsage
        expr: sum(rate(ai_tokens_used_total[1h])) > 10000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Token使用量过高"
          description: "每小时Token使用量: {{ $value }}"

      # 对话响应时间过长
      - alert: SlowConversationResponse
        expr: histogram_quantile(0.95, rate(conversation_response_duration_seconds_bucket[5m])) > 5
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "对话响应时间过长"
          description: "95%对话响应时间超过5秒"

      # WebSocket连接数异常
      - alert: WebSocketConnectionsAbnormal
        expr: websocket_connections_active < 1 or websocket_connections_active > 1000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "WebSocket连接数异常"
          description: "当前WebSocket连接数: {{ $value }}"
