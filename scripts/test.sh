#!/bin/bash

# AI数字人对话系统测试脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查依赖
check_dependencies() {
    log_info "检查测试依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 设置测试环境
setup_test_env() {
    log_info "设置测试环境..."
    
    # 设置环境变量
    export NODE_ENV=test
    export LOG_LEVEL=error
    export JWT_SECRET=test-jwt-secret
    export ENCRYPTION_KEY=test-encryption-key-32-characters
    export MONGODB_URI=mongodb://localhost:27017/ai_chat_test
    export REDIS_URL=redis://localhost:6379/15
    
    # 禁用外部服务调用
    export OPENAI_API_KEY=test-key
    export ANTHROPIC_API_KEY=test-key
    export PINECONE_API_KEY=test-key
    
    log_success "测试环境设置完成"
}

# 启动测试数据库
start_test_db() {
    log_info "启动测试数据库..."
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker 未运行，请启动Docker"
        exit 1
    fi
    
    # 启动测试用的MongoDB和Redis
    docker-compose -f docker-compose.test.yml up -d mongodb redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    log_success "测试数据库启动完成"
}

# 停止测试数据库
stop_test_db() {
    log_info "停止测试数据库..."
    docker-compose -f docker-compose.test.yml down
    log_success "测试数据库已停止"
}

# 运行后端测试
run_backend_tests() {
    log_info "运行后端测试..."
    
    cd backend
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装后端依赖..."
        npm ci
    fi
    
    # 运行代码检查
    log_info "运行代码检查..."
    npm run lint
    npm run type-check
    
    # 运行单元测试
    log_info "运行单元测试..."
    npm run test
    
    # 运行测试覆盖率
    log_info "运行测试覆盖率..."
    npm run test:coverage
    
    cd ..
    log_success "后端测试完成"
}

# 运行前端测试
run_frontend_tests() {
    log_info "运行前端测试..."
    
    cd frontend
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm ci
    fi
    
    # 运行代码检查
    log_info "运行代码检查..."
    npm run lint
    npm run type-check
    
    # 运行单元测试
    log_info "运行单元测试..."
    npm run test
    
    # 运行测试覆盖率
    log_info "运行测试覆盖率..."
    npm run test:coverage
    
    cd ..
    log_success "前端测试完成"
}

# 运行集成测试
run_integration_tests() {
    log_info "运行集成测试..."
    
    # 启动应用
    log_info "启动应用进行集成测试..."
    docker-compose -f docker-compose.test.yml up -d
    
    # 等待应用启动
    log_info "等待应用启动..."
    sleep 30
    
    # 健康检查
    log_info "执行健康检查..."
    if curl -f http://localhost:3001/health &> /dev/null; then
        log_success "应用健康检查通过"
    else
        log_error "应用健康检查失败"
        docker-compose -f docker-compose.test.yml logs
        exit 1
    fi
    
    # 运行API测试
    log_info "运行API测试..."
    cd backend
    npm run test:integration
    cd ..
    
    # 停止应用
    docker-compose -f docker-compose.test.yml down
    
    log_success "集成测试完成"
}

# 运行性能测试
run_performance_tests() {
    log_info "运行性能测试..."
    
    # 检查k6是否安装
    if ! command -v k6 &> /dev/null; then
        log_warning "k6 未安装，跳过性能测试"
        return
    fi
    
    # 启动应用
    docker-compose up -d
    
    # 等待应用启动
    sleep 30
    
    # 运行性能测试
    k6 run tests/performance/load-test.js
    
    # 停止应用
    docker-compose down
    
    log_success "性能测试完成"
}

# 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    # 创建报告目录
    mkdir -p reports
    
    # 合并覆盖率报告
    if [ -d "backend/coverage" ] && [ -d "frontend/coverage" ]; then
        log_info "合并测试覆盖率报告..."
        # 这里可以使用工具合并前后端覆盖率报告
    fi
    
    # 生成HTML报告
    if [ -d "backend/coverage" ]; then
        cp -r backend/coverage reports/backend-coverage
    fi
    
    if [ -d "frontend/coverage" ]; then
        cp -r frontend/coverage reports/frontend-coverage
    fi
    
    log_success "测试报告生成完成，查看 reports/ 目录"
}

# 清理测试环境
cleanup() {
    log_info "清理测试环境..."
    
    # 停止所有容器
    docker-compose -f docker-compose.test.yml down &> /dev/null || true
    docker-compose down &> /dev/null || true
    
    # 清理测试数据
    docker volume prune -f &> /dev/null || true
    
    log_success "测试环境清理完成"
}

# 显示帮助信息
show_help() {
    echo "AI数字人对话系统测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  all         运行所有测试 (默认)"
    echo "  backend     仅运行后端测试"
    echo "  frontend    仅运行前端测试"
    echo "  integration 运行集成测试"
    echo "  performance 运行性能测试"
    echo "  lint        仅运行代码检查"
    echo "  coverage    仅运行覆盖率测试"
    echo "  clean       清理测试环境"
    echo ""
    echo "选项:"
    echo "  --no-db     不启动测试数据库"
    echo "  --verbose   显示详细输出"
    echo ""
    echo "示例:"
    echo "  $0 all"
    echo "  $0 backend --verbose"
    echo "  $0 integration"
}

# 主函数
main() {
    local command="all"
    local start_db=true
    local verbose=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            all|backend|frontend|integration|performance|lint|coverage|clean|help)
                command=$1
                shift
                ;;
            --no-db)
                start_db=false
                shift
                ;;
            --verbose)
                verbose=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置详细输出
    if [ "$verbose" = true ]; then
        set -x
    fi
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行命令
    case $command in
        "all")
            check_dependencies
            setup_test_env
            if [ "$start_db" = true ]; then
                start_test_db
            fi
            run_backend_tests
            run_frontend_tests
            run_integration_tests
            generate_test_report
            ;;
        "backend")
            check_dependencies
            setup_test_env
            if [ "$start_db" = true ]; then
                start_test_db
            fi
            run_backend_tests
            ;;
        "frontend")
            check_dependencies
            setup_test_env
            run_frontend_tests
            ;;
        "integration")
            check_dependencies
            setup_test_env
            run_integration_tests
            ;;
        "performance")
            check_dependencies
            setup_test_env
            run_performance_tests
            ;;
        "lint")
            check_dependencies
            cd backend && npm run lint && npm run type-check && cd ..
            cd frontend && npm run lint && npm run type-check && cd ..
            ;;
        "coverage")
            check_dependencies
            setup_test_env
            if [ "$start_db" = true ]; then
                start_test_db
            fi
            cd backend && npm run test:coverage && cd ..
            cd frontend && npm run test:coverage && cd ..
            generate_test_report
            ;;
        "clean")
            cleanup
            ;;
        "help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
    
    log_success "测试完成！"
}

# 执行主函数
main "$@"
