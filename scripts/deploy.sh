#!/bin/bash

# AI数字人对话系统部署脚本
# 使用方法: ./scripts/deploy.sh [环境] [选项]
# 环境: development, staging, production
# 选项: --build, --no-cache, --logs

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 检查环境变量
check_env() {
    local env_file=".env"
    
    if [ ! -f "$env_file" ]; then
        log_warning "环境变量文件 $env_file 不存在，从示例文件复制..."
        cp .env.example $env_file
        log_warning "请编辑 $env_file 文件，配置必要的环境变量"
        return 1
    fi
    
    # 检查关键环境变量
    local required_vars=(
        "JWT_SECRET"
        "ENCRYPTION_KEY"
        "MONGODB_URI"
        "REDIS_URL"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$env_file" || grep -q "^$var=$" "$env_file" || grep -q "^$var=your-" "$env_file"; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "以下环境变量未配置或使用默认值："
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        log_error "请在 $env_file 中配置这些变量"
        return 1
    fi
    
    log_success "环境变量检查完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    if [ "$NO_CACHE" = true ]; then
        docker-compose build --no-cache
    else
        docker-compose build
    fi
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    local env=$1
    
    log_info "启动服务 (环境: $env)..."
    
    # 根据环境选择不同的配置
    case $env in
        "development")
            docker-compose up -d mongodb redis
            log_info "等待数据库启动..."
            sleep 10
            docker-compose up -d backend frontend
            ;;
        "staging")
            docker-compose up -d mongodb redis
            log_info "等待数据库启动..."
            sleep 10
            docker-compose up -d backend frontend
            ;;
        "production")
            docker-compose --profile production up -d
            ;;
        *)
            log_error "未知环境: $env"
            exit 1
            ;;
    esac
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "健康检查尝试 $attempt/$max_attempts"
        
        # 检查后端服务
        if curl -f http://localhost:3001/health &> /dev/null; then
            log_success "后端服务健康检查通过"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "健康检查失败，服务可能未正常启动"
            return 1
        fi
        
        sleep 10
        ((attempt++))
    done
    
    # 检查前端服务
    if curl -f http://localhost:3000 &> /dev/null; then
        log_success "前端服务健康检查通过"
    else
        log_warning "前端服务健康检查失败"
    fi
    
    log_success "健康检查完成"
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    
    echo ""
    log_info "服务访问地址:"
    echo "  前端: http://localhost:3000"
    echo "  后端: http://localhost:3001"
    echo "  MongoDB: mongodb://localhost:27017"
    echo "  Redis: redis://localhost:6379"
    echo "  MinIO: http://localhost:9001"
}

# 显示日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs -f
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 清理资源
cleanup() {
    log_info "清理Docker资源..."
    
    # 停止并删除容器
    docker-compose down --remove-orphans
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除未使用的网络
    docker network prune -f
    
    log_success "清理完成"
}

# 备份数据
backup_data() {
    local backup_dir="./backups/$(date +%Y%m%d_%H%M%S)"
    
    log_info "备份数据到 $backup_dir..."
    
    mkdir -p "$backup_dir"
    
    # 备份MongoDB
    docker-compose exec -T mongodb mongodump --archive > "$backup_dir/mongodb.archive"
    
    # 备份Redis
    docker-compose exec -T redis redis-cli --rdb - > "$backup_dir/redis.rdb"
    
    # 备份上传文件
    if [ -d "./uploads" ]; then
        cp -r ./uploads "$backup_dir/"
    fi
    
    log_success "数据备份完成: $backup_dir"
}

# 恢复数据
restore_data() {
    local backup_dir=$1
    
    if [ -z "$backup_dir" ] || [ ! -d "$backup_dir" ]; then
        log_error "请指定有效的备份目录"
        exit 1
    fi
    
    log_info "从 $backup_dir 恢复数据..."
    
    # 恢复MongoDB
    if [ -f "$backup_dir/mongodb.archive" ]; then
        docker-compose exec -T mongodb mongorestore --archive < "$backup_dir/mongodb.archive"
    fi
    
    # 恢复上传文件
    if [ -d "$backup_dir/uploads" ]; then
        cp -r "$backup_dir/uploads" ./
    fi
    
    log_success "数据恢复完成"
}

# 显示帮助信息
show_help() {
    echo "AI数字人对话系统部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令] [环境] [选项]"
    echo ""
    echo "命令:"
    echo "  deploy      部署应用 (默认)"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      显示服务状态"
    echo "  logs        显示服务日志"
    echo "  build       构建镜像"
    echo "  cleanup     清理Docker资源"
    echo "  backup      备份数据"
    echo "  restore     恢复数据"
    echo "  health      健康检查"
    echo ""
    echo "环境:"
    echo "  development 开发环境 (默认)"
    echo "  staging     测试环境"
    echo "  production  生产环境"
    echo ""
    echo "选项:"
    echo "  --build     强制重新构建镜像"
    echo "  --no-cache  构建时不使用缓存"
    echo "  --logs      部署后显示日志"
    echo ""
    echo "示例:"
    echo "  $0 deploy development --build"
    echo "  $0 start production"
    echo "  $0 backup"
    echo "  $0 restore ./backups/20231201_120000"
}

# 主函数
main() {
    local command="deploy"
    local environment="development"
    local build_flag=false
    local no_cache_flag=false
    local show_logs_flag=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            deploy|start|stop|restart|status|logs|build|cleanup|backup|restore|health|help)
                command=$1
                shift
                ;;
            development|staging|production)
                environment=$1
                shift
                ;;
            --build)
                build_flag=true
                shift
                ;;
            --no-cache)
                no_cache_flag=true
                shift
                ;;
            --logs)
                show_logs_flag=true
                shift
                ;;
            *)
                if [ "$command" = "restore" ] && [ -z "$2" ]; then
                    backup_dir=$1
                    shift
                else
                    log_error "未知参数: $1"
                    show_help
                    exit 1
                fi
                ;;
        esac
    done
    
    # 设置全局变量
    BUILD=$build_flag
    NO_CACHE=$no_cache_flag
    
    # 执行命令
    case $command in
        "deploy")
            check_dependencies
            if ! check_env; then
                exit 1
            fi
            if [ "$BUILD" = true ]; then
                build_images
            fi
            start_services "$environment"
            health_check
            show_status
            if [ "$show_logs_flag" = true ]; then
                show_logs
            fi
            ;;
        "start")
            start_services "$environment"
            show_status
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            start_services "$environment"
            show_status
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs
            ;;
        "build")
            build_images
            ;;
        "cleanup")
            cleanup
            ;;
        "backup")
            backup_data
            ;;
        "restore")
            restore_data "$backup_dir"
            ;;
        "health")
            health_check
            ;;
        "help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
