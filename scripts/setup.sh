#!/bin/bash

# AI数字人定制应用系统 - 项目初始化脚本
# 此脚本用于快速设置开发环境

set -e

echo "🚀 开始初始化 AI数字人定制应用系统..."

# 检查Node.js版本
check_node_version() {
    echo "📋 检查Node.js版本..."
    if ! command -v node &> /dev/null; then
        echo "❌ 错误: 未找到Node.js，请先安装Node.js 20.x或更高版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 20 ]; then
        echo "❌ 错误: Node.js版本过低，当前版本: $(node -v)，需要20.x或更高版本"
        exit 1
    fi
    
    echo "✅ Node.js版本检查通过: $(node -v)"
}

# 检查npm版本
check_npm_version() {
    echo "📋 检查npm版本..."
    if ! command -v npm &> /dev/null; then
        echo "❌ 错误: 未找到npm"
        exit 1
    fi
    
    echo "✅ npm版本: $(npm -v)"
}

# 检查Docker (可选)
check_docker() {
    echo "📋 检查Docker..."
    if command -v docker &> /dev/null; then
        echo "✅ Docker已安装: $(docker --version)"
        if command -v docker-compose &> /dev/null; then
            echo "✅ Docker Compose已安装: $(docker-compose --version)"
        else
            echo "⚠️  警告: 未找到docker-compose，建议安装以便使用Docker开发环境"
        fi
    else
        echo "⚠️  警告: 未找到Docker，建议安装以便使用容器化开发环境"
    fi
}

# 创建环境变量文件
setup_env_files() {
    echo "📝 设置环境变量文件..."
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        echo "✅ 已创建根目录 .env 文件"
    else
        echo "ℹ️  根目录 .env 文件已存在"
    fi
    
    # 创建前端环境变量文件
    if [ ! -d "frontend" ]; then
        mkdir -p frontend
    fi
    
    if [ ! -f "frontend/.env" ]; then
        cat > frontend/.env << EOF
# 前端环境变量
VITE_API_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3001
VITE_APP_NAME=AI数字人定制平台
VITE_APP_VERSION=1.0.0
EOF
        echo "✅ 已创建前端 .env 文件"
    else
        echo "ℹ️  前端 .env 文件已存在"
    fi
    
    # 创建后端环境变量文件
    if [ ! -d "backend" ]; then
        mkdir -p backend
    fi
    
    if [ ! -f "backend/.env" ]; then
        cat > backend/.env << EOF
# 后端环境变量
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/ai-digital-robots
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secret-jwt-key-change-in-production-minimum-32-characters
JWT_EXPIRES_IN=7d

# AI模型配置 (请填入真实的API密钥)
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here

# 向量数据库配置
PINECONE_API_KEY=your-pinecone-api-key-here
PINECONE_ENVIRONMENT=your-pinecone-environment-here

# 文件存储配置
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
EOF
        echo "✅ 已创建后端 .env 文件"
    else
        echo "ℹ️  后端 .env 文件已存在"
    fi
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    
    # 安装根目录依赖
    echo "📦 安装根目录依赖..."
    npm install
    
    # 检查并创建前端项目
    if [ ! -f "frontend/package.json" ]; then
        echo "🔧 创建前端项目..."
        mkdir -p frontend
        cd frontend
        npm create vite@latest . -- --template react-ts
        cd ..
    fi
    
    # 安装前端依赖
    if [ -f "frontend/package.json" ]; then
        echo "📦 安装前端依赖..."
        cd frontend
        npm install
        cd ..
    fi
    
    # 检查并创建后端项目
    if [ ! -f "backend/package.json" ]; then
        echo "🔧 创建后端项目..."
        mkdir -p backend
        cd backend
        npm init -y
        cd ..
    fi
    
    # 安装后端依赖
    if [ -f "backend/package.json" ]; then
        echo "📦 安装后端依赖..."
        cd backend
        npm install
        cd ..
    fi
}

# 创建必要的目录
create_directories() {
    echo "📁 创建必要的目录..."
    
    # 创建上传目录
    mkdir -p uploads/{documents,images,temp}
    
    # 创建日志目录
    mkdir -p logs
    
    # 创建数据目录
    mkdir -p data/{mongodb,redis,minio}
    
    # 创建SSL证书目录
    mkdir -p nginx/ssl
    
    # 创建脚本目录
    mkdir -p scripts
    
    echo "✅ 目录创建完成"
}

# 设置Git钩子
setup_git_hooks() {
    echo "🔧 设置Git钩子..."
    
    if [ -d ".git" ]; then
        # 安装husky
        npx husky install
        
        # 创建pre-commit钩子
        npx husky add .husky/pre-commit "npm run lint-staged"
        
        echo "✅ Git钩子设置完成"
    else
        echo "⚠️  警告: 不是Git仓库，跳过Git钩子设置"
    fi
}

# 显示下一步操作
show_next_steps() {
    echo ""
    echo "🎉 项目初始化完成！"
    echo ""
    echo "📋 下一步操作:"
    echo "1. 编辑 .env 文件，填入真实的API密钥"
    echo "2. 启动数据库服务 (MongoDB 和 Redis)"
    echo "3. 运行开发服务器:"
    echo "   npm run dev"
    echo ""
    echo "🐳 或者使用Docker:"
    echo "   docker-compose up -d"
    echo ""
    echo "📚 更多信息请查看 README.md 文件"
    echo ""
}

# 主函数
main() {
    check_node_version
    check_npm_version
    check_docker
    setup_env_files
    create_directories
    install_dependencies
    setup_git_hooks
    show_next_steps
}

# 运行主函数
main "$@"
