// MongoDB初始化脚本
// 此脚本在MongoDB容器启动时自动执行，用于创建数据库和初始用户

// 切换到目标数据库
db = db.getSiblingDB('ai-digital-robots');

// 创建应用用户
db.createUser({
  user: 'app_user',
  pwd: 'app_password_123',
  roles: [
    {
      role: 'readWrite',
      db: 'ai-digital-robots'
    }
  ]
});

// 创建集合并添加索引
print('创建用户集合和索引...');
db.createCollection('users');
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ username: 1 }, { unique: true });
db.users.createIndex({ createdAt: 1 });

print('创建数字人集合和索引...');
db.createCollection('characters');
db.characters.createIndex({ userId: 1 });
db.characters.createIndex({ isPublic: 1, tags: 1 });
db.characters.createIndex({ 'stats.lastUsedAt': -1 });
db.characters.createIndex({ createdAt: -1 });

print('创建对话集合和索引...');
db.createCollection('conversations');
db.conversations.createIndex({ userId: 1, createdAt: -1 });
db.conversations.createIndex({ characterId: 1 });
db.conversations.createIndex({ status: 1 });
db.conversations.createIndex({ updatedAt: -1 });

print('创建知识库集合和索引...');
db.createCollection('knowledges');
db.knowledges.createIndex({ userId: 1 });
db.knowledges.createIndex({ 'documents.status': 1 });
db.knowledges.createIndex({ createdAt: -1 });

// 插入示例数据 (仅开发环境)
if (db.adminCommand('ismaster').ismaster) {
  print('插入示例数据...');
  
  // 插入示例用户
  db.users.insertOne({
    _id: ObjectId(),
    username: 'demo_user',
    email: '<EMAIL>',
    passwordHash: '$2b$10$example.hash.for.demo.user.only',
    avatar: '',
    role: 'user',
    subscription: {
      plan: 'free',
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
      tokensUsed: 0,
      tokensLimit: 10000
    },
    preferences: {
      language: 'zh-CN',
      theme: 'light',
      notifications: {
        email: true,
        browser: true
      }
    },
    createdAt: new Date(),
    updatedAt: new Date()
  });
  
  // 获取示例用户ID
  const demoUser = db.users.findOne({ email: '<EMAIL>' });
  
  // 插入示例数字人
  db.characters.insertMany([
    {
      _id: ObjectId(),
      name: '智能客服助手',
      description: '专业的客服助手，能够回答常见问题并提供优质服务',
      avatar: '',
      userId: demoUser._id,
      config: {
        personality: '友善、专业、耐心',
        expertise: ['客户服务', '产品咨询', '技术支持'],
        language: 'zh-CN',
        voice: {
          provider: 'azure',
          voiceId: 'zh-CN-XiaoxiaoNeural',
          speed: 1.0,
          pitch: 1.0
        },
        aiModel: {
          provider: 'openai',
          model: 'gpt-4',
          temperature: 0.7,
          maxTokens: 2000,
          systemPrompt: '你是一个专业的客服助手，请用友善、耐心的态度回答用户的问题。'
        }
      },
      knowledgeBase: {
        enabled: false,
        vectorStoreId: '',
        documents: []
      },
      isPublic: false,
      tags: ['客服', '助手', '专业'],
      stats: {
        conversationCount: 0,
        messageCount: 0,
        lastUsedAt: new Date()
      },
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: ObjectId(),
      name: '技术专家',
      description: '精通各种技术问题的专家，能够提供技术咨询和解决方案',
      avatar: '',
      userId: demoUser._id,
      config: {
        personality: '严谨、专业、逻辑清晰',
        expertise: ['软件开发', '系统架构', '技术咨询'],
        language: 'zh-CN',
        voice: {
          provider: 'azure',
          voiceId: 'zh-CN-YunxiNeural',
          speed: 1.0,
          pitch: 1.0
        },
        aiModel: {
          provider: 'openai',
          model: 'gpt-4',
          temperature: 0.3,
          maxTokens: 3000,
          systemPrompt: '你是一个技术专家，请用专业、准确的语言回答技术相关问题。'
        }
      },
      knowledgeBase: {
        enabled: false,
        vectorStoreId: '',
        documents: []
      },
      isPublic: false,
      tags: ['技术', '专家', '开发'],
      stats: {
        conversationCount: 0,
        messageCount: 0,
        lastUsedAt: new Date()
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]);
  
  print('示例数据插入完成');
}

print('MongoDB初始化完成！');
