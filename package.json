{"name": "ai-digital-robots", "version": "1.0.0", "description": "AI数字人定制应用系统 - 支持RAG检索增强生成和多种大语言模型集成的现代化平台", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "test:e2e": "cd frontend && npm run test:e2e", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:fix": "npm run lint:fix:frontend && npm run lint:fix:backend", "lint:fix:frontend": "cd frontend && npm run lint:fix", "lint:fix:backend": "cd backend && npm run lint:fix", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules frontend/dist backend/dist", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "keywords": ["ai", "digital-human", "chatbot", "rag", "llm", "react", "nodejs", "express", "mongodb", "typescript"], "author": "AI Digital Robots Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/ai-digital-robots.git"}, "bugs": {"url": "https://github.com/your-org/ai-digital-robots/issues"}, "homepage": "https://github.com/your-org/ai-digital-robots#readme", "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"frontend/**/*.{js,jsx,ts,tsx}": ["cd frontend && npm run lint:fix", "git add"], "backend/**/*.{js,ts}": ["cd backend && npm run lint:fix", "git add"]}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}