# AI数字人定制应用系统

一个基于现代Web技术栈构建的AI数字人定制平台，支持RAG检索增强生成和多种大语言模型集成。

## 🚀 项目特性

- **🤖 AI数字人定制**: 创建个性化的AI数字人角色，支持多种性格和专业领域设定
- **💬 实时对话**: 基于WebSocket的实时对话系统，支持流式响应
- **📚 RAG知识库**: 支持文档上传、向量化存储和检索增强生成
- **🔌 多模型支持**: 集成OpenAI GPT、Anthropic Claude等主流大语言模型
- **🎨 现代化UI**: 基于React和Ant Design的响应式用户界面
- **🔐 安全可靠**: JWT认证、权限管理、数据加密等安全机制
- **📊 监控分析**: 完整的使用统计和性能监控

## 🏗️ 技术架构

### 前端技术栈
- **框架**: React 19.x + TypeScript
- **UI组件**: Ant Design 5.x
- **状态管理**: Zustand + React Query
- **路由**: React Router 7.x
- **实时通信**: Socket.io-client
- **构建工具**: Vite
- **样式**: Tailwind CSS

### 后端技术栈
- **运行环境**: Node.js 20.x
- **Web框架**: Express.js 5.x
- **数据库**: MongoDB 7.x + Redis 7.x
- **ORM**: Mongoose
- **认证**: JWT + Passport.js
- **实时通信**: Socket.io
- **文件存储**: MinIO (S3兼容)

### AI服务集成
- **大语言模型**: OpenAI GPT-4, Anthropic Claude
- **向量数据库**: Pinecone / Weaviate
- **文档处理**: LangChain
- **语音服务**: Azure Speech Services

## 📋 项目结构

```
ai-digital-robots/
├── docs/                    # 项目文档
│   ├── requirements.md      # 需求文档
│   ├── system-design.md     # 系统设计文档
│   └── development-roadmap.md # 开发路线图
├── frontend/                # 前端项目
│   ├── src/
│   │   ├── components/      # 通用组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── services/       # API服务
│   │   ├── stores/         # 状态管理
│   │   └── utils/          # 工具函数
│   ├── public/             # 静态资源
│   └── package.json
├── backend/                 # 后端项目
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── services/       # 业务服务
│   │   ├── models/         # 数据模型
│   │   ├── middleware/     # 中间件
│   │   ├── routes/         # 路由定义
│   │   └── utils/          # 工具函数
│   ├── tests/              # 测试文件
│   └── package.json
├── docker-compose.yml       # Docker编排文件
├── .github/                # GitHub Actions
└── README.md               # 项目说明
```

## 🚀 快速开始

### 环境要求

- Node.js 20.x+
- MongoDB 7.x+
- Redis 7.x+
- Docker & Docker Compose (可选)

### 本地开发

1. **克隆项目**
```bash
git clone https://github.com/your-org/ai-digital-robots.git
cd ai-digital-robots
```

2. **安装依赖**
```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd ../backend
npm install
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# 编辑配置文件，填入必要的API密钥和数据库连接信息
```

4. **启动服务**
```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端服务
cd frontend
npm run dev
```

5. **访问应用**
- 前端应用: http://localhost:5173
- 后端API: http://localhost:3000
- API文档: http://localhost:3000/api-docs

### Docker部署

```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📖 开发文档

- [需求文档](./docs/requirements.md) - 详细的功能需求和用户场景
- [系统设计文档](./docs/system-design.md) - 系统架构和技术设计
- [开发路线图](./docs/development-roadmap.md) - 开发计划和任务清单
- [API文档](http://localhost:3000/api-docs) - RESTful API接口文档

## 🧪 测试

```bash
# 运行后端测试
cd backend
npm test

# 运行前端测试
cd frontend
npm test

# 运行端到端测试
npm run test:e2e
```

## 📦 部署

### 生产环境部署

1. **构建项目**
```bash
# 构建前端
cd frontend
npm run build

# 构建后端
cd backend
npm run build
```

2. **Docker部署**
```bash
# 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 启动生产服务
docker-compose -f docker-compose.prod.yml up -d
```

3. **Kubernetes部署**
```bash
# 应用Kubernetes配置
kubectl apply -f k8s/
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [React](https://reactjs.org/) - 用户界面库
- [Express.js](https://expressjs.com/) - Web应用框架
- [MongoDB](https://www.mongodb.com/) - 文档数据库
- [OpenAI](https://openai.com/) - AI模型服务
- [Ant Design](https://ant.design/) - UI组件库

## 📞 联系我们

- 项目主页: https://github.com/your-org/ai-digital-robots
- 问题反馈: https://github.com/your-org/ai-digital-robots/issues
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！