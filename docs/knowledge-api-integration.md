# 知识库 API 集成说明文档

## 概述

本文档描述了 AI 数字人对话系统中知识库 API 集成功能的实现。该功能允许系统通过调用外部知识库 HTTP API 来获取知识库服务，并在 API 不可用时自动降级到通用大模型 API，确保服务的高可用性。

## 功能特性

### 1. 知识库 API 调用
- **标准 HTTP API 接口**：支持调用符合标准的知识库 HTTP API
- **灵活配置**：支持配置 API 端点、认证信息、超时设置等
- **自动重试**：内置指数退避重试机制，提高请求成功率
- **健康检查**：定期检查知识库服务健康状态

### 2. 配置管理
- **环境变量配置**：通过环境变量管理所有配置项
- **动态配置更新**：支持运行时更新配置
- **配置验证**：启动时自动验证配置的有效性

### 3. 降级策略
- **多种降级模式**：
  - `ai_model`：使用通用大模型 API
  - `cached_response`：使用缓存的历史响应
  - `empty_response`：返回友好的错误提示
- **自动切换**：当知识库 API 不可用时自动执行降级
- **智能恢复**：服务恢复后自动切换回知识库 API

### 4. 错误处理与监控
- **完善的错误处理**：区分可重试和不可重试错误
- **详细日志记录**：记录所有 API 调用和错误信息
- **性能监控**：统计响应时间、成功率等指标
- **降级统计**：跟踪降级触发次数和原因

### 5. 缓存机制
- **响应缓存**：缓存成功的 API 响应，提高性能
- **降级缓存**：缓存成功响应用于降级场景
- **智能过期**：基于内容类型设置不同的缓存时间

## 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端 API       │    │  知识库 API     │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ 用户界面    │ │───▶│ │ 知识库控制器 │ │───▶│ │ 外部知识库  │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
└─────────────────┘    │ ┌──────────────┐ │    └─────────────────┘
                       │ │ 增强知识库   │ │              │
                       │ │ 服务         │ │              │
                       │ └──────────────┘ │              │
                       │ ┌──────────────┐ │              │
                       │ │ API 客户端   │ │──────────────┘
                       │ └──────────────┘ │
                       │ ┌──────────────┐ │    ┌─────────────────┐
                       │ │ 降级策略     │ │───▶│   AI 模型 API   │
                       │ │ 服务         │ │    │                 │
                       │ └──────────────┘ │    └─────────────────┘
                       └──────────────────┘
```

## 配置说明

### 环境变量配置

#### 知识库 API 配置
```bash
# 启用知识库 API
KNOWLEDGE_API_ENABLED=true

# API 基础 URL
KNOWLEDGE_API_BASE_URL=https://api.knowledge-service.com

# API 认证信息
KNOWLEDGE_API_KEY=your-knowledge-api-key-here
KNOWLEDGE_API_SECRET=your-knowledge-api-secret-here

# 超时和重试配置
KNOWLEDGE_API_TIMEOUT=30000
KNOWLEDGE_API_RETRY_ATTEMPTS=3
KNOWLEDGE_API_RETRY_DELAY=1000
```

#### 降级策略配置
```bash
# 启用降级策略
KNOWLEDGE_FALLBACK_ENABLED=true

# 降级策略类型：ai_model | cached_response | empty_response
KNOWLEDGE_FALLBACK_STRATEGY=ai_model

# 缓存超时时间（秒）
KNOWLEDGE_FALLBACK_CACHE_TIMEOUT=86400

# AI 模型降级配置
FALLBACK_AI_PROVIDER=openai
FALLBACK_AI_MODEL=gpt-3.5-turbo
FALLBACK_AI_TEMPERATURE=0.7
FALLBACK_AI_MAX_TOKENS=1000
FALLBACK_AI_SYSTEM_PROMPT=你是一个智能助手，当知识库不可用时提供通用回答。
```

## API 接口

### 知识库搜索
```http
POST /api/knowledge/:id/search
Content-Type: application/json
Authorization: Bearer <token>

{
  "query": "用户查询内容",
  "topK": 5,
  "scoreThreshold": 0.7
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "content": "基于知识库的回答内容",
    "source": "knowledge_api",
    "confidence": 0.85,
    "results": [...],
    "sources": [...],
    "metadata": {
      "totalResults": 10,
      "processingTime": 150,
      "timestamp": "2024-01-01T00:00:00Z"
    }
  }
}
```

### 服务状态检查
```http
GET /api/knowledge/status
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "initialized": true,
    "apiEnabled": true,
    "apiHealthy": true,
    "fallbackEnabled": true,
    "fallbackStats": {
      "count": 5,
      "lastTime": 1640995200000,
      "strategy": "ai_model",
      "enabled": true
    },
    "config": {
      "api": {
        "enabled": true,
        "baseUrl": "https://api.knowledge-service.com",
        "hasApiKey": true,
        "timeout": 30000
      },
      "fallback": {
        "enabled": true,
        "strategy": "ai_model",
        "hasAiConfig": true
      }
    }
  }
}
```

### 健康检查
```http
GET /api/knowledge/health
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "details": {
      "api": {
        "available": true,
        "healthy": true
      },
      "fallback": {
        "enabled": true,
        "working": true
      }
    }
  }
}
```

## 使用示例

### 基本搜索
```typescript
import { enhancedKnowledgeService } from '@/services/enhancedKnowledgeService'

// 搜索知识库
const result = await enhancedKnowledgeService.searchKnowledge(
  "如何使用 AI 数字人？",
  {
    knowledgeBaseId: "kb_123",
    topK: 5,
    scoreThreshold: 0.7,
    userId: "user_456"
  }
)

console.log('搜索结果:', result.content)
console.log('数据源:', result.source)
console.log('置信度:', result.confidence)
```

### 服务状态监控
```typescript
// 获取服务状态
const status = enhancedKnowledgeService.getServiceStatus()
console.log('API 状态:', status.apiHealthy)
console.log('降级统计:', status.fallbackStats)

// 健康检查
const health = await enhancedKnowledgeService.healthCheck()
console.log('整体状态:', health.status)
```

### 配置管理
```typescript
import { knowledgeApiConfig } from '@/config/knowledgeApi'

// 更新 API 配置
knowledgeApiConfig.updateApiConfig({
  timeout: 60000,
  retryAttempts: 5
})

// 重新初始化服务
await enhancedKnowledgeService.reinitialize()
```

## 错误处理

### 错误类型
- **网络错误**：连接超时、网络不可达
- **认证错误**：API 密钥无效、权限不足
- **服务错误**：知识库服务内部错误
- **配置错误**：配置参数无效

### 降级触发条件
- 知识库 API 服务不可用（5xx 错误）
- 网络连接失败
- 请求超时
- 认证失败（在配置错误时）

### 错误日志示例
```
[2024-01-01 12:00:00] INFO: 知识库 API 搜索成功 {
  query: "用户查询",
  source: "knowledge_api",
  confidence: 0.85
}

[2024-01-01 12:01:00] WARN: 知识库 API 搜索失败，降级到 AI 模型 {
  query: "用户查询",
  error: "Connection timeout"
}

[2024-01-01 12:02:00] ERROR: 降级策略执行失败 {
  strategy: "ai_model",
  error: "AI provider unavailable"
}
```

## 性能优化

### 缓存策略
- **API 响应缓存**：30分钟
- **降级缓存**：24小时
- **健康检查缓存**：1分钟

### 并发控制
- 支持并发请求处理
- 连接池管理
- 请求队列限制

### 监控指标
- API 响应时间
- 成功率统计
- 降级触发频率
- 缓存命中率

## 部署注意事项

1. **环境变量配置**：确保所有必需的环境变量都已正确设置
2. **网络连通性**：确保服务器能够访问外部知识库 API
3. **认证信息**：妥善保管 API 密钥和认证信息
4. **监控告警**：设置适当的监控和告警机制
5. **日志管理**：配置日志轮转和存储策略

## 故障排除

### 常见问题

1. **知识库 API 连接失败**
   - 检查网络连通性
   - 验证 API 端点 URL
   - 确认认证信息正确

2. **降级策略不工作**
   - 检查降级配置
   - 验证 AI 模型配置
   - 查看错误日志

3. **响应时间过长**
   - 调整超时设置
   - 检查网络延迟
   - 优化缓存策略

### 调试命令
```bash
# 检查服务状态
curl http://localhost:3001/api/knowledge/status

# 健康检查
curl http://localhost:3001/api/knowledge/health

# 重新初始化服务
curl -X POST http://localhost:3001/api/knowledge/admin/reinitialize

# 重置降级统计
curl -X POST http://localhost:3001/api/knowledge/admin/reset-fallback-stats
```

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现知识库 API 调用功能
- 添加降级策略支持
- 完善错误处理和监控
- 添加缓存机制
