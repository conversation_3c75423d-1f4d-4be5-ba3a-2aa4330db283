# AI数字人对话系统 - 部署指南

## 概述

本文档提供了AI数字人对话系统的完整部署指南，包括开发环境、测试环境和生产环境的部署方法。

## 环境要求

### 基础要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+) / macOS / Windows
- **Node.js**: 18.0+ 
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 最少 4GB，推荐 8GB+
- **存储**: 最少 20GB 可用空间

### 生产环境额外要求
- **Kubernetes**: 1.24+ (可选)
- **负载均衡器**: Nginx / HAProxy
- **SSL证书**: Let's Encrypt 或商业证书
- **监控系统**: Prometheus + Grafana

## 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/yourusername/ai-digital-robots.git
cd ai-digital-robots
```

### 2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量
```

### 3. 启动开发环境
```bash
# 使用部署脚本
./scripts/deploy.sh deploy development

# 或手动启动
docker-compose up -d
```

### 4. 验证部署
```bash
# 检查服务状态
./scripts/deploy.sh status

# 访问应用
# 前端: http://localhost:3000
# 后端: http://localhost:3001
# API文档: http://localhost:3001/docs
```

## 环境配置详解

### 开发环境

开发环境用于本地开发和调试。

```bash
# 启动开发环境
./scripts/deploy.sh deploy development

# 查看日志
./scripts/deploy.sh logs

# 停止服务
./scripts/deploy.sh stop
```

**特点:**
- 热重载支持
- 详细的调试日志
- 开发工具集成
- 测试数据库

### 测试环境

测试环境用于自动化测试和集成测试。

```bash
# 运行所有测试
./scripts/test.sh all

# 仅运行后端测试
./scripts/test.sh backend

# 仅运行前端测试
./scripts/test.sh frontend

# 运行集成测试
./scripts/test.sh integration
```

**特点:**
- 独立的测试数据库
- 模拟外部服务
- 自动化测试流程
- 测试覆盖率报告

### 生产环境

生产环境用于正式部署和用户访问。

```bash
# 部署到生产环境
./scripts/deploy.sh deploy production

# 启动监控服务
docker-compose -f docker-compose.monitoring.yml up -d

# 健康检查
curl http://localhost:3001/health/detailed
```

**特点:**
- 高可用性配置
- 安全加固
- 性能优化
- 监控告警

## Docker部署

### 基础部署

```bash
# 构建镜像
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]

# 停止服务
docker-compose down
```

### 生产环境部署

```bash
# 使用生产配置
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 启动监控服务
docker-compose -f docker-compose.monitoring.yml up -d

# 配置反向代理
docker-compose --profile production up -d nginx
```

## Kubernetes部署

### 前置条件

1. 安装kubectl
2. 配置Kubernetes集群访问
3. 安装Helm (可选)

### 部署步骤

```bash
# 1. 创建命名空间
kubectl apply -f k8s/namespace.yaml

# 2. 创建配置和密钥
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml

# 3. 部署数据库服务
kubectl apply -f k8s/mongodb.yaml
kubectl apply -f k8s/redis.yaml

# 4. 等待数据库就绪
kubectl wait --for=condition=ready pod -l app=mongodb -n ai-chat --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n ai-chat --timeout=300s

# 5. 部署应用服务
kubectl apply -f k8s/backend.yaml
kubectl apply -f k8s/frontend.yaml

# 6. 配置入口
kubectl apply -f k8s/ingress.yaml

# 7. 验证部署
kubectl get pods -n ai-chat
kubectl get services -n ai-chat
```

### 扩容和更新

```bash
# 扩容后端服务
kubectl scale deployment backend --replicas=5 -n ai-chat

# 滚动更新
kubectl set image deployment/backend backend=ai-chat/backend:v2.0.0 -n ai-chat

# 查看部署状态
kubectl rollout status deployment/backend -n ai-chat

# 回滚部署
kubectl rollout undo deployment/backend -n ai-chat
```

## 监控和日志

### Prometheus监控

```bash
# 启动监控服务
docker-compose -f docker-compose.monitoring.yml up -d

# 访问监控面板
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3001 (admin/admin123)
# AlertManager: http://localhost:9093
```

### 日志管理

```bash
# 查看应用日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 查看系统日志
journalctl -u docker

# ELK日志分析
# Kibana: http://localhost:5601
```

### 健康检查

```bash
# 基础健康检查
curl http://localhost:3001/health

# 详细健康检查
curl http://localhost:3001/health/detailed

# 就绪检查
curl http://localhost:3001/ready

# 存活检查
curl http://localhost:3001/live
```

## 性能优化

### 数据库优化

```bash
# MongoDB索引优化
mongosh --eval "db.users.createIndex({email: 1}, {unique: true})"
mongosh --eval "db.characters.createIndex({userId: 1, createdAt: -1})"

# Redis内存优化
redis-cli CONFIG SET maxmemory 2gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

### 应用优化

```bash
# 启用Gzip压缩
# 配置CDN
# 启用缓存策略
# 数据库连接池优化
```

## 安全配置

### SSL/TLS配置

```bash
# 使用Let's Encrypt
certbot --nginx -d yourdomain.com

# 或使用自签名证书
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/key.pem -out ssl/cert.pem
```

### 防火墙配置

```bash
# 开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

### 安全加固

```bash
# 更新系统
apt update && apt upgrade -y

# 配置fail2ban
apt install fail2ban
systemctl enable fail2ban

# 禁用root登录
sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
systemctl restart ssh
```

## 备份和恢复

### 数据备份

```bash
# 备份数据
./scripts/deploy.sh backup

# 手动备份MongoDB
mongodump --uri="***************************************************" --out=backup/

# 备份Redis
redis-cli --rdb backup/redis.rdb

# 备份文件
tar -czf backup/uploads.tar.gz uploads/
```

### 数据恢复

```bash
# 恢复数据
./scripts/deploy.sh restore backup/20231201_120000

# 手动恢复MongoDB
mongorestore --uri="***************************************************" backup/

# 恢复Redis
redis-cli --rdb backup/redis.rdb

# 恢复文件
tar -xzf backup/uploads.tar.gz
```

## 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查日志
   docker-compose logs [service_name]
   
   # 检查端口占用
   netstat -tulpn | grep :3001
   
   # 检查磁盘空间
   df -h
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose ps mongodb
   
   # 测试连接
   mongosh "***************************************************"
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 清理Docker资源
   docker system prune -a
   ```

### 性能问题

1. **响应时间慢**
   - 检查数据库索引
   - 启用缓存
   - 优化查询语句

2. **高CPU使用率**
   - 检查进程状态
   - 优化算法逻辑
   - 增加服务实例

3. **内存泄漏**
   - 监控内存使用
   - 检查代码逻辑
   - 重启服务

## 维护指南

### 定期维护

```bash
# 每日检查
./scripts/deploy.sh health
docker-compose ps

# 每周维护
./scripts/deploy.sh backup
docker system prune

# 每月更新
apt update && apt upgrade
docker-compose pull
```

### 版本更新

```bash
# 1. 备份数据
./scripts/deploy.sh backup

# 2. 拉取新版本
git pull origin main

# 3. 更新配置
cp .env .env.backup
# 检查新的环境变量

# 4. 重新部署
./scripts/deploy.sh deploy production --build

# 5. 验证更新
./scripts/deploy.sh health
```

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 联系技术支持团队

---

**注意**: 在生产环境部署前，请务必：
- 修改所有默认密码
- 配置正确的域名和SSL证书
- 设置适当的资源限制
- 配置监控和告警
- 制定备份策略
