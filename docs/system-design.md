# AI数字人定制应用系统 - 系统设计文档

## 1. 系统架构设计

### 1.1 整体架构概述
本系统采用前后端分离的微服务架构，基于现代Web技术栈构建，确保系统的可扩展性、可维护性和高性能。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用层     │    │   API网关层     │    │   后端服务层     │
│                │    │                │    │                │
│  React.js      │◄──►│  Express.js    │◄──►│  微服务集群      │
│  TypeScript    │    │  中间件        │    │  Node.js       │
│  Ant Design    │    │  认证授权      │    │  MongoDB       │
│  WebSocket     │    │  负载均衡      │    │  Redis         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                              │
                                              ▼
                                    ┌─────────────────┐
                                    │   外部服务层     │
                                    │                │
                                    │  OpenAI API    │
                                    │  Claude API    │
                                    │  RAG服务       │
                                    │  TTS/STT服务   │
                                    └─────────────────┘
```

### 1.2 技术栈选择

#### 1.2.1 前端技术栈
- **框架**: React 19.x + TypeScript
- **UI组件库**: Ant Design 5.x
- **状态管理**: Zustand + React Query
- **路由**: React Router 7.x
- **实时通信**: Socket.io-client
- **构建工具**: Vite
- **样式**: Tailwind CSS + CSS Modules

#### 1.2.2 后端技术栈
- **运行环境**: Node.js 20.x
- **Web框架**: Express.js 5.x
- **数据库**: MongoDB 7.x (主数据库) + Redis 7.x (缓存)
- **ORM**: Mongoose
- **认证**: JWT + Passport.js
- **实时通信**: Socket.io
- **文件存储**: MinIO (S3兼容)
- **消息队列**: Bull Queue (基于Redis)

#### 1.2.3 AI服务集成
- **大语言模型**: OpenAI GPT-4, Anthropic Claude, 国产大模型
- **向量数据库**: Pinecone / Weaviate
- **文档处理**: LangChain
- **语音服务**: Azure Speech Services / 阿里云语音服务

### 1.3 系统分层架构

#### 1.3.1 表现层 (Presentation Layer)
```typescript
// 组件层次结构
src/
├── components/          // 通用组件
│   ├── common/         // 基础组件
│   ├── forms/          // 表单组件
│   └── layout/         // 布局组件
├── pages/              // 页面组件
│   ├── dashboard/      // 仪表板
│   ├── characters/     // 数字人管理
│   ├── chat/          // 对话界面
│   └── settings/      // 系统设置
├── hooks/              // 自定义Hooks
├── services/           // API服务
├── stores/             // 状态管理
└── utils/              // 工具函数
```

#### 1.3.2 业务逻辑层 (Business Logic Layer)
```javascript
// 服务模块结构
src/
├── controllers/        // 控制器层
│   ├── auth.js        // 认证控制器
│   ├── character.js   // 数字人控制器
│   ├── chat.js        // 对话控制器
│   └── knowledge.js   // 知识库控制器
├── services/          // 业务服务层
│   ├── aiService.js   // AI模型服务
│   ├── ragService.js  // RAG服务
│   ├── ttsService.js  // 语音合成服务
│   └── userService.js // 用户服务
├── middleware/        // 中间件
│   ├── auth.js       // 认证中间件
│   ├── validation.js // 数据验证
│   └── rateLimit.js  // 限流中间件
└── utils/            // 工具函数
```

#### 1.3.3 数据访问层 (Data Access Layer)
```javascript
// 数据模型结构
src/
├── models/            // 数据模型
│   ├── User.js       // 用户模型
│   ├── Character.js  // 数字人模型
│   ├── Conversation.js // 对话模型
│   └── Knowledge.js  // 知识库模型
├── repositories/      // 数据仓库
│   ├── userRepo.js   // 用户数据仓库
│   ├── characterRepo.js // 数字人数据仓库
│   └── chatRepo.js   // 对话数据仓库
└── database/         // 数据库配置
    ├── mongodb.js    // MongoDB连接
    └── redis.js      // Redis连接
```

## 2. 数据库设计

### 2.1 MongoDB 数据模型

#### 2.1.1 用户模型 (User)
```javascript
const userSchema = {
  _id: ObjectId,
  username: String,        // 用户名
  email: String,          // 邮箱
  passwordHash: String,   // 密码哈希
  avatar: String,         // 头像URL
  role: String,           // 用户角色 (admin, user)
  subscription: {         // 订阅信息
    plan: String,         // 订阅计划
    expiresAt: Date,      // 过期时间
    tokensUsed: Number,   // 已使用token数
    tokensLimit: Number   // token限制
  },
  preferences: {          // 用户偏好
    language: String,     // 界面语言
    theme: String,        // 主题设置
    notifications: Object // 通知设置
  },
  createdAt: Date,
  updatedAt: Date
}
```

#### 2.1.2 数字人模型 (Character)
```javascript
const characterSchema = {
  _id: ObjectId,
  name: String,           // 数字人名称
  description: String,    // 描述
  avatar: String,         // 头像URL
  userId: ObjectId,       // 创建者ID
  config: {
    personality: String,  // 性格设定
    expertise: [String],  // 专业领域
    language: String,     // 主要语言
    voice: {             // 语音设置
      provider: String,   // 语音服务提供商
      voiceId: String,   // 语音ID
      speed: Number,     // 语速
      pitch: Number      // 音调
    },
    aiModel: {           // AI模型配置
      provider: String,   // 模型提供商
      model: String,     // 模型名称
      temperature: Number, // 温度参数
      maxTokens: Number,  // 最大token数
      systemPrompt: String // 系统提示词
    }
  },
  knowledgeBase: {        // 知识库配置
    enabled: Boolean,     // 是否启用RAG
    vectorStoreId: String, // 向量存储ID
    documents: [{         // 文档列表
      id: String,
      name: String,
      type: String,
      uploadedAt: Date
    }]
  },
  isPublic: Boolean,      // 是否公开
  tags: [String],         // 标签
  stats: {               // 统计信息
    conversationCount: Number,
    messageCount: Number,
    lastUsedAt: Date
  },
  createdAt: Date,
  updatedAt: Date
}
```

#### 2.1.3 对话模型 (Conversation)
```javascript
const conversationSchema = {
  _id: ObjectId,
  userId: ObjectId,       // 用户ID
  characterId: ObjectId,  // 数字人ID
  title: String,          // 对话标题
  messages: [{            // 消息列表
    id: String,
    role: String,         // user, assistant, system
    content: String,      // 消息内容
    timestamp: Date,      // 时间戳
    metadata: {           // 元数据
      tokens: Number,     // token消耗
      model: String,      // 使用的模型
      ragUsed: Boolean,   // 是否使用RAG
      sources: [String]   // RAG来源文档
    }
  }],
  status: String,         // active, archived, deleted
  createdAt: Date,
  updatedAt: Date
}
```

#### 2.1.4 知识库模型 (Knowledge)
```javascript
const knowledgeSchema = {
  _id: ObjectId,
  name: String,           // 知识库名称
  description: String,    // 描述
  userId: ObjectId,       // 创建者ID
  documents: [{           // 文档列表
    id: String,
    name: String,
    originalName: String,
    type: String,         // pdf, docx, txt, md
    size: Number,         // 文件大小
    url: String,          // 文件URL
    status: String,       // processing, completed, failed
    chunks: Number,       // 分块数量
    uploadedAt: Date,
    processedAt: Date
  }],
  vectorStore: {          // 向量存储配置
    provider: String,     // pinecone, weaviate
    indexId: String,      // 索引ID
    dimensions: Number,   // 向量维度
    totalVectors: Number  // 向量总数
  },
  settings: {             // 设置
    chunkSize: Number,    // 分块大小
    chunkOverlap: Number, // 分块重叠
    embeddingModel: String // 嵌入模型
  },
  createdAt: Date,
  updatedAt: Date
}
```

### 2.2 Redis 缓存设计

#### 2.2.1 缓存策略
```javascript
// 缓存键命名规范
const cacheKeys = {
  user: 'user:{userId}',                    // 用户信息缓存
  character: 'character:{characterId}',     // 数字人信息缓存
  conversation: 'conv:{conversationId}',    // 对话缓存
  aiResponse: 'ai:{hash}',                  // AI响应缓存
  rateLimit: 'rate:{userId}:{endpoint}',    // 限流缓存
  session: 'session:{sessionId}',           // 会话缓存
  knowledgeIndex: 'kb:{knowledgeId}:index' // 知识库索引缓存
}

// 缓存过期时间配置
const cacheTTL = {
  user: 3600,           // 1小时
  character: 1800,      // 30分钟
  conversation: 900,    // 15分钟
  aiResponse: 86400,    // 24小时
  rateLimit: 3600,      // 1小时
  session: 7200,        // 2小时
  knowledgeIndex: 3600  // 1小时
}
```

## 3. API设计

### 3.1 RESTful API 规范

#### 3.1.1 API 基础结构
```
Base URL: https://api.ai-digital-robots.com/v1
Authentication: Bearer Token (JWT)
Content-Type: application/json
```

#### 3.1.2 响应格式标准
```javascript
// 成功响应格式
{
  "success": true,
  "data": {}, // 响应数据
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}

// 错误响应格式
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": {}
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 3.1.3 核心API端点

##### 认证相关API
```javascript
POST   /auth/register          // 用户注册
POST   /auth/login             // 用户登录
POST   /auth/logout            // 用户登出
POST   /auth/refresh           // 刷新token
GET    /auth/profile           // 获取用户信息
PUT    /auth/profile           // 更新用户信息
```

##### 数字人管理API
```javascript
GET    /characters             // 获取数字人列表
POST   /characters             // 创建数字人
GET    /characters/:id         // 获取数字人详情
PUT    /characters/:id         // 更新数字人
DELETE /characters/:id         // 删除数字人
POST   /characters/:id/clone   // 克隆数字人
```

##### 对话相关API
```javascript
GET    /conversations          // 获取对话列表
POST   /conversations          // 创建新对话
GET    /conversations/:id      // 获取对话详情
DELETE /conversations/:id      // 删除对话
POST   /conversations/:id/messages // 发送消息
```

##### 知识库管理API
```javascript
GET    /knowledge              // 获取知识库列表
POST   /knowledge              // 创建知识库
GET    /knowledge/:id          // 获取知识库详情
PUT    /knowledge/:id          // 更新知识库
DELETE /knowledge/:id          // 删除知识库
POST   /knowledge/:id/documents // 上传文档
DELETE /knowledge/:id/documents/:docId // 删除文档
```

### 3.2 WebSocket API设计

#### 3.2.1 实时对话接口
```javascript
// 连接建立
socket.emit('join_conversation', {
  conversationId: 'conv_123',
  characterId: 'char_456'
})

// 发送消息
socket.emit('send_message', {
  conversationId: 'conv_123',
  content: '你好，请介绍一下自己',
  type: 'text'
})

// 接收消息
socket.on('message_received', {
  messageId: 'msg_789',
  role: 'assistant',
  content: '你好！我是...',
  timestamp: '2024-01-01T00:00:00Z',
  metadata: {
    tokens: 50,
    model: 'gpt-4',
    ragUsed: true
  }
})

// 流式响应
socket.on('message_stream', {
  messageId: 'msg_789',
  chunk: '你好',
  isComplete: false
})
```

## 4. 用户界面设计

### 4.1 设计原则
- **简洁直观**: 界面简洁，操作直观，降低学习成本
- **响应式设计**: 适配桌面端、平板和移动端
- **无障碍访问**: 支持键盘导航和屏幕阅读器
- **国际化支持**: 支持多语言切换

### 4.2 主要页面设计

#### 4.2.1 仪表板页面
```
┌─────────────────────────────────────────────────────────┐
│  导航栏: Logo | 数字人 | 对话 | 知识库 | 设置 | 用户头像  │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │  数字人数量  │  │  对话次数    │  │  Token使用   │      │
│  │     12      │  │    1,234    │  │   45,678    │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
│                                                         │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              最近对话列表                            │ │
│  │  ○ 与客服助手的对话 - 2小时前                        │ │
│  │  ○ 技术支持咨询 - 1天前                             │ │
│  │  ○ 产品介绍对话 - 3天前                             │ │
│  └─────────────────────────────────────────────────────┘ │
│                                                         │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              快速操作                                │ │
│  │  [创建数字人] [开始对话] [上传文档] [查看统计]        │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

#### 4.2.2 数字人管理页面
```
┌─────────────────────────────────────────────────────────┐
│  数字人管理                    [+ 创建新数字人] [导入模板] │
├─────────────────────────────────────────────────────────┤
│  搜索: [_______________] 筛选: [全部▼] [标签▼] [排序▼]   │
│                                                         │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐        │
│  │ [头像]  │ │ [头像]  │ │ [头像]  │ │ [头像]  │        │
│  │客服助手 │ │技术专家 │ │销售顾问 │ │教育导师 │        │
│  │在线     │ │离线     │ │在线     │ │在线     │        │
│  │[编辑]   │ │[编辑]   │ │[编辑]   │ │[编辑]   │        │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘        │
│                                                         │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐        │
│  │ [头像]  │ │ [头像]  │ │ [头像]  │ │   [+]   │        │
│  │产品经理 │ │法律顾问 │ │健康助手 │ │ 添加新的 │        │
│  │离线     │ │在线     │ │在线     │ │ 数字人   │        │
│  │[编辑]   │ │[编辑]   │ │[编辑]   │ │         │        │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘        │
└─────────────────────────────────────────────────────────┘
```

#### 4.2.3 对话界面
```
┌─────────────────────────────────────────────────────────┐
│  ← 返回  客服助手  ●在线  [设置] [分享] [清空对话]        │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────┐ │
│  │                   对话区域                          │ │
│  │                                                     │ │
│  │  用户: 你好，请问如何使用这个系统？                   │ │
│  │  [10:30]                                           │ │
│  │                                                     │ │
│  │      助手: 您好！我是客服助手，很高兴为您服务。      │ │
│  │      这个系统主要用于创建和管理AI数字人...          │ │
│  │      [10:30] [🔊] [📋]                             │ │
│  │                                                     │ │
│  │  用户: 能详细介绍一下创建数字人的步骤吗？             │ │
│  │  [10:32]                                           │ │
│  │                                                     │ │
│  │      助手: 当然可以！创建数字人主要包括以下步骤：    │ │
│  │      1. 基础信息设置...                             │ │
│  │      [正在输入...] ⏸️                              │ │
│  └─────────────────────────────────────────────────────┘ │
│                                                         │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ [📎] [输入您的消息...                    ] [🎤] [📤] │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 4.3 组件设计规范

#### 4.3.1 设计系统
```typescript
// 颜色系统
const colors = {
  primary: '#1890ff',      // 主色调
  success: '#52c41a',      // 成功色
  warning: '#faad14',      // 警告色
  error: '#f5222d',        // 错误色
  text: {
    primary: '#262626',    // 主要文本
    secondary: '#8c8c8c',  // 次要文本
    disabled: '#bfbfbf'    // 禁用文本
  },
  background: {
    default: '#ffffff',    // 默认背景
    secondary: '#fafafa',  // 次要背景
    disabled: '#f5f5f5'    // 禁用背景
  }
}

// 字体系统
const typography = {
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
  fontSize: {
    xs: '12px',
    sm: '14px',
    base: '16px',
    lg: '18px',
    xl: '20px',
    '2xl': '24px',
    '3xl': '30px'
  },
  fontWeight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700
  }
}

// 间距系统
const spacing = {
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  '2xl': '48px',
  '3xl': '64px'
}
```

#### 4.3.2 通用组件库
```typescript
// 按钮组件
interface ButtonProps {
  type?: 'primary' | 'secondary' | 'danger' | 'ghost'
  size?: 'small' | 'medium' | 'large'
  loading?: boolean
  disabled?: boolean
  icon?: ReactNode
  onClick?: () => void
  children: ReactNode
}

// 输入框组件
interface InputProps {
  type?: 'text' | 'password' | 'email' | 'number'
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  error?: string
  disabled?: boolean
  prefix?: ReactNode
  suffix?: ReactNode
}

// 卡片组件
interface CardProps {
  title?: string
  extra?: ReactNode
  bordered?: boolean
  hoverable?: boolean
  loading?: boolean
  children: ReactNode
}
```

## 5. 安全设计

### 5.1 认证与授权

#### 5.1.1 JWT认证机制
```javascript
// JWT Token结构
const tokenPayload = {
  userId: 'user_123',
  email: '<EMAIL>',
  role: 'user',
  permissions: ['chat:create', 'character:manage'],
  iat: 1640995200,  // 签发时间
  exp: 1641081600   // 过期时间
}

// 认证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ error: '访问令牌缺失' })
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ error: '令牌无效' })
    req.user = user
    next()
  })
}
```

#### 5.1.2 基于角色的访问控制 (RBAC)
```javascript
// 权限定义
const permissions = {
  'user': [
    'character:read',
    'character:create',
    'character:update:own',
    'chat:create',
    'chat:read:own',
    'knowledge:read:own',
    'knowledge:create'
  ],
  'admin': [
    'character:*',
    'chat:*',
    'knowledge:*',
    'user:*',
    'system:*'
  ]
}

// 权限检查中间件
const requirePermission = (permission) => {
  return (req, res, next) => {
    const userPermissions = req.user.permissions || []

    if (!userPermissions.includes(permission) &&
        !userPermissions.includes(permission.split(':')[0] + ':*')) {
      return res.status(403).json({ error: '权限不足' })
    }

    next()
  }
}
```

### 5.2 数据安全

#### 5.2.1 数据加密
```javascript
// 敏感数据加密
const crypto = require('crypto')

class DataEncryption {
  constructor() {
    this.algorithm = 'aes-256-gcm'
    this.secretKey = process.env.ENCRYPTION_KEY
  }

  encrypt(text) {
    const iv = crypto.randomBytes(16)
    const cipher = crypto.createCipher(this.algorithm, this.secretKey)
    cipher.setAAD(Buffer.from('additional-data'))

    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')

    const authTag = cipher.getAuthTag()

    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    }
  }

  decrypt(encryptedData) {
    const decipher = crypto.createDecipher(this.algorithm, this.secretKey)
    decipher.setAAD(Buffer.from('additional-data'))
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'))

    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  }
}
```

#### 5.2.2 输入验证与清理
```javascript
// 使用Joi进行数据验证
const Joi = require('joi')

const characterSchema = Joi.object({
  name: Joi.string().min(1).max(50).required(),
  description: Joi.string().max(500),
  config: Joi.object({
    personality: Joi.string().max(1000),
    expertise: Joi.array().items(Joi.string().max(50)).max(10),
    language: Joi.string().valid('zh-CN', 'en-US', 'ja-JP'),
    aiModel: Joi.object({
      provider: Joi.string().valid('openai', 'anthropic', 'local'),
      model: Joi.string().required(),
      temperature: Joi.number().min(0).max(2),
      maxTokens: Joi.number().min(1).max(4000)
    })
  })
})

// XSS防护
const xss = require('xss')

const sanitizeInput = (input) => {
  if (typeof input === 'string') {
    return xss(input, {
      whiteList: {},  // 不允许任何HTML标签
      stripIgnoreTag: true,
      stripIgnoreTagBody: ['script']
    })
  }
  return input
}
```

### 5.3 API安全

#### 5.3.1 限流机制
```javascript
// 使用express-rate-limit实现限流
const rateLimit = require('express-rate-limit')

// 通用限流配置
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    error: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
})

// AI API限流配置
const aiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 20, // 限制每个IP 1分钟内最多20个AI请求
  message: {
    error: 'AI请求过于频繁，请稍后再试'
  }
})

// 基于用户的限流
const userBasedLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: (req) => {
    // 根据用户订阅计划设置不同限制
    const userPlan = req.user?.subscription?.plan || 'free'
    const limits = {
      'free': 50,
      'pro': 500,
      'enterprise': 5000
    }
    return limits[userPlan] || 50
  },
  keyGenerator: (req) => req.user?.userId || req.ip
})
```

#### 5.3.2 CORS配置
```javascript
// CORS安全配置
const cors = require('cors')

const corsOptions = {
  origin: (origin, callback) => {
    const allowedOrigins = [
      'https://ai-digital-robots.com',
      'https://app.ai-digital-robots.com',
      'http://localhost:3000', // 开发环境
      'http://localhost:5173'  // Vite开发服务器
    ]

    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true)
    } else {
      callback(new Error('不允许的CORS来源'))
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  maxAge: 86400 // 预检请求缓存时间
}
```

## 6. 性能优化

### 6.1 前端性能优化

#### 6.1.1 代码分割与懒加载
```typescript
// 路由级别的代码分割
import { lazy, Suspense } from 'react'
import { Routes, Route } from 'react-router-dom'

// 懒加载页面组件
const Dashboard = lazy(() => import('../pages/Dashboard'))
const Characters = lazy(() => import('../pages/Characters'))
const Chat = lazy(() => import('../pages/Chat'))
const Knowledge = lazy(() => import('../pages/Knowledge'))

// 加载中组件
const LoadingSpinner = () => (
  <div className="flex justify-center items-center h-64">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
  </div>
)

// 路由配置
const AppRoutes = () => (
  <Suspense fallback={<LoadingSpinner />}>
    <Routes>
      <Route path="/dashboard" element={<Dashboard />} />
      <Route path="/characters" element={<Characters />} />
      <Route path="/chat" element={<Chat />} />
      <Route path="/knowledge" element={<Knowledge />} />
    </Routes>
  </Suspense>
)
```

#### 6.1.2 状态管理优化
```typescript
// 使用Zustand进行状态管理
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

interface AppState {
  user: User | null
  characters: Character[]
  currentConversation: Conversation | null
  loading: boolean
  error: string | null
}

interface AppActions {
  setUser: (user: User | null) => void
  setCharacters: (characters: Character[]) => void
  setCurrentConversation: (conversation: Conversation | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

// 创建store
const useAppStore = create<AppState & AppActions>()(
  subscribeWithSelector((set, get) => ({
    // 状态
    user: null,
    characters: [],
    currentConversation: null,
    loading: false,
    error: null,

    // 操作
    setUser: (user) => set({ user }),
    setCharacters: (characters) => set({ characters }),
    setCurrentConversation: (conversation) => set({ currentConversation: conversation }),
    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error })
  }))
)

// 选择器优化
const useUser = () => useAppStore((state) => state.user)
const useCharacters = () => useAppStore((state) => state.characters)
```

#### 6.1.3 缓存策略
```typescript
// 使用React Query进行数据缓存
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

// 查询配置
const queryConfig = {
  staleTime: 5 * 60 * 1000,    // 5分钟内数据被认为是新鲜的
  cacheTime: 10 * 60 * 1000,   // 10分钟后清除缓存
  retry: 3,                     // 失败重试3次
  retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
}

// 获取数字人列表
const useCharacters = () => {
  return useQuery({
    queryKey: ['characters'],
    queryFn: () => api.getCharacters(),
    ...queryConfig
  })
}

// 获取对话历史
const useConversation = (conversationId: string) => {
  return useQuery({
    queryKey: ['conversation', conversationId],
    queryFn: () => api.getConversation(conversationId),
    enabled: !!conversationId,
    ...queryConfig
  })
}

// 发送消息mutation
const useSendMessage = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { conversationId: string; content: string }) =>
      api.sendMessage(data),
    onSuccess: (response, variables) => {
      // 更新对话缓存
      queryClient.setQueryData(
        ['conversation', variables.conversationId],
        (oldData: any) => ({
          ...oldData,
          messages: [...oldData.messages, response.data]
        })
      )
    }
  })
}
```

### 6.2 后端性能优化

#### 6.2.1 数据库优化
```javascript
// MongoDB索引优化
const createIndexes = async () => {
  const db = mongoose.connection.db

  // 用户集合索引
  await db.collection('users').createIndex({ email: 1 }, { unique: true })
  await db.collection('users').createIndex({ username: 1 }, { unique: true })

  // 数字人集合索引
  await db.collection('characters').createIndex({ userId: 1 })
  await db.collection('characters').createIndex({ isPublic: 1, tags: 1 })
  await db.collection('characters').createIndex({ 'stats.lastUsedAt': -1 })

  // 对话集合索引
  await db.collection('conversations').createIndex({ userId: 1, createdAt: -1 })
  await db.collection('conversations').createIndex({ characterId: 1 })
  await db.collection('conversations').createIndex({ status: 1 })

  // 知识库集合索引
  await db.collection('knowledges').createIndex({ userId: 1 })
  await db.collection('knowledges').createIndex({ 'documents.status': 1 })
}

// 查询优化
class CharacterRepository {
  async findByUserId(userId, options = {}) {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = -1,
      tags = []
    } = options

    const query = { userId }
    if (tags.length > 0) {
      query.tags = { $in: tags }
    }

    const skip = (page - 1) * limit

    return await Character
      .find(query)
      .select('-config.aiModel.apiKey') // 排除敏感字段
      .sort({ [sortBy]: sortOrder })
      .skip(skip)
      .limit(limit)
      .lean() // 返回普通JavaScript对象，提高性能
  }
}
```

#### 6.2.2 缓存层设计
```javascript
// Redis缓存服务
class CacheService {
  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST,
      port: process.env.REDIS_PORT,
      password: process.env.REDIS_PASSWORD,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3
    })
  }

  // 获取缓存
  async get(key) {
    try {
      const value = await this.redis.get(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('缓存获取失败:', error)
      return null
    }
  }

  // 设置缓存
  async set(key, value, ttl = 3600) {
    try {
      await this.redis.setex(key, ttl, JSON.stringify(value))
      return true
    } catch (error) {
      console.error('缓存设置失败:', error)
      return false
    }
  }

  // 删除缓存
  async del(key) {
    try {
      await this.redis.del(key)
      return true
    } catch (error) {
      console.error('缓存删除失败:', error)
      return false
    }
  }

  // 批量删除缓存
  async delPattern(pattern) {
    try {
      const keys = await this.redis.keys(pattern)
      if (keys.length > 0) {
        await this.redis.del(...keys)
      }
      return true
    } catch (error) {
      console.error('批量缓存删除失败:', error)
      return false
    }
  }
}

// 缓存装饰器
const cache = (ttl = 3600) => {
  return (target, propertyName, descriptor) => {
    const method = descriptor.value

    descriptor.value = async function(...args) {
      const cacheKey = `${target.constructor.name}:${propertyName}:${JSON.stringify(args)}`

      // 尝试从缓存获取
      let result = await cacheService.get(cacheKey)
      if (result !== null) {
        return result
      }

      // 执行原方法
      result = await method.apply(this, args)

      // 设置缓存
      await cacheService.set(cacheKey, result, ttl)

      return result
    }
  }
}

// 使用缓存装饰器
class CharacterService {
  @cache(1800) // 缓存30分钟
  async getCharacterById(id) {
    return await Character.findById(id)
  }

  @cache(900) // 缓存15分钟
  async getCharactersByUserId(userId) {
    return await Character.find({ userId })
  }
}
```

## 7. 部署架构

### 7.1 容器化部署

#### 7.1.1 Docker配置
```dockerfile
# 前端Dockerfile
FROM node:20-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```dockerfile
# 后端Dockerfile
FROM node:20-alpine

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码
COPY . .

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 更改文件所有权
RUN chown -R nodejs:nodejs /app
USER nodejs

EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

CMD ["node", "server.js"]
```

#### 7.1.2 Docker Compose配置
```yaml
version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - app-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/ai-digital-robots
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mongodb
      - redis
    networks:
      - app-network
    volumes:
      - ./uploads:/app/uploads

  # MongoDB数据库
  mongodb:
    image: mongo:7
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - app-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - app-network

volumes:
  mongodb_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### 7.2 云部署方案

#### 7.2.1 Kubernetes部署
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-digital-robots-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-digital-robots-backend
  template:
    metadata:
      labels:
        app: ai-digital-robots-backend
    spec:
      containers:
      - name: backend
        image: ai-digital-robots/backend:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: mongodb-uri
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: redis-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: ai-digital-robots-backend-service
spec:
  selector:
    app: ai-digital-robots-backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
```

#### 7.2.2 CI/CD流水线
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Run linting
      run: npm run lint

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Build Docker images
      run: |
        docker build -t ${{ secrets.DOCKER_REGISTRY }}/ai-digital-robots/frontend:${{ github.sha }} ./frontend
        docker build -t ${{ secrets.DOCKER_REGISTRY }}/ai-digital-robots/backend:${{ github.sha }} ./backend

    - name: Push to registry
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login ${{ secrets.DOCKER_REGISTRY }} -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push ${{ secrets.DOCKER_REGISTRY }}/ai-digital-robots/frontend:${{ github.sha }}
        docker push ${{ secrets.DOCKER_REGISTRY }}/ai-digital-robots/backend:${{ github.sha }}

    - name: Deploy to Kubernetes
      uses: azure/k8s-deploy@v1
      with:
        manifests: |
          k8s/deployment.yaml
          k8s/service.yaml
        images: |
          ${{ secrets.DOCKER_REGISTRY }}/ai-digital-robots/backend:${{ github.sha }}
        kubeconfig: ${{ secrets.KUBE_CONFIG }}
```

## 8. 监控与日志

### 8.1 应用监控

#### 8.1.1 健康检查端点
```javascript
// 健康检查路由
app.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version,
    checks: {}
  }

  try {
    // 检查数据库连接
    await mongoose.connection.db.admin().ping()
    health.checks.database = 'ok'
  } catch (error) {
    health.checks.database = 'error'
    health.status = 'error'
  }

  try {
    // 检查Redis连接
    await redis.ping()
    health.checks.redis = 'ok'
  } catch (error) {
    health.checks.redis = 'error'
    health.status = 'error'
  }

  const statusCode = health.status === 'ok' ? 200 : 503
  res.status(statusCode).json(health)
})

// 就绪检查端点
app.get('/ready', (req, res) => {
  res.status(200).json({
    status: 'ready',
    timestamp: new Date().toISOString()
  })
})
```

#### 8.1.2 性能指标收集
```javascript
// 使用prom-client收集指标
const promClient = require('prom-client')

// 创建指标收集器
const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'HTTP请求持续时间',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.5, 1, 2, 5]
})

const httpRequestTotal = new promClient.Counter({
  name: 'http_requests_total',
  help: 'HTTP请求总数',
  labelNames: ['method', 'route', 'status_code']
})

const aiRequestDuration = new promClient.Histogram({
  name: 'ai_request_duration_seconds',
  help: 'AI请求持续时间',
  labelNames: ['provider', 'model'],
  buckets: [1, 5, 10, 30, 60]
})

// 中间件收集HTTP指标
const metricsMiddleware = (req, res, next) => {
  const start = Date.now()

  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000
    const route = req.route?.path || req.path

    httpRequestDuration
      .labels(req.method, route, res.statusCode)
      .observe(duration)

    httpRequestTotal
      .labels(req.method, route, res.statusCode)
      .inc()
  })

  next()
}

// 指标端点
app.get('/metrics', async (req, res) => {
  res.set('Content-Type', promClient.register.contentType)
  res.end(await promClient.register.metrics())
})
```

### 8.2 日志管理

#### 8.2.1 结构化日志
```javascript
// 使用winston进行日志管理
const winston = require('winston')

// 日志格式配置
const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      ...meta
    })
  })
)

// 创建logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: {
    service: 'ai-digital-robots',
    version: process.env.npm_package_version
  },
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),

    // 文件输出
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error'
    }),
    new winston.transports.File({
      filename: 'logs/combined.log'
    })
  ]
})

// 请求日志中间件
const requestLogger = (req, res, next) => {
  const start = Date.now()

  res.on('finish', () => {
    const duration = Date.now() - start

    logger.info('HTTP请求', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.userId
    })
  })

  next()
}

// AI请求日志
const logAIRequest = (provider, model, prompt, response, duration) => {
  logger.info('AI请求', {
    provider,
    model,
    promptLength: prompt.length,
    responseLength: response.length,
    duration,
    tokens: response.usage?.total_tokens
  })
}
```

这个系统设计文档现在包含了完整的安全设计、性能优化、部署架构和监控日志等关键内容。接下来我将创建功能清单TODO列表。
