apiVersion: v1
kind: Secret
metadata:
  name: ai-chat-secrets
  namespace: ai-chat
type: Opaque
stringData:
  # JWT配置
  JWT_SECRET: "your-super-secret-jwt-key-change-in-production"
  
  # 加密配置
  ENCRYPTION_KEY: "your-encryption-key-change-in-production"
  
  # 数据库配置
  MONGO_ROOT_USERNAME: "admin"
  MONGO_ROOT_PASSWORD: "password123"
  MONGODB_URI: "************************************************************************************"
  
  # Redis配置
  REDIS_PASSWORD: "redis123"
  REDIS_URL: "redis://:redis123@redis-service:6379"
  
  # AI服务配置
  OPENAI_API_KEY: "your-openai-api-key"
  OPENAI_ORG_ID: "your-openai-org-id"
  ANTHROPIC_API_KEY: "your-anthropic-api-key"
  
  # 向量存储配置
  PINECONE_API_KEY: "your-pinecone-api-key"
  PINECONE_ENVIRONMENT: "us-east1-gcp"
  
  # 邮件服务配置
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  SMTP_USER: "<EMAIL>"
  SMTP_PASS: "your-app-password"
  SMTP_FROM: "AI数字人对话系统 <<EMAIL>>"
  
  # 语音服务配置
  AZURE_SPEECH_KEY: "your-azure-speech-key"
  AZURE_SPEECH_REGION: "eastus"
  
  # 阿里云语音
  ALIYUN_ACCESS_KEY_ID: "your-aliyun-access-key-id"
  ALIYUN_ACCESS_KEY_SECRET: "your-aliyun-access-key-secret"
  ALIYUN_REGION: "cn-hangzhou"
