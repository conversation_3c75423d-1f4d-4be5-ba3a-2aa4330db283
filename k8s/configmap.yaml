apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-chat-config
  namespace: ai-chat
data:
  NODE_ENV: "production"
  APP_NAME: "AI数字人对话系统"
  APP_URL: "https://ai-chat.yourdomain.com"
  
  # 数据库配置
  MONGO_DATABASE: "ai_digital_robots"
  
  # Redis配置
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  
  # JWT配置
  JWT_EXPIRES_IN: "7d"
  
  # 文件上传配置
  UPLOAD_DIR: "/app/uploads"
  MAX_FILE_SIZE: "52428800"
  ALLOWED_FILE_TYPES: "pdf,docx,txt,md,rtf"
  
  # 缓存配置
  CACHE_TTL: "3600"
  CACHE_MAX_SIZE: "100"
  
  # 性能配置
  MAX_CONCURRENT_REQUESTS: "10"
  REQUEST_TIMEOUT: "30000"
  
  # 监控配置
  ENABLE_METRICS: "true"
  METRICS_PORT: "9090"
  
  # 功能开关
  FEATURE_VOICE_CHAT: "true"
  FEATURE_KNOWLEDGE_BASE: "true"
  FEATURE_PUBLIC_CHARACTERS: "true"
  
  # 知识库 API 配置
  KNOWLEDGE_API_ENABLED: "true"
  KNOWLEDGE_API_TIMEOUT: "30000"
  KNOWLEDGE_API_RETRY_ATTEMPTS: "3"
  KNOWLEDGE_API_RETRY_DELAY: "1000"

  # 默认配置
  DEFAULT_LANGUAGE: "zh-CN"
  DEFAULT_THEME: "light"
  DEFAULT_AI_MODEL: "gpt-3.5-turbo"
  
  # 系统限制
  MAX_CHARACTERS_PER_USER: "10"
  MAX_CONVERSATIONS_PER_USER: "100"
  MAX_KNOWLEDGE_BASES_PER_USER: "5"
  MAX_DOCUMENTS_PER_KNOWLEDGE_BASE: "100"
